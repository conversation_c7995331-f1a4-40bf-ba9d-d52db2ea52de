{"name": "todo-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@re-dev/react-truncate": "^0.5.0", "chart.js": "^4.4.7", "firebase": "^9.22.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^4.8.0", "react-markdown": "^10.1.0", "react-router": "^7.0.2", "react-router-dom": "^7.0.2", "react-tailwindcss-datepicker": "^1.7.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "genkit-cli": "^1.2.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^4.3.2"}}