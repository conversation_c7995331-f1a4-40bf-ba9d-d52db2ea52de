[debug] [2025-07-15T14:00:54.831Z] ----------------------------------------------------------------------
[debug] [2025-07-15T14:00:54.833Z] Command:       /Users/<USER>/.nvm/versions/node/v22.2.0/bin/node /Users/<USER>/.nvm/versions/node/v22.2.0/bin/firebase emulators:start --only functions,database
[debug] [2025-07-15T14:00:54.833Z] CLI Version:   14.2.0
[debug] [2025-07-15T14:00:54.833Z] Platform:      darwin
[debug] [2025-07-15T14:00:54.833Z] Node Version:  v22.2.0
[debug] [2025-07-15T14:00:54.833Z] Time:          Tue Jul 15 2025 15:00:54 GMT+0100 (British Summer Time)
[debug] [2025-07-15T14:00:54.833Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-15T14:00:54.835Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-15T14:00:54.974Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T14:00:54.974Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-15T14:00:55.239Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-15T14:00:55.239Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-07-15T14:00:55.372Z] openjdk version "11.0.17" 2022-10-18
[debug] [2025-07-15T14:00:55.372Z] 
OpenJDK Runtime Environment Temurin-11.0.17+8 (build 11.0.17+8)
OpenJDK 64-Bit Server VM Temurin-11.0.17+8 (build 11.0.17+8, mixed mode)

[debug] [2025-07-15T14:00:55.375Z] Parsed Java major version: 11
[info] i  emulators: Starting emulators: functions, database {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, database"}}
[debug] [2025-07-15T14:00:55.725Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] ⚠  ui: Emulator UI unable to start on port 4000, starting on 4001 instead. {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI unable to start on port 4000, starting on 4001 instead."}}
[debug] [2025-07-15T14:00:55.725Z] assigned listening specs for emulators {"user":{"database":[{"address":"127.0.0.1","family":"IPv4","port":9000}],"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4001},{"address":"::1","family":"IPv6","port":4001}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-15T14:00:55.728Z] [hub] writing locator at /var/folders/m2/jhy7fgbd05b3cvxs4fn46q_80000gn/T/hub-mood-rings-beta.json
[debug] [2025-07-15T14:00:55.947Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T14:00:55.948Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-15T14:00:55.948Z] late-assigned ports for functions and eventarc emulators {"user":{"database":[{"address":"127.0.0.1","family":"IPv4","port":9000}],"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4001},{"address":"::1","family":"IPv6","port":4001}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, hosting, pubsub, storage, dataconnect"}}
