{"traceId": "b0177423a5d4ae594519e0a1ef6b201f", "spans": {"b94cb026c4dbd725": {"spanId": "b94cb026c4dbd725", "traceId": "b0177423a5d4ae594519e0a1ef6b201f", "parentSpanId": "98a273653946efb0", "startTime": 1752608230805, "endTime": 1752608233807.494, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "googleai/gemini-1.5-flash", "genkit:path": "/{generate,t:util}/{googleai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}", "genkit:output": "{\"candidates\":[{\"index\":0,\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user's mood score and areas of concern are not enough context to make a recommendation.  Additional information is needed.\\n\"}]},\"finishReason\":\"stop\",\"custom\":{}}],\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user's mood score and areas of concern are not enough context to make a recommendation.  Additional information is needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.2201993893354367}],\"usageMetadata\":{\"promptTokenCount\":51,\"candidatesTokenCount\":39,\"totalTokenCount\":90,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":51}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":39}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"5q12aLGrOumWsbQP_9XzqA4\"},\"usage\":{\"inputCharacters\":204,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":205,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":51,\"outputTokens\":39,\"totalTokens\":90},\"latencyMs\":3002.0261250000003}", "genkit:state": "success"}, "displayName": "googleai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "98a273653946efb0": {"spanId": "98a273653946efb0", "traceId": "b0177423a5d4ae594519e0a1ef6b201f", "startTime": 1752608230758, "endTime": 1752608233861.3147, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"googleai/gemini-1.5-flash\",\"docs\":[],\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"config\":{}}", "genkit:output": "{\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user's mood score and areas of concern are not enough context to make a recommendation.  Additional information is needed.\\n\"}]},\"finishReason\":\"stop\",\"usage\":{\"inputCharacters\":204,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":205,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":51,\"outputTokens\":39,\"totalTokens\":90},\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user's mood score and areas of concern are not enough context to make a recommendation.  Additional information is needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.2201993893354367}],\"usageMetadata\":{\"promptTokenCount\":51,\"candidatesTokenCount\":39,\"totalTokenCount\":90,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":51}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":39}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"5q12aLGrOumWsbQP_9XzqA4\"},\"request\":{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}}", "genkit:state": "success"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generate", "startTime": 1752608230758, "endTime": 1752608233861.3147}