{"traceId": "4a5d82e564e283c73c730025fd319d4b", "spans": {"a77b56cce9a0b767": {"spanId": "a77b56cce9a0b767", "traceId": "4a5d82e564e283c73c730025fd319d4b", "parentSpanId": "f5af832ccd4c6f7e", "startTime": 1752607997164, "endTime": 1752607997569.2014, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: finances, work life\\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.02558554,0.022754813,-0.038571395,-0.013210694,0.026975757,0.046439797,0.0755397,0.009021418,-0.019828718,0.02188033,-0.0017759742,0.060051106,0.010568618,0.012834866,-0.021321895,-0.04419292,-0.036057014,0.0490373,-0.054453716,-0.048469804,-0.012695618,-0.0069719693,-0.041981064,-0.018589612,-0.025267167,-0.00006759384,-0.0023186863,-0.014509946,0.036857218,0.01172378,0.019358901,0.03923518,0.041613914,-0.026095446,0.03318034,0.0330239,-0.03425025,0.011257555,-0.012097616,-0.091608725,-0.023741597,-0.0020227223,0.013895872,0.060482018,-0.01567684,-0.025248477,-0.03321903,-0.00019265733,-0.04673334,0.016547618,0.026542585,0.016780058,-0.028995395,0.053658146,-0.07020004,-0.030800842,0.011929937,0.005287968,0.040407225,-0.014951764,0.036569867,-0.018323401,-0.0032405634,-0.07129747,0.09243435,-0.026178893,0.04441577,0.04953486,-0.06299179,0.04399176,-0.0035417893,-0.013330483,-0.08790331,0.008562441,-0.042716354,-0.053281646,0.006572088,-0.015180759,0.027311733,-0.027540592,-0.043329723,-0.024131121,0.0063034673,0.07489057,-0.0058845566,0.04212521,-0.011761232,-0.05336403,-0.047384344,-0.021040741,0.04150405,0.04154416,0.0034625973,-0.0303394,0.07140357,0.011031665,-0.064584605,-0.014990382,0.05305382,0.034484204,-0.0054843337,0.037160482,0.009054173,-0.019206675,0.04311,0.073263,-0.034694593,-0.032166976,-0.0024054814,0.051690023,0.014867081,-0.020072917,0.051118307,-0.04740155,0.000022420874,0.046193276,0.032776754,0.010588908,0.04651016,-0.031199837,-0.021092828,0.023212628,-0.043161828,0.116299644,0.065168746,-0.011556121,0.018924063,-0.04474691,0.001663376,-0.035835322,0.064249955,-0.040483773,-0.030312499,0.032410048,-0.027803997,0.015809506,0.036727794,-0.055348672,-0.009401896,0.04859065,-0.046885587,-0.008288484,-0.0647622,0.035069164,-0.01699667,-0.043476846,0.009267472,0.07009567,-0.014113591,0.052517008,-0.09568127,0.04141665,0.030031586,0.06406939,-0.04757376,0.0050308877,0.021832095,-0.04476209,0.021815972,0.01204103,-0.007825265,-0.07275549,-0.028104289,0.019721262,-0.03203778,-0.04589196,-0.033698227,-0.04692785,0.021830779,0.008885123,-0.014046907,-0.018436728,-0.10758473,0.04249473,-0.009633795,0.019274596,-0.03482561,-0.0028024283,-0.03980953,-0.01918325,0.074130334,0.011152131,-0.023589231,-0.08476459,-0.011114307,0.0090961335,-0.050717883,0.011238298,-0.017142937,-0.016522883,0.03744857,-0.0067113074,0.039615657,-0.009499956,-0.04410704,0.037240304,0.064497136,-0.046738043,0.029160688,-0.041971155,0.0077435626,0.0063527664,-0.010821528,-0.012051767,-0.04377638,-0.01367425,-0.026439475,0.0069643,-0.025448427,0.0724406,-0.045208216,-0.029498963,-0.0011666872,-0.106136315,-0.04309544,0.050200783,-0.00853691,-0.023278378,0.051166024,-0.064498395,0.010043627,0.024852192,0.0033538719,-0.0010916758,0.034034856,0.012714565,-0.072375305,0.013682382,-0.05344493,-0.04439185,-0.010148703,0.040088788,0.012254003,-0.03563651,0.008101553,0.038213056,0.023323689,-0.027664566,-0.017557655,-0.026090268,-0.051947247,0.042970873,-0.027217148,-0.026332982,0.029183427,0.02039198,0.07665175,0.023224596,0.012234089,-0.019555788,0.035687797,-0.027144214,-0.091252975,0.008661252,-0.0034395133,0.005812042,0.007818628,0.0060491455,-0.031777136,-0.0005496863,0.049278397,-0.018329406,-0.041363332,-0.0076237293,0.00009560573,-0.052612066,-0.029002475,0.002516036,0.047862407,-0.040674046,-0.008832314,-0.037023168,-0.05109631,-0.024319842,-0.02540155,0.0061739413,-0.022643026,0.049026623,-0.047540512,0.027285023,0.050947785,-0.0027256848,0.046489656,-0.029149652,-0.01856605,-0.057725925,-0.006493661,0.003754034,-0.031175304,-0.042382665,0.049558286,0.01856828,-0.051835965,-0.049386967,0.018848313,-0.01826676,0.049713712,0.062561765,0.006136385,-0.0025665604,-0.022445515,0.013959905,-0.02862822,0.06168737,-0.046047304,0.039384473,-0.017751027,0.0061544133,-0.045439728,0.03917696,0.023740623,-0.03179909,0.010179864,0.020279404,-0.036903877,-0.017613357,-0.07408308,0.0016782038,0.0026620273,-0.037486583,0.042922482,-0.00066326227,-0.037462868,0.002594368,0.0477554,-0.006896085,0.03125741,0.010982775,-0.010689857,0.045830123,0.022870863,-0.020323826,0.0018414867,-0.08572301,0.028090248,0.0042433166,-0.022260845,0.06347702,0.08327391,0.005517526,0.04602674,-0.02759249,0.056713335,-0.014894718,0.0036065849,-0.016686698,-0.0641941,-0.007454266,0.038290884,0.0057407008,0.058563937,0.07402495,-0.0024446875,0.002295181,-0.00051354367,0.001284789,0.06507617,-0.0150149325,0.03686951,0.020620262,-0.0136690885,0.019726299,0.0039747735,0.04175483,-0.030854195,-0.007930078,0.073894985,-0.014929957,0.01738683,-0.0033744848,0.017847316,0.0124385115,0.07800428,-0.00456543,0.02755898,-0.008930146,-0.008809644,0.03250979,0.030238608,-0.0524082,0.022900963,-0.034125976,-0.04254415,0.039909396,-0.029985368,0.055824872,-0.050268304,0.03297206,-0.0067119375,0.027841818,-0.014676636,0.041340094,0.040752232,-0.008452282,-0.0029237347,0.010930683,-0.0009203595,0.026519021,-0.030872414,-0.029448146,0.007931286,-0.00055675715,0.06586584,0.003171407,0.037154622,-0.02346118,0.08035307,-0.020163514,0.0095984945,0.06276776,0.014175237,-0.044655442,-0.05860566,0.031126136,-0.010530512,0.00960428,0.0317331,-0.011091381,0.014377925,0.00016538022,0.008212691,0.037512112,0.052953064,0.0040403293,0.008640211,-0.02842323,-0.012849847,-0.024780313,-0.018096026,-0.014327494,0.026692789,-0.01792773,-0.05482628,0.016984044,-0.009064444,0.06058585,0.00066628266,-0.031838633,0.015733892,0.025750564,0.024593437,-0.030129181,0.019266756,-0.03562727,-0.016951807,-0.014938229,0.02172,-0.025052436,-0.030705117,0.005167411,0.028750738,-0.040836185,-0.014659322,-0.058736894,-0.08533365,0.020009775,-0.024062661,-0.011580372,0.0148484055,-0.009771471,-0.06442351,-0.016137011,0.03231042,0.005480757,-0.045961272,-0.0010730438,-0.06430419,0.014912438,0.013170497,-0.031553812,0.02503199,0.054625615,0.029924242,0.020803778,-0.0830569,-0.012729622,0.05103906,-0.015354172,-0.03119324,-0.028585497,-0.08473313,-0.018973919,-0.030988446,0.053612676,0.04196114,0.00065292476,-0.048159264,0.025069825,0.0053245444,-0.057430625,-0.0040234965,0.057491694,-0.059185363,0.0052568326,0.011176554,-0.023832407,0.03278414,0.0050538066,0.019949535,0.06944258,0.0034820389,0.06500145,0.0013482737,-0.03561892,-0.05223857,-0.024340497,-0.036056593,0.024513107,0.020580484,0.02804067,0.006468555,-0.009938347,-0.006934229,-0.03646329,-0.046454903,0.008767907,-0.020005729,0.037865143,-0.012154254,0.053683966,0.0021606463,-0.032622676,0.012241442,0.02052688,0.026051851,-0.017795501,-0.016851094,-0.015931478,0.008754585,-0.07619151,-0.023757441,0.06516622,0.055241,-0.002664319,0.02976557,0.051704627,0.047533255,0.020826703,0.009905226,-0.027399143,0.03185147,0.012777785,0.026615458,0.023230987,0.0046507074,-0.012184309,-0.0031246727,0.08645315,0.027808009,0.036134034,-0.0043950332,0.015866436,-0.00039717118,0.010419979,-0.0091641955,0.036694404,0.024147997,0.008328948,-0.027463606,-0.028008692,0.015247299,-0.0045400495,-0.0048775,-0.004097901,-0.04793306,-0.043659635,-0.054635737,0.011404567,-0.024271546,0.023756905,0.000037382586,-0.05566577,0.029795714,-0.059338752,0.0016549052,-0.06405574,-0.012941102,-0.03771138,0.020581352,0.009843214,-0.00680299,-0.00076110096,0.052004363,0.020188747,0.041026745,-0.029227402,0.021222746,-0.029812237,0.018661363,-0.036118366,0.03423069,-0.017357856,-0.012636128,-0.03716787,0.06575255,0.030871725,-0.032448135,0.01255547,-0.002541742,0.015357139,0.04347577,-0.0026265488,0.048462402,0.0059003294,-0.027006723,-0.06892219,-0.010489498,-0.04206836,0.010736805,0.000554244,-0.013218863,0.021499593,-0.028413149,-0.03930595,-0.072299294,0.027853524,0.009874889,-0.051421925,-0.026392706,0.053712156,-0.027341694,0.03468699,0.00065277505,-0.037176013,0.016318275,0.021105304,-0.012967121,0.008375875,0.04690923,0.0060829837,-0.021670593,-0.019629184,0.005241752,-0.026657365,0.006763168,-0.0073301205,0.00089941075,-0.013433511,-0.029954933,-0.022308739,-0.032019906,-0.022186698,0.027304512,0.00017277147,-0.072004266,0.031432096,-0.07822281,-0.064883955,0.05389899,0.037295282,0.053993758,0.060142335,-0.04447825,0.03440808,-0.07423331,-0.008707182,0.03373543,-0.024554035,0.0033385365,0.0057753236,-0.005911797,-0.07761414,0.032256808,0.037203208,-0.08097127,-0.01909321,-0.030046467,0.022398273,0.004362228,-0.046522632,0.024639249,-0.036153473,-0.009456484,0.022132214,0.0017229659,-0.009633736,0.031187944,0.044007134,-0.00039365992,-0.0067366576,0.0003662723,-0.018811204,0.03585696,-0.008180635,0.018445816,0.0152638955,0.058828056,-0.0023334094,0.0391942,0.00078879384,-0.021532755,-0.043797743,0.0024686225,0.004390947,-0.031079559,0.07170887,-0.020603567,0.02980074,-0.041807145,-0.023048852,0.0077129086,-0.041666556,-0.010665489,-0.0036225482,0.07009418,-0.002677959,0.03921939,-0.0038059168,-0.063848615,0.022989912,-0.018107558,0.004581241,0.08261548,-0.023157846,0.020963458,0.016961856,0.031992234,0.0032046384,0.062824704,-0.025122076,0.0014351028,-0.020133628,0.06526404,0.023252843,0.052282255,-0.0037342717,-0.024366619,0.022613468,-0.03027525,0.010946375,0.020361071,0.034275305,-0.06339623,0.02013094,0.025671303,-0.03815592,-0.043218724,0.020378536,-0.02608954,0.012557967,0.023005309,-0.04349429,-0.06091509,-0.010892044,-0.036687963,0.019135837,-0.023428326,-0.012131374,0.0045258803,0.022661535,-0.07134397,-0.052114446,0.0022052876,0.0029212078,0.023464058,-0.040094018,0.037431393,0.02412499,0.02344398,0.009385718,-0.09012664,-0.0117423795,0.014648247,-0.025670378,-0.06664612,-0.009155121,0.046914272,-0.019451538]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "f5af832ccd4c6f7e": {"spanId": "f5af832ccd4c6f7e", "traceId": "4a5d82e564e283c73c730025fd319d4b", "startTime": 1752607997156, "endTime": 1752607998607.8464, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: finances, work life\\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"session_type\":\"no_preference\",\"working_hours\":\"flexible\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752607997577.7722, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752607997636.2747, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752607998607.6072, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752607997156, "endTime": 1752607998607.8464}