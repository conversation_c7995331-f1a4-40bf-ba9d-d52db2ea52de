{"traceId": "ae8304e601ea500403524158a4ee474c", "spans": {"fa8930ba5bcef20b": {"spanId": "fa8930ba5bcef20b", "traceId": "ae8304e601ea500403524158a4ee474c", "parentSpanId": "70eee280ad5b487f", "startTime": 1752603166857, "endTime": 1752603167187.1365, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: work life, finances\\n      Additional information: I am looking to work with a professional coach, somebody with extensive experience working with executives and leadership level. I also prefer to work with a woman in this role\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.03324445,-0.0040870616,-0.06366675,0.0026962536,0.030765573,0.058285486,0.07611025,-0.0062646763,-0.0077543505,0.039374106,0.0028260795,0.07750498,0.005943616,-0.017565375,-0.043182775,-0.057531565,-0.02475459,0.06325408,-0.068618305,-0.03666766,-0.015702542,-0.0033676003,-0.027890628,-0.009839578,-0.009544429,-0.0016690372,0.020198973,-0.02455928,0.035546258,-0.0014847475,0.030004218,0.045285553,0.02577793,-0.032818817,0.039377853,0.02390003,-0.010014096,0.0054602046,-0.012273373,-0.097353645,-0.029397432,-0.00761057,0.00765821,0.0332849,-0.014768295,-0.04326683,-0.0064549525,-0.023914585,-0.074612446,0.01297723,0.027110811,0.032055534,-0.01511731,0.04691575,-0.05582018,-0.01885528,0.014032611,-0.028423486,0.046455927,-0.043508194,0.034388486,-0.020663328,-0.008913395,-0.053750746,0.088576004,-0.030150283,0.033258546,0.018791074,-0.08591301,0.018208755,0.016153222,0.00814071,-0.052545417,0.007717405,-0.04863387,-0.059234604,0.008545386,0.01838282,0.03861256,-0.024247622,-0.032374308,-0.025332598,0.01247718,0.062164847,-0.022669135,0.038468346,-0.027386548,-0.04187196,-0.059289414,-0.038513802,0.037399895,0.039137434,-0.004620292,-0.014883666,0.08921485,0.018193992,-0.039898288,-0.007458029,0.027140055,0.027779013,0.01134097,0.044914085,0.018946875,-0.039574116,0.049870867,0.08205514,-0.035615418,-0.027145676,-0.027990634,0.06020857,0.025809804,-0.011405874,0.035239186,-0.05034909,0.0066770543,0.040034954,0.007962263,-0.0071468037,0.042410858,-0.014491099,-0.019628804,0.03791734,-0.058541913,0.10684617,0.057995394,-0.00758309,0.0022076329,-0.052319515,-0.02231205,-0.04108854,0.045580912,-0.049992085,-0.010607934,0.039261848,0.003109733,-0.0013796677,0.022356203,-0.05439351,-0.018090298,0.053387932,-0.054832444,-0.005955373,-0.06737916,0.06158712,0.0025082983,-0.040478587,0.019650629,0.06524427,0.005809,0.042081103,-0.07432762,0.008607409,0.038246863,0.06840853,-0.049403302,-0.008519457,0.0031522815,-0.062003076,0.02806661,0.015344981,0.016305821,-0.044626486,-0.021148246,0.0178206,-0.021218434,-0.042071838,-0.060316827,-0.03339303,0.019785779,0.013625436,-0.0117252115,0.00040897916,-0.059803598,0.010744985,-0.018574616,0.004731451,-0.011592894,0.01823149,-0.03158592,-0.020129541,0.07837122,0.011710851,-0.015165023,-0.07484528,-0.02162616,0.009651943,-0.06807022,0.013523379,-0.019388784,-0.005438531,0.037512273,-0.024641288,0.02780159,-0.005538312,-0.036507517,0.042850234,0.058511242,-0.03261909,0.029711701,-0.038101785,0.0057698996,-0.01973953,-0.030192466,-0.026244093,-0.030492643,0.0014397879,-0.025343746,0.0011715728,-0.00685997,0.048674867,-0.042763002,-0.040658478,-0.010576861,-0.06068472,-0.027224129,0.04253741,0.0025600898,-0.06255529,0.043926153,-0.04603651,-0.0114514595,0.021730375,-0.010811017,0.0011253427,0.052536476,-0.009281513,-0.058731724,-0.0034106222,-0.06692411,-0.026227064,0.0040108114,0.05421361,0.016114056,-0.018864162,-0.023434462,0.0026458737,0.022592135,-0.016092844,-0.014156992,-0.04673579,-0.04171848,0.009571021,-0.036619883,-0.01777052,0.030429048,0.007697145,0.09714755,0.0007991566,-0.0010429968,0.004625977,0.041929346,-0.046517517,-0.06503687,0.006799313,-0.009074205,-0.00440205,0.012623932,-0.0016826856,-0.0015112245,0.0149535015,0.022227716,-0.025836501,-0.03272947,-0.0048515387,-0.013791524,-0.049316026,-0.029916147,0.0067966236,0.051487375,-0.01569087,0.002375347,-0.0428338,-0.026084581,-0.03031681,-0.020937864,0.0033909997,-0.038288683,0.03624595,-0.06941536,0.01086071,0.06988235,-0.0073533463,0.045631733,0.002780389,-0.0071729682,-0.07557951,-0.0064476747,-0.013608862,-0.029733526,-0.06763349,0.047806762,0.04314609,-0.02447364,-0.07210694,0.0199369,-0.020252751,0.052908644,0.073194794,0.01702758,0.024672464,-0.014053001,0.012606151,-0.025205066,0.06480694,-0.06020805,0.02773118,-0.0136840185,0.00012389806,-0.045556933,0.026728814,0.043514844,-0.028658463,-0.0041344436,0.00077024975,-0.04730587,-0.005579336,-0.062049616,0.01578096,0.005501887,-0.027965728,0.049406793,0.0033269771,-0.02227996,0.012531469,0.026188022,0.012346135,0.043156564,0.03564052,-0.022151928,0.013373298,0.020309627,-0.029860135,-0.0022449822,-0.07523494,0.025632372,-0.02331945,-0.029920561,0.04389538,0.0809337,-0.010377271,0.025316777,0.003128096,0.068449184,-0.005360051,0.010121363,0.012854872,-0.05602795,-0.004684997,0.05283138,-0.00628606,0.060394954,0.075703464,0.016895985,0.010581352,0.00338965,0.016922615,0.060828235,0.004248361,0.024310902,0.03314008,0.018980945,0.026239254,0.004213546,0.049438875,-0.023624768,-0.025010869,0.057135202,0.0027246873,0.0095785605,0.0025777074,0.011499353,0.0061601205,0.06075098,-0.02349654,0.021978203,-0.009645319,-0.01375966,0.036470663,0.043009147,-0.020871952,0.035652738,-0.04832266,-0.06248873,0.029451264,-0.029672049,0.0469795,-0.051894892,0.0410246,-0.017464865,0.029013505,0.009782732,0.05796702,0.051021576,-0.0069775917,-0.020507138,0.0029340724,-0.0057055186,0.04216177,-0.039693195,-0.020702548,-0.005013037,-0.0085412515,0.056127038,-0.021649607,0.02278421,-0.015579746,0.070031054,-0.02026343,-0.00060933287,0.081796825,0.037687987,-0.048767067,-0.07906042,0.02886568,-0.018867897,0.01648685,0.027481353,0.0039213425,0.019472213,0.0047146673,0.031949017,0.03834416,0.071988836,-0.012895135,-0.0019703754,-0.020610496,-0.013197211,-0.027944999,-0.024129815,-0.016884634,0.027755847,-0.022561425,-0.04553047,0.03257505,-0.00046014134,0.05521288,-0.018451048,-0.04358152,0.005629857,0.034729432,0.017090349,-0.031708844,0.011437633,-0.03712233,-0.009557758,-0.038908545,0.008944705,-0.013787685,-0.029546656,-0.008650179,0.04117593,-0.048340466,0.012937449,-0.048038106,-0.08730303,0.009004612,-0.029899746,-0.01709328,-0.0015348585,-0.017632566,-0.06973704,0.0041026147,0.04191497,0.0033025227,-0.045616653,0.0074459244,-0.05734874,0.017672723,-0.0033849122,-0.048335407,0.016140154,0.053959534,0.03946865,0.040019974,-0.057050504,-0.022362834,0.06598897,-0.038199604,-0.03279653,-0.019353965,-0.09648726,-0.019683845,-0.022831814,0.029673109,0.054077987,0.011700724,-0.03286188,0.025589196,-0.0036643813,-0.051563103,-0.0125773745,0.05891024,-0.046404384,-0.0008387375,0.012595938,-0.009896139,0.03836644,0.02312896,0.014641335,0.05372452,-0.0017633218,0.042525634,-0.009383157,-0.048905727,-0.036621485,0.0045766304,-0.029785091,0.04123303,0.028367031,0.030742887,-0.008074451,-0.016132804,0.015130804,-0.042801846,-0.075868055,-0.034374688,-0.014656659,0.012253951,-0.013182537,0.06576636,0.017830815,-0.026703374,0.0082566645,0.0054119485,0.017664226,-0.005247098,-0.006837318,0.011197985,0.014541329,-0.06337037,0.0119449,0.06408141,0.051030852,0.011995685,0.036809593,0.060890846,0.055771038,0.030005211,0.024492579,-0.028694019,0.037185658,0.016189901,0.0293126,0.016303495,0.011798228,-0.009981991,-0.0026899911,0.09631878,0.018838184,0.005618946,-0.005776973,0.017676137,0.021606695,-0.012457936,-0.014234514,-0.0036109202,0.043176956,0.017936317,-0.0142812235,-0.04579658,0.020400068,-0.020383485,0.011526722,-0.011774154,-0.055085935,-0.044567503,-0.03788108,0.0041277567,-0.029095128,0.025158722,0.014328438,-0.061909873,0.028361207,-0.06092023,0.008238304,-0.07964496,-0.025354642,-0.021971013,0.026306173,0.020337578,-0.019946693,0.0021773889,0.03369908,-0.004852095,0.037551384,-0.04204758,0.03351745,-0.039414756,0.027857745,-0.05401091,0.014403894,-0.03412331,0.0061820643,-0.03835762,0.08787153,0.02439657,-0.03464866,0.007792413,0.0022741556,0.012078604,0.06312383,-0.0040228255,0.04921316,0.0024880671,-0.018741654,-0.09567569,-0.0106941145,-0.053610474,-0.013345309,-0.028232832,-0.0048073037,0.03761922,-0.023041999,-0.05125379,-0.07623748,0.044973783,-0.0063261706,-0.046555657,-0.018325571,0.048067097,-0.022642503,0.03433727,0.008743107,-0.03328478,0.021774111,0.019354606,-0.006163575,-0.005397772,0.052827124,0.0028871133,-0.04059128,-0.0002629405,0.0036957616,-0.04495283,0.012963804,-0.014109077,-0.0007434487,-0.011850731,-0.040459543,-0.02144749,-0.02486481,-0.003711147,0.03869861,-0.002801155,-0.051905677,0.028086644,-0.075343415,-0.067707464,0.05341864,0.037891205,0.068135664,0.06931741,-0.03862186,0.037877988,-0.07264034,-0.0013178372,0.028268691,-0.030613702,0.004148379,-0.0167333,-0.0025674861,-0.07256587,0.008275696,0.04426004,-0.08060488,-0.014585295,-0.02939083,0.004096946,0.009062373,-0.01661653,0.01611981,-0.034573603,0.014777471,-0.015523818,-0.015332741,-0.0028831607,0.039288167,0.049089365,0.0057857926,-0.0061111203,0.0009856684,-0.03857273,0.05864203,-0.009094349,0.010527917,0.022160515,0.036923967,-0.015981283,0.02726937,-0.00712765,-0.012064316,-0.032486252,0.025029087,0.000107602245,-0.020135945,0.08966946,-0.018199168,0.011255095,-0.02246859,0.009947916,0.0012008208,-0.06154512,-0.010169671,-0.011835761,0.026915677,-0.004726886,0.06947183,0.019853698,-0.059406366,0.009235389,-0.0029563725,0.008432355,0.05869833,-0.021927526,0.03861555,0.024884526,0.038099144,-0.01309557,0.06968323,-0.026177898,0.03027466,-0.017483793,0.056136932,0.011644194,0.032457322,-0.0010442472,-0.017919138,0.030243836,-0.024264399,0.027075587,0.016628366,0.021895954,-0.051121477,0.007389122,0.022759456,-0.015474441,-0.039093807,0.02255763,-0.03068328,0.0019436294,0.010145367,-0.04198027,-0.07911061,-0.029680597,-0.029959133,0.03720013,-0.025899768,-0.00931846,-0.0007345546,0.014855882,-0.06457336,-0.030713275,-0.002958232,0.01357586,0.04367182,-0.035930276,0.015761346,0.030755032,0.029030368,0.019050539,-0.067684464,0.01536412,-0.0065134,-0.019971224,-0.073574856,-0.01104813,0.034022752,-0.029485237]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "70eee280ad5b487f": {"spanId": "70eee280ad5b487f", "traceId": "ae8304e601ea500403524158a4ee474c", "startTime": 1752603166856, "endTime": 1752603167919.5361, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: work life, finances\\n      Additional information: I am looking to work with a professional coach, somebody with extensive experience working with executives and leadership level. I also prefer to work with a woman in this role\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"hourly_rate\":{\">=\":25,\"<=\":65}}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752603167188.518, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752603167189.8442, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752603167919.316, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752603166856, "endTime": 1752603167919.5361}