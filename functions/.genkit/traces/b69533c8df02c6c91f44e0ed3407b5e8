{"traceId": "b69533c8df02c6c91f44e0ed3407b5e8", "spans": {"6d83ce8d002b7cb3": {"spanId": "6d83ce8d002b7cb3", "traceId": "b69533c8df02c6c91f44e0ed3407b5e8", "parentSpanId": "5fa334ed0d17e61d", "startTime": 1752607746654, "endTime": 1752607747034.1543, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 4/10\\n      Areas of concern: relationships, physical health\\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.040994376,0.016888356,-0.048052005,-0.018335573,0.017479373,0.04224818,0.07940064,-0.**********,-0.015720816,0.026939083,-0.008645083,0.07950176,0.025122005,0.**********,-0.02427731,-0.042741984,-0.038179673,0.038245402,-0.06253273,-0.054871198,-0.013702902,-0.012570625,-0.02235159,-0.021632321,-0.033959832,0.**********,0.013081082,-0.0091125,0.037809137,0.**********,0.016973224,0.032134578,0.046147753,-0.**********,0.04042743,0.044086587,-0.020966778,0.014241218,-0.014300191,-0.08129692,-0.018248208,0.006935196,0.01935349,0.04820527,-0.010588644,-0.021465335,-0.01848828,-0.022843847,-0.059322067,0.024010062,0.02096209,0.026213527,-0.037610233,0.041199446,-0.064948335,-0.024077019,0.015618276,0.006008439,0.040068757,-0.015658267,0.029126218,-0.023230292,0.0067726388,-0.055219688,0.0748106,-0.02911388,0.050578684,0.05383006,-0.06804168,0.043393597,-0.005285226,0.0013000839,-0.08905571,0.002775406,-0.041423153,-0.04880657,-0.0014170106,-0.008083879,0.03448427,-0.025242958,-0.0507593,-0.025564834,0.007417567,0.073120795,-0.007849347,0.042615924,-0.006687174,-0.05628174,-0.04372894,-0.020651994,0.032464575,0.041994262,0.014474108,-0.010430793,0.04932457,0.021468038,-0.06316655,-0.013837993,0.04715761,0.02350263,-0.0005688103,0.032132853,0.01303364,-0.023729347,0.049301174,0.06898304,-0.0360423,-0.032023918,-0.0029939588,0.06022413,0.01171896,-0.026135324,0.061985288,-0.035935342,0.0050969184,0.032797024,0.0063240184,0.016628966,0.041700836,-0.036344435,-0.03141092,0.052445248,-0.0551692,0.10957611,0.05781073,-0.0103526,0.0054262257,-0.037197497,-0.0018048615,-0.028647445,0.060147237,-0.041189946,-0.023039954,0.026238171,-0.0061685177,0.005067714,0.041627917,-0.05443694,-0.019729383,0.05713427,-0.05288703,-0.019537758,-0.057768285,0.027662894,-0.013200719,-0.038884085,0.016913226,0.07848775,-0.003567622,0.049037658,-0.086134106,0.04400792,0.022834746,0.07700088,-0.049265414,0.0064275363,0.025306651,-0.03850292,0.03898996,0.015156604,0.0058008074,-0.065865465,-0.021395495,0.031543847,-0.036141448,-0.03749187,-0.043898195,-0.042012665,0.012640773,0.0012438949,-0.011605385,-0.011674842,-0.0773595,0.019693378,-0.015996255,0.015146965,-0.028003898,0.010284924,-0.030379022,-0.028537411,0.07404152,0.007704974,-0.02532501,-0.09013972,-0.0071193837,0.0016555664,-0.038911555,0.00460314,-0.02933271,-0.025825089,0.04255282,-0.008700869,0.044603284,0.0026239664,-0.041246515,0.049133018,0.062236458,-0.031008946,0.045907542,-0.045434054,0.010900473,0.017173113,-0.015026367,-0.02683606,-0.028400937,-0.020262359,-0.022624826,0.014523463,-0.017551921,0.07431661,-0.04727166,-0.030419312,-0.002367364,-0.1056174,-0.033110913,0.05088839,-0.0065530036,-0.027741712,0.048810173,-0.06214281,0.0051546698,0.027056538,-0.007331022,0.0029117672,0.03746541,0.001233791,-0.095453605,0.0007130446,-0.050950464,-0.035794728,-0.0062257885,0.0419937,0.013053292,-0.04799384,-0.0020231416,0.046196524,0.020171579,-0.041321583,-0.009517175,-0.038221736,-0.062397756,0.032519344,-0.026703028,-0.023318848,0.029050397,0.00033090945,0.08287671,0.022889175,0.004938368,-0.0032611068,0.059902664,-0.02388685,-0.07588156,0.006224016,-0.002333039,-0.0056632534,0.010068206,0.006741849,-0.026058227,0.009990951,0.050068863,-0.017852291,-0.029284194,0.002501364,-0.003235666,-0.054849736,-0.0131793,0.0019927656,0.047798242,-0.04047967,-0.0022331055,-0.034654498,-0.0494727,-0.029997822,-0.029217841,0.0014074692,-0.029827826,0.046317782,-0.057161596,0.02294274,0.04363411,-0.0023956168,0.048988346,-0.01846903,-0.017118061,-0.069080584,-0.0058643795,-0.0026198255,-0.030423854,-0.04424617,0.05286373,0.022526113,-0.052437205,-0.040648617,0.023216425,-0.015744623,0.039217655,0.05724888,0.017531984,-0.006016091,-0.008425977,0.017227288,-0.02012635,0.06397011,-0.04331562,0.053917352,-0.021902425,0.0088771265,-0.050210472,0.029900594,0.030992547,-0.032485362,0.0058891256,0.011089532,-0.04428087,-0.020245835,-0.05292305,-0.0018101403,0.0010454368,-0.038295906,0.03418049,0.008473779,-0.042365257,0.01889091,0.044790972,-0.0054310295,0.037499268,0.01223316,-0.016118037,0.045630325,0.023011597,-0.015564745,0.00718869,-0.08960237,0.036277957,-0.01836886,-0.02865194,0.06257839,0.07806393,0.00952675,0.04430403,-0.02825513,0.06451673,-0.015070254,0.016710563,-0.0008805677,-0.052947897,-0.026818164,0.032372717,0.012583624,0.060171064,0.07811993,-0.0008372867,0.007720726,-0.005124551,0.003411525,0.057838988,0.0043438408,0.038684268,0.029418759,-0.008722475,0.03125378,0.017870657,0.046236068,-0.008321064,-0.011771965,0.0851183,-0.015303978,0.017638218,0.0007349789,0.015254756,0.011593422,0.07061947,-0.021356786,0.034370586,-0.01090416,-0.016586583,0.042527385,0.018666781,-0.05482963,0.019023744,-0.03333211,-0.06046937,0.03626028,-0.019172534,0.054522682,-0.05935383,0.035389613,-0.011896777,0.035803124,-0.0106711555,0.04968397,0.03738307,-0.009754388,-0.0052985786,0.0143102845,-0.007902769,0.037450653,-0.03430674,-0.019395165,0.013495609,-0.0030865904,0.059571903,0.0088990545,0.03288918,-0.03164864,0.07116185,-0.019178215,0.008729447,0.052774668,0.008916518,-0.033031654,-0.05858717,0.034628414,-0.013883822,0.007971832,0.032532156,0.0018814638,0.02018518,0.014528672,0.020678349,0.04506584,0.04225696,-0.0050370703,0.015165354,-0.024639655,-0.011315744,-0.016621817,-0.01549862,-0.023158062,0.030038085,-0.008722264,-0.045028042,0.020114327,0.0022206497,0.058692507,-0.01121294,-0.040291645,0.007954784,0.03731839,0.019057909,-0.020830715,0.021297697,-0.04270181,-0.022524374,-0.010627719,0.021839323,-0.020028114,-0.033380553,-0.0030091167,0.025688088,-0.037632447,-0.015060433,-0.052504044,-0.09114302,0.020124437,-0.022782361,-0.0018595435,0.018270569,-0.008820391,-0.06408537,-0.017805299,0.037575122,0.010546946,-0.030704182,-0.0048290505,-0.07491943,0.013320506,0.006483947,-0.036883306,0.017427351,0.056708124,0.019932538,0.031076498,-0.09176214,-0.024740387,0.054716926,-0.020991953,-0.03187119,-0.032983705,-0.081545524,-0.017526679,-0.033512026,0.04982647,0.044738177,-0.0057890145,-0.040064983,0.033395685,0.0073819063,-0.053308114,-0.010259083,0.048604965,-0.059016597,-0.0063507035,0.008152501,-0.025392879,0.042405725,0.0031775304,0.020781899,0.0722907,-0.004434366,0.062391773,-0.008018086,-0.044372447,-0.054488678,-0.0103920875,-0.019576017,0.04139674,0.022426846,0.031730074,-0.0011038283,-0.010092824,-0.002059931,-0.036754597,-0.041799482,-0.0011432231,-0.0028257293,0.03454912,-0.01123465,0.03927064,-0.0053590103,-0.028379165,0.0150390165,0.007466965,0.033497028,-0.02526243,-0.008142587,-0.010256475,0.006804925,-0.06888409,-0.015158572,0.07453526,0.046459742,0.002381394,0.033732783,0.055380374,0.05319028,0.028029576,0.02182088,-0.023788352,0.03277312,0.012257129,0.033426654,0.027817842,0.009375009,-0.018039783,-0.008989574,0.089978576,0.032338295,0.029164482,-0.0075470386,0.0021471474,0.010752638,0.0029748986,-0.012862543,0.02977539,0.03248782,0.004709361,-0.012310587,-0.024314523,0.0128506785,-0.014535125,-0.009195975,-0.004457742,-0.03451627,-0.05218724,-0.05397436,0.013196609,-0.027122825,0.035717376,-0.0050057457,-0.051170018,0.029143509,-0.05602415,0.004224374,-0.065345444,-0.021976572,-0.0369468,0.023593655,0.013319132,-0.013499243,0.008551864,0.042674262,0.007201569,0.041771747,-0.047463216,0.027297528,-0.04697297,0.009972547,-0.047093254,0.03212634,-0.025936693,0.0023766644,-0.033207934,0.07409755,0.021216849,-0.033192668,0.0032969692,-0.00041273548,0.01659291,0.05219282,0.0047492706,0.052087363,0.0091582835,-0.03396,-0.05894668,-0.0064609447,-0.0520089,0.0029045253,-0.012156811,-0.014460009,0.028000336,-0.023049302,-0.040415768,-0.076449305,0.024626514,0.013027568,-0.061753068,-0.010002788,0.06086662,-0.014118769,0.048181653,0.0046428624,-0.03522892,0.021631602,0.008968302,-0.025719881,0.0047581405,0.046803504,-0.003004108,-0.009545848,-0.022665784,0.0057071103,-0.024754189,-0.0074987784,-0.012640751,-0.008155923,-0.011508786,-0.02544558,-0.031858794,-0.026184464,-0.019504072,0.029428147,-0.008682535,-0.066304065,0.032551307,-0.080572434,-0.076495074,0.051527586,0.028022524,0.05208291,0.042680673,-0.041414764,0.032285336,-0.07014152,-0.010552208,0.045962002,-0.027570218,0.008570183,-0.016620869,-0.0048589446,-0.07120483,0.039715413,0.044107772,-0.080582894,-0.016974002,-0.03525229,0.023033414,0.0016569528,-0.03967762,0.037054345,-0.039077453,-0.0016054593,0.007889171,-0.0060166465,-0.0016497665,0.038846076,0.041540638,-0.0010247247,-0.014752138,-0.005021869,-0.021028668,0.02821843,0.0047585913,0.003854443,0.020986317,0.062286954,0.006162735,0.041679066,-0.007017018,-0.01971397,-0.06230251,-0.0021139812,-0.0035113434,-0.03143951,0.078102514,-0.017378483,0.03244125,-0.023669787,-0.021896495,0.020431222,-0.05257661,-0.018650949,0.012426911,0.058393024,-0.0074696834,0.050547484,0.0031167057,-0.06560066,0.005502887,-0.015702717,0.013401637,0.07793254,-0.024798047,0.020390406,0.006853527,0.031347565,-0.0010974,0.06072652,-0.025592476,0.015448906,-0.013248872,0.05104247,0.008821161,0.04893135,-0.0025719479,-0.012369375,0.022658395,-0.03257702,0.023351276,0.01535799,0.039193053,-0.061848216,0.019286256,0.0287609,-0.044526074,-0.050055586,0.0137238335,-0.03157364,0.009933196,0.015163398,-0.040056404,-0.05993827,-0.0027971857,-0.038762122,0.018932058,-0.032424465,0.0013347064,0.009724164,0.020000294,-0.073941424,-0.043965045,-0.0048565287,0.0032346665,0.024168646,-0.03428445,0.032039803,0.034882635,0.022706455,0.028199961,-0.08269477,0.0060846875,0.0002329707,-0.024689045,-0.06297277,0.0044871005,0.042385995,-0.025952073]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "5fa334ed0d17e61d": {"spanId": "5fa334ed0d17e61d", "traceId": "b69533c8df02c6c91f44e0ed3407b5e8", "startTime": 1752607746646, "endTime": 1752607747565.9268, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 4/10\\n      Areas of concern: relationships, physical health\\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"session_type\":\"no_preference\",\"working_hours\":\"flexible\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752607747043.1353, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752607747097.3623, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752607747565.666, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752607746646, "endTime": 1752607747565.9268}