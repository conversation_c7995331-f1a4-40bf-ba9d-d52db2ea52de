{"traceId": "3724312bb11f2136b8025c7c5263c2b6", "spans": {"ec1c9c0c2e90f121": {"spanId": "ec1c9c0c2e90f121", "traceId": "3724312bb11f2136b8025c7c5263c2b6", "parentSpanId": "bf9f052c1d42802f", "startTime": 1752604789322, "endTime": 1752604789658.582, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: executive coaching\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.024305172,-0.020787362,-0.050871886,0.015411009,0.004144573,0.054771308,0.07117209,-0.01976117,0.011566992,0.049634915,-0.0042385496,0.061348107,0.028780274,-0.012528848,-0.028127847,-0.06700684,-0.044880383,0.036127094,-0.07183561,-0.019151082,-0.008997062,-0.0152094755,-0.02961819,-0.001550926,0.028599475,0.002001215,0.0064442507,-0.038217604,-0.005016706,0.012638944,0.020851824,-0.0033349816,0.048630793,-0.012735741,0.03996445,0.031579413,-0.019157339,-0.0017565525,-0.0040043434,-0.09422801,-0.024435898,-0.017436763,0.0048596878,0.034605615,0.0037555487,-0.053402457,0.00070886686,-0.01185225,-0.058273885,0.009797802,0.007845039,0.020332174,-0.018300107,0.055835575,-0.034680005,0.012009778,0.01828656,-0.0262629,0.05669169,-0.03423989,0.03262692,-0.039822996,-0.0324928,-0.04580581,0.07499764,-0.040006034,0.0378145,0.015999826,-0.08300344,0.019108824,-0.026947424,-0.0038001132,-0.04355641,-0.0056354185,-0.04197202,-0.05552444,-0.0041875895,-0.010877739,0.019399215,-0.041238423,-0.03198337,-0.01715316,0.03334859,0.069881074,0.000960912,0.04143487,-0.02962682,-0.04142255,-0.04807219,-0.0110099325,0.043929826,0.02886338,-0.024707478,-0.0094253095,0.06669975,0.03941482,-0.02283851,-0.008304224,0.049691454,0.0108975,0.018150734,0.057100356,0.010541277,-0.03892239,0.036721457,0.09236938,-0.04581928,-0.011734642,-0.018594027,0.05368828,0.015107839,-0.017781738,0.051647406,-0.029718932,0.0115247695,0.0044417805,0.0041112676,-0.025400808,0.053077802,0.017270459,-0.023679007,0.06259344,-0.059518177,0.10983967,0.048587523,0.0030502242,0.019197904,-0.019205574,-0.04191922,-0.051799625,0.01813942,-0.06433055,-0.01085269,0.021582555,-0.02101212,-0.012963564,0.0038923037,-0.088794276,-0.008075646,0.053712323,-0.029885016,-0.033852696,-0.061890766,0.0741642,-0.0058352235,-0.06545434,0.0280113,0.1140682,0.03257827,0.020977078,-0.05281169,-0.012472573,0.034643333,0.06287615,-0.027147561,-0.03032424,0.017615369,-0.077403076,0.02663776,0.016126595,0.016602587,-0.06665531,-0.03369786,0.007213843,-0.02795334,-0.03992828,-0.07604087,-0.037625913,0.015213889,-0.02028041,-0.038662143,-0.009211043,-0.054454386,0.028859477,-0.002058252,0.029326132,-0.01630916,0.0089710895,-0.041945785,0.0052314284,0.09182787,0.047576927,0.003218496,-0.07088069,-0.04685035,0.025655467,-0.037259355,0.0025408713,-0.020366902,-0.004870206,0.04333685,0.001406535,0.038818788,0.0262373,-0.047582593,0.07110514,0.056925032,-0.05059728,0.023444897,-0.037370287,0.0025546043,0.016937759,-0.033440925,-0.0042381124,-0.039957132,-0.0030424427,-0.045227252,-0.010424724,-0.013387246,0.02647191,-0.028945321,-0.012763827,-0.013894604,-0.060577616,-0.0065543894,0.015601746,0.01134594,-0.058638602,0.0376778,-0.041196384,-0.0089065125,0.015255107,0.011054531,-0.014404911,0.04338511,0.005123112,-0.07823552,-0.007467356,-0.042437635,-0.016007164,-0.005573348,0.019881962,0.005677986,-0.010609404,-0.03370019,0.04553065,0.023096375,0.0068771155,0.009104405,-0.025322651,-0.067497686,0.04267772,0.0046285973,-0.025508985,0.029249894,0.022938738,0.075643666,0.0053048944,0.0015761236,-0.018112801,0.053632982,-0.046758708,-0.062405434,-0.042909276,-0.002110843,-0.012175116,0.01921987,-0.0021611245,-0.010789989,0.014085978,0.04425087,-0.017237082,-0.021038989,-0.0076804697,-0.016751176,-0.043737836,-0.015470379,0.028631015,0.05551057,-0.04363956,0.009649476,-0.0501181,-0.026217734,-0.01946162,-0.042288214,0.0010130653,-0.034278765,0.023062684,-0.039279398,0.010847559,0.07895013,-0.012606467,0.04642494,0.0039758473,0.00989791,-0.07328084,-0.0044532884,0.022214781,-0.0013519675,-0.06427223,0.049373075,0.044846438,-0.03686659,-0.052367534,0.014870917,-0.033144057,0.040628698,0.0537013,0.032473914,0.025587399,0.011094678,0.016616289,-0.037036642,0.051467426,-0.045309637,0.0367362,-0.0037790688,-0.002135876,-0.025247993,0.039261993,0.020233031,-0.00075338647,-0.020806925,-0.013894551,-0.058506038,-0.005696174,-0.070930526,-0.004264056,0.008352859,-0.023346782,0.04537933,0.017574362,-0.015227176,0.043922268,0.0071310336,0.020367606,0.037428744,0.039954916,0.0072799656,0.0147659555,0.015719889,-0.01325604,-0.021815399,-0.07290254,0.018086068,-0.017649775,-0.0337517,0.032654542,0.059204493,-0.010827757,0.04055915,-0.0057909526,0.058070146,0.0064868485,-0.01677393,-0.0056851725,-0.036457434,-0.010186587,0.07263958,-0.031975474,0.051162682,0.05496555,-0.013030803,-0.0015367075,0.015831353,0.0011450247,0.03767232,0.0012388319,0.025115851,0.04271429,0.03132252,0.043088846,0.030608159,0.05714663,-0.055304818,-0.024118146,0.025742358,0.0152909085,0.027701024,0.003451003,0.015159414,-0.023250598,0.049529918,-0.023375705,0.019175969,-0.032958996,-0.0332054,0.020105068,0.01720785,-0.0030811823,0.032506038,-0.061239917,-0.048586167,0.027651807,-0.015540321,0.017331434,-0.07059598,0.03467091,-0.023588108,0.04194032,-0.00094533287,0.044682603,0.005603368,0.018339287,-0.0033730716,0.022331744,-0.008232996,0.064527765,-0.0531403,0.00078958407,-0.0077995067,-0.017757328,0.035300016,-0.019556988,0.016239956,-0.012360739,0.055914495,-0.021469755,-0.007511505,0.07714445,0.017022936,-0.035625536,-0.07505899,0.040183276,-0.024311505,0.03689026,0.03021232,-0.015920853,0.020417936,0.011805768,0.059220884,0.035977148,0.06033258,0.010657173,-0.0051583494,-0.011790802,-0.020300172,-0.017238926,-0.020096181,-0.0028580811,0.026410438,-0.019928634,-0.039413687,0.060657978,-0.0036413171,0.041831717,0.0028984295,-0.04381295,0.024406787,0.032640688,0.0016579101,-0.039477814,0.04361303,-0.019742565,0.013901051,-0.04127522,0.015229336,-0.006002613,-0.026005711,-0.021765498,0.029657431,-0.046858724,0.008920942,-0.06126173,-0.08836598,0.024809517,-0.04165774,0.0037775917,0.0046471613,-0.03732575,-0.055371366,-0.024185527,0.043331288,0.004616141,-0.0445281,-0.013892006,-0.04937036,0.040053125,-0.00906325,-0.037269916,0.01559236,0.021444174,0.06473937,0.03642074,-0.075462125,-0.057760637,0.056716442,-0.04381129,-0.015956571,-0.03275886,-0.08501138,-0.024666788,-0.034428257,0.027047487,0.062445957,0.023173375,-0.020695338,0.017476387,-0.009586086,-0.057391718,-0.0010584644,0.034759574,-0.04382355,-0.004920474,0.0034750844,0.013507681,0.043226883,0.008013817,0.0072611165,0.079909734,0.02804856,0.04754247,-0.004281012,-0.06494065,-0.056023344,-0.01633111,-0.049664445,0.013482084,0.0046695736,0.023473725,0.018839682,-0.00070126646,0.025353331,-0.024539229,-0.06268829,-0.03408072,-0.02525338,0.026990112,0.00044562758,0.055357065,0.015831543,-0.030126462,0.0072828033,0.0020717874,0.023290219,-0.02103807,0.02364399,0.009474587,0.022880856,-0.071686596,0.018255157,0.07981715,0.039179605,0.0029361248,0.01929143,0.06572228,0.050874557,0.028025728,0.026166076,-0.029800445,0.017469028,0.024538359,0.0077352296,0.017591398,0.018856877,-0.019302772,-0.02212676,0.11317415,0.01035746,0.007637424,-0.029308235,0.023574801,0.03241738,-0.0042353733,0.0128788585,0.023282606,0.040487792,0.0005305679,-0.017334895,-0.019466769,0.0154291075,-0.013221699,0.025845367,-0.016157102,-0.073851146,-0.04990964,-0.033921473,0.017728232,-0.017023578,0.0059427954,-0.02672549,-0.038927697,0.030656504,-0.06114713,-0.012057621,-0.051563676,-0.041017514,-0.02332861,0.018794158,0.04213215,-0.01654126,0.037131935,0.017994199,-0.027710304,0.039818566,-0.04105842,0.029670782,-0.03311995,0.03800008,-0.024707185,0.017057614,-0.04710408,0.024534483,-0.027722478,0.08404071,0.003499978,0.0047308058,0.009270344,0.044246193,-0.0074231313,0.05266399,-0.01024281,0.053479966,-0.012416352,-0.030213147,-0.08889218,0.021709837,-0.0654783,-0.016238436,-0.026539506,0.012753162,0.014771717,-0.046524365,-0.08264206,-0.049685437,0.020298876,-0.016238727,-0.04353954,0.013224053,0.037088357,-0.017149862,0.021865672,0.019822689,-0.039733227,0.015449251,-0.0010375105,-0.014170767,0.007601206,0.025947308,-0.008825695,-0.0063398723,0.014740662,0.019416355,-0.038526017,0.0001678325,-0.004960556,0.02335604,-0.013518028,-0.06746451,-0.053680282,-0.02678011,0.016363727,0.062377892,-0.023465987,-0.073991664,0.037632994,-0.07045618,-0.07067581,0.0543874,0.032004345,0.07771735,0.04326966,-0.039455116,0.0498049,-0.03581932,0.020723617,0.03732289,-0.025495587,-0.002345695,-0.040147733,-0.008588911,-0.030948935,0.010007852,0.025929544,-0.043832112,-0.03165482,-0.012459793,0.0035084663,0.017457547,-0.017085742,0.010209259,-0.041425653,0.00053830777,-0.030704519,-0.030653547,0.00029758358,0.040750258,0.035474196,-0.0120827705,0.0038999626,-0.010109098,-0.039718688,0.052745186,-0.014142446,-0.001310242,0.021018539,0.05331836,-0.0044604447,0.00006107297,-0.00089156855,-0.024615156,-0.01872247,0.021374403,0.0066254307,-0.032406256,0.08106292,-0.02017074,0.006812831,-0.009210782,0.017072326,-0.00069672166,-0.04197246,-0.03659489,-0.013866818,0.051923133,0.0044941804,0.022850532,0.015439986,-0.06687501,0.00335202,-0.02418239,0.0002018631,0.06144093,-0.026061134,0.010644625,0.015836844,0.028662082,0.00007646606,0.02754712,0.010415933,0.03835486,-0.013429452,0.034607388,0.010268591,0.046503004,-0.016614093,-0.025682578,0.0050235814,-0.016071338,0.007916436,0.028282678,0.04118042,-0.0679993,0.023952324,0.010898941,-0.03339327,-0.028109388,0.021554343,-0.029230129,0.015486035,0.0085213985,-0.039031282,-0.05618656,-0.007232339,-0.023738056,0.041919835,-0.033844393,0.007244404,-0.001961578,0.03192236,-0.09156865,0.003356466,-0.00091331603,0.021771848,0.03347995,-0.049889456,0.011408079,0.040638205,0.043099802,-0.00841358,-0.09607641,0.026874265,-0.0064552496,-0.012111594,-0.07502809,-0.010763061,0.027627414,-0.011322545]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "bf9f052c1d42802f": {"spanId": "bf9f052c1d42802f", "traceId": "3724312bb11f2136b8025c7c5263c2b6", "startTime": 1752604789316, "endTime": 1752604790059.7065, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: executive coaching\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"compositeFilter\":{\"op\":\"AND\",\"filters\":[{\"fieldFilter\":{\"field\":{\"fieldReference\":\"hourly_rate\"},\"op\":\"GREATER_THAN_OR_EQUAL\",\"value\":{\"integerValue\":10}}},{\"fieldFilter\":{\"field\":{\"fieldReference\":\"hourly_rate\"},\"op\":\"LESS_THAN_OR_EQUAL\",\"value\":{\"integerValue\":75}}},{\"fieldFilter\":{\"field\":{\"fieldReference\":\"availability\"},\"op\":\"EQUAL\",\"value\":{\"stringValue\":\"available\"}}}]}}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752604789663.0076, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752604789710.7302, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752604790059.5808, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752604789316, "endTime": 1752604790059.7065}