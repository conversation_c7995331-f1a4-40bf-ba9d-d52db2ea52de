{"traceId": "db2c277af700f00b401d92d85abfb9db", "spans": {"bc65534d6429f305": {"spanId": "bc65534d6429f305", "traceId": "db2c277af700f00b401d92d85abfb9db", "parentSpanId": "4f96a14a29d65c36", "startTime": 1752607094533, "endTime": 1752607094902.1626, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 6/10\\n      Areas of concern: finances, work life\\n      Additional information: coaching please, from a female\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.02571973,0.00091816153,-0.050588068,-0.0057597314,0.010181353,0.058770638,0.057247754,0.014928544,-0.018826453,0.021089919,-0.0011088504,0.090086095,0.016556237,-0.0134937465,-0.044739414,-0.048286308,-0.06357624,0.034347765,-0.07346246,-0.031984195,-0.011541905,-0.0020105334,-0.03778771,-0.014103637,0.01338687,-0.009796949,0.0031671734,-0.07856668,0.060459465,-0.032641083,0.025942247,0.034452155,0.059771676,-0.04809458,0.015149001,0.0125976335,0.01329596,-0.023129718,0.00955182,-0.07912665,-0.017453993,0.013665377,-0.00045213342,0.029678311,-0.0027634504,-0.07047928,-0.02602188,-0.009427128,-0.059718233,0.018429562,0.022706455,0.010005314,-0.018914752,0.04006776,-0.08504266,-0.008410103,0.0050584837,-0.04168788,0.039866775,-0.03127842,0.041121423,-0.052034333,-0.037852474,-0.042197656,0.07435865,-0.03463607,0.043274574,0.023761526,-0.07529918,0.021507382,0.0072371876,-0.037778832,-0.03351059,0.019837005,-0.050868202,-0.03268122,-0.0036880726,0.02993689,-0.0024912655,-0.026757699,-0.028752895,-0.018817795,0.014617001,0.06805628,-0.008191133,0.037870687,-0.017229833,-0.05223717,-0.06428588,-0.047106735,0.0430347,0.03894805,-0.024429861,-0.0034727415,0.07783392,0.058973733,-0.031524844,-0.0069444715,0.004477996,0.031320453,0.0142635945,0.062088028,0.010683076,-0.05892459,0.05688569,0.091827035,-0.03207613,0.00727058,-0.033659544,0.051840156,-0.0006050318,-0.00772078,0.05886205,-0.037806507,0.004309705,0.016635276,0.007064268,-0.01922316,0.0604874,-0.026789337,-0.0034653435,0.03876803,-0.047322378,0.10162384,0.04556845,-0.0053217057,0.001176674,-0.06160124,-0.054214638,-0.04395195,0.037362855,-0.036826096,-0.03662865,0.054896146,-0.019652734,-0.015337512,0.012962509,-0.047178973,0.005703191,0.061554942,-0.06401626,-0.015924357,-0.063252375,0.07725378,-0.01821897,-0.04598615,0.03212575,0.07412403,-0.00032646436,0.036303837,-0.09188305,0.005929789,0.0261131,0.06805575,-0.058649957,0.020009778,0.0019514089,-0.061873883,0.016122641,0.017617982,0.016852843,-0.074853964,0.0039399536,0.0006416775,-0.049555555,-0.050922524,-0.06840999,-0.04618279,0.024286993,-0.0048218025,-0.021437,-0.0064270473,-0.045205817,0.018958855,0.01030638,0.03482022,-0.0096205985,0.0049724267,-0.044427246,-0.008700414,0.08825459,0.04024373,-0.015744602,-0.057927094,-0.042720556,0.018473843,-0.01770263,0.027760746,-0.040520452,-0.021649877,0.056017093,-0.008796776,0.027814478,0.016025398,-0.034575704,0.04484098,0.07281979,-0.041165914,0.013605944,-0.03984858,-0.009415312,-0.020863712,-0.025878519,-0.04081926,-0.035365697,-0.020966692,-0.032486413,-0.028941283,0.0017608823,0.03897949,-0.03147857,-0.011018692,-0.017750207,-0.052058026,-0.028664414,0.034494456,0.02314852,-0.045043703,0.039445862,-0.03489525,-0.026906021,0.03321614,0.012569954,-0.024705067,0.06292189,-0.018752143,-0.07292497,-0.024905508,-0.07020936,-0.021618186,-0.013780655,0.044244505,0.0026946326,0.002876828,-0.014491249,0.021207316,0.01593183,-0.02141654,0.023646548,-0.033445884,-0.062336326,0.03118666,-0.014549954,-0.019738503,0.018672368,-0.002538122,0.073065706,0.016540555,-0.015926901,0.003969916,0.058491074,-0.038162917,-0.06417886,-0.032320086,-0.012609239,-0.051516697,0.04068199,-0.0050063035,-0.009349189,0.01096865,0.045588136,-0.013434782,-0.010624942,0.0066499095,-0.018783761,-0.034888044,-0.00017872371,0.00016920936,0.05483799,-0.03439243,0.037148792,-0.033774775,-0.018694695,-0.014944842,-0.039424606,-0.0028983955,-0.022516772,0.04099248,-0.023152241,0.014891666,0.07167449,0.008541342,0.04498993,-0.0041390145,0.015093544,-0.07965356,-0.0016734275,-0.021600856,-0.024656042,-0.063844904,0.04000507,0.027927024,-0.024058927,-0.033553485,0.0054513575,-0.041345138,0.0344878,0.06463123,0.027641399,0.050236624,0.0047756736,0.011153015,-0.033888843,0.059768938,-0.046632476,0.036030695,-0.0085478425,-0.0047915983,-0.018051384,0.041610118,0.012923784,0.0023501983,-0.015196759,-0.004456996,-0.04999801,-0.0019687163,-0.090482056,-0.0042604567,-0.0131958015,-0.02617856,0.036258277,0.0019630045,-0.011381117,0.010572339,0.0154729625,0.035119798,0.0358634,0.026155993,0.008128435,0.031928938,0.016410243,-0.008269515,-0.008732434,-0.09350763,0.015405349,-0.017064601,-0.024624806,0.051778585,0.10357516,-0.0034776474,0.033744007,0.017267415,0.054775637,0.004890286,0.0041508405,-0.012231131,-0.033520263,-0.010302428,0.0728658,-0.018009717,0.04916512,0.072296396,0.017468525,0.0012698154,-0.002092284,0.0024856755,0.044789482,-0.000014761595,0.0075325402,0.04725157,0.007479364,0.041045677,0.022598168,0.051165,-0.022354035,-0.0021514893,0.04639839,0.0023469657,-0.00795429,-0.010332642,0.0027511448,-0.01974167,0.05175442,-0.018093437,0.0032668917,-0.0144924745,-0.019142073,0.018637765,0.01586692,0.006698076,0.032764867,-0.06011517,-0.0565207,0.030929398,-0.021518644,0.013599541,-0.052363534,0.01732557,-0.019867346,0.045212656,0.0092869215,0.07205084,0.008936812,0.032579474,0.0105249025,0.009171644,-0.054096386,0.030803353,-0.04505977,-0.02549131,-0.0029325862,-0.015116575,0.049563393,-0.0079839,0.0060302075,-0.026514878,0.068425775,-0.03403472,0.01008816,0.08001118,0.02864261,-0.00976868,-0.06499135,0.03566033,-0.024410762,0.0018225178,0.004254564,-0.0109967915,0.023796175,0.0129061695,0.026647264,0.021616401,0.07295037,0.015533947,-0.0062123793,-0.018828254,-0.007375296,-0.023271557,-0.01697606,-0.019490324,0.014985965,-0.009613646,-0.056686703,0.036781687,-0.0053076535,0.08105669,-0.011861653,-0.033908654,0.023541862,0.043797478,0.0040503647,-0.008349761,0.009250833,-0.028956927,-0.0012299095,-0.05806419,0.010896946,0.0076996875,-0.017004853,-0.013049951,0.02276692,-0.02917656,0.010634566,-0.036633916,-0.06705595,0.015466791,-0.026079698,-0.0075515,-0.014744575,0.0020962502,-0.044136267,-0.0066168425,0.020192172,0.012920071,-0.053196926,-0.027391233,-0.06337005,0.028848797,0.015955646,-0.01291433,0.012948278,0.022992078,0.052711304,0.029597318,-0.038556393,-0.04048523,0.056512166,-0.03385497,-0.034734055,0.000047753485,-0.07050322,-0.013099901,-0.055838566,0.02046652,0.04914733,0.008768114,-0.04215648,0.037039,-0.013416339,-0.042067345,-0.007291054,0.049013752,-0.06753216,0.012379081,0.006491431,-0.0038244626,0.056076154,0.024728881,0.018545814,0.050534207,0.008231511,0.027113276,0.02134473,-0.057656698,-0.053538933,-0.012253499,-0.02513316,0.017057976,0.024388215,0.019847972,0.03789252,-0.010603445,0.013084894,-0.01903342,-0.04275464,-0.031160368,-0.0060905437,0.030179564,-0.014925887,0.07699668,-0.016768523,-0.036358517,0.008820231,-0.0021665825,0.003688136,-0.023737978,-0.012298793,0.04777999,0.026874565,-0.088519245,0.018888824,0.060162056,0.048917383,-0.0025075777,0.02251684,0.062853426,0.07054169,0.040879805,-0.00029057456,-0.023610473,0.021687258,0.02542302,0.023765318,0.006518613,0.018055044,-0.004579979,-0.0069179763,0.081575684,-0.01482936,0.02037227,0.00076609943,0.0313601,0.016418189,-0.0071413154,0.0022166206,-0.0013631303,0.02384784,-0.0060578375,0.0047633178,-0.044303466,0.0069323783,-0.027777383,0.033569355,0.011883092,-0.07283719,-0.04137844,-0.03901578,-0.0060942178,-0.032901622,0.012984749,-0.0065716095,-0.043597303,0.035933822,-0.07048794,-0.017410045,-0.068035804,-0.03151028,-0.026700225,0.023186833,0.009745476,-0.019843517,-0.015113594,0.002263757,0.0018708142,0.012703924,-0.063617475,0.024001552,-0.043590207,0.016540913,-0.05453763,-0.0039614844,-0.032908205,-0.0021737646,-0.02168629,0.08342787,0.01502707,-0.01764051,0.025569037,0.030803489,0.004903606,0.05617539,-0.008285507,0.044797253,-0.0062521747,-0.04679662,-0.075406045,0.014977187,-0.0769111,-0.00031050842,-0.027012043,-0.009921115,0.022263175,-0.044413734,-0.053463295,-0.057096425,0.014661162,-0.03871122,-0.05746962,-0.017257519,0.035894133,-0.029300341,0.031527147,0.009235853,-0.01762512,0.010801861,-0.005822607,-0.012006862,-0.0058501502,0.006877448,-0.0065649636,-0.036712352,0.00052847475,0.01495998,-0.04230009,0.001240161,-0.011323154,0.0033050699,-0.03189733,-0.021765023,-0.020568406,-0.027966134,-0.0070636612,0.05402829,-0.008963973,-0.07616651,0.04960184,-0.061009925,-0.060293622,0.06294047,0.03741908,0.07124469,0.028805243,-0.033086356,0.046825018,-0.046143245,0.009372479,0.04144397,-0.04274647,0.009615999,-0.0204876,-0.02872843,-0.06086951,0.013186461,0.0035611962,-0.055273585,-0.03274133,-0.03177581,0.012688067,0.0050679944,-0.023865785,0.017199209,-0.049623355,0.021483915,-0.015371818,-0.025351698,0.016103271,0.040930368,0.039076354,0.020474896,0.009947963,-0.02374054,-0.024841549,0.043952927,-0.01760185,0.012922184,0.01787907,0.05369643,0.0013124185,0.019099701,-0.0058637396,-0.015074617,-0.028812313,0.038193386,0.0049023638,-0.024599876,0.09580865,-0.013608043,0.012217277,-0.016129602,0.025936684,0.007123176,-0.057860266,-0.03753655,-0.021891383,0.037846826,-0.016449599,0.064989924,0.017371336,-0.030836316,0.028852867,-0.004601525,-0.0010609564,0.057492103,-0.018974107,0.036844485,0.010942891,0.026416073,-0.02098069,0.08028697,-0.005441857,0.037102483,0.0036486457,0.047676988,0.012019641,0.038403016,-0.02139191,-0.024764283,0.011064167,-0.015609397,0.009225049,0.016475845,0.017476426,-0.039375663,0.01489616,0.009175372,-0.04420829,-0.05146392,0.011645745,-0.047032494,-0.011159602,0.0045945654,-0.037633535,-0.04881021,-0.027713303,-0.033449106,0.018896392,-0.036655225,0.0045066494,-0.013107379,0.011291949,-0.05695643,0.004985178,0.008157389,0.0058718184,0.027923131,-0.0745679,0.031143924,0.050295267,0.02386042,-0.0070121284,-0.091260135,0.012024168,0.014813034,0.005322112,-0.066710144,-0.03413891,0.03780788,0.007547689]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "4f96a14a29d65c36": {"spanId": "4f96a14a29d65c36", "traceId": "db2c277af700f00b401d92d85abfb9db", "startTime": 1752607094526, "endTime": 1752607095815.1062, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 6/10\\n      Areas of concern: finances, work life\\n      Additional information: coaching please, from a female\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"session_type\":\"no_preference\",\"working_hours\":\"flexible\",\"gender\":\"no_preference\",\"works_with_gender\":\"prefer-not-to-say\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=gender --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752607094905.5251, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752607094954.196, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752607095814.7654, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=gender --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=gender --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752607094526, "endTime": 1752607095815.1062}