{"traceId": "d54eca2a1969adf418f7dda9a08c8d42", "spans": {"0d2e036a9279147f": {"spanId": "0d2e036a9279147f", "traceId": "d54eca2a1969adf418f7dda9a08c8d42", "parentSpanId": "f673e77536042977", "startTime": 1752607524919, "endTime": 1752607525299.0706, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.028426595,0.025328092,-0.05081765,-0.024269573,-0.0066570346,0.044662245,0.05223554,-0.00024542437,-0.014558592,0.016202547,-0.01846527,0.0881551,0.023282954,0.02407765,-0.021419901,-0.052844413,-0.057149835,0.035889238,-0.06611917,-0.03304369,-0.0038601274,-0.010329248,-0.030704644,-0.025422592,0.015678626,-0.010192041,0.0026241855,-0.06570401,0.0386735,-0.021449454,0.0039945394,0.0018016552,0.06404707,-0.037834924,0.015361431,0.035820868,0.013548189,-0.020061629,0.010813476,-0.080586016,-0.012662716,0.014355306,0.0042950176,0.02398638,0.002291268,-0.061877877,-0.027434673,0.011473734,-0.05589278,0.007900245,-0.0025379453,-0.0010503842,-0.033663828,0.03457405,-0.0839663,-0.0072859353,0.006686903,-0.02587096,0.03943579,-0.023357363,0.0132225575,-0.06506905,-0.024695668,-0.034460478,0.054206695,-0.043358583,0.035513528,0.043480128,-0.075555876,0.019553687,0.00373648,-0.029603316,-0.044105772,-0.0031575263,-0.044055574,-0.038619943,0.00782891,0.025259577,-0.00016519903,-0.024896977,-0.044467904,-0.02242181,0.011497261,0.059365977,0.0048645753,0.037662856,0.0008004472,-0.049474124,-0.058721587,-0.028622037,0.049462542,0.035456933,-0.028130135,-0.0068529993,0.048522,0.053278904,-0.04588639,-0.022665212,0.019093337,0.027554527,-0.008791921,0.042237964,0.0023501548,-0.04091718,0.057170365,0.07350747,-0.013535963,0.0016460745,-0.010892136,0.041387267,0.0073145716,-0.021002876,0.07924438,-0.016562631,-0.017282737,0.023546003,0.024466867,-0.0104125105,0.07564398,-0.043403674,-0.014822466,0.061566558,-0.040110014,0.09554875,0.06523874,0.0069798836,0.0138778845,-0.047197133,-0.028059388,-0.02576828,0.028339082,-0.025855735,-0.053795155,0.0452685,-0.019337585,-0.026857873,0.019800462,-0.06200073,-0.0048383735,0.05333931,-0.05637245,-0.019643381,-0.055305146,0.068738155,-0.01777217,-0.04802486,0.031282354,0.10116933,0.014344499,0.05143904,-0.07773487,0.014915841,0.0071254205,0.07491171,-0.054818872,0.023142261,0.01862016,-0.076914996,0.016043931,0.020077033,0.026753308,-0.07798335,0.0010634823,0.0018102673,-0.05645681,-0.048690636,-0.05352347,-0.058791615,0.005754453,-0.017175518,-0.033147976,-0.017986268,-0.05754802,0.02486216,0.011005645,0.021723192,-0.024805455,-0.012398221,-0.053488184,-0.0016582161,0.095824346,0.057880785,-0.011925565,-0.06951116,-0.035675954,0.018201418,0.0027490503,0.019536542,-0.02392407,-0.028274588,0.059953738,0.0075217932,0.05859755,0.019602902,-0.027647864,0.045331594,0.08758071,-0.03903783,0.0008991883,-0.041164633,-0.017632034,0.021135634,-0.029355606,-0.03799007,-0.044123415,-0.022406893,-0.023834782,-0.037259936,-0.0090625975,0.043559454,-0.017275821,-0.0020514948,-0.012313153,-0.06709447,-0.033920445,0.033495676,0.025344322,-0.02703687,0.05914527,-0.040515114,-0.020984795,0.036396675,0.010591823,-0.015537637,0.032743163,-0.015698623,-0.093032286,-0.027397301,-0.05292354,-0.03455177,-0.032670245,0.047753274,-0.000009201002,-0.0086525595,-0.009289823,0.057769027,0.0013047887,-0.021912009,0.028696965,0.0031830731,-0.06521848,0.040834755,-0.01225934,-0.04339203,0.01578772,0.018370172,0.074751906,0.028678605,-0.018899523,-0.018974818,0.07602696,-0.046591874,-0.07151967,-0.049807493,-0.004783435,-0.04832247,0.036782496,0.019666916,-0.017684812,0.011890039,0.058177613,-0.01491755,-0.012897253,0.012377184,-0.013979463,-0.043423362,0.009261439,0.011381596,0.05841236,-0.05620731,0.019313473,-0.05159898,-0.043210216,-0.009764923,-0.042716548,-0.015138612,-0.028753074,0.053186387,-0.009362018,0.00667276,0.050455183,0.0030583218,0.05668929,-0.030108228,0.0056874095,-0.06714546,-0.005936494,-0.009097731,-0.013684524,-0.07218104,0.047813844,0.021108687,-0.024453398,-0.024419306,0.01769675,-0.017923353,0.02319914,0.05485101,-0.0018281317,0.05890795,0.028695107,-0.0008796712,-0.0365842,0.061520744,-0.04011532,0.05308347,-0.0022478553,0.01596459,-0.025080923,0.047913205,0.018482795,0.010764735,-0.025630448,0.005958906,-0.054793105,-0.003357345,-0.09915835,-0.009001933,-0.009160386,-0.02729438,0.032765195,0.023877215,-0.022437565,0.0027423603,0.012220413,0.024224136,0.022476543,0.022859275,0.026956968,0.025589824,0.007237139,-0.0018468604,-0.012824985,-0.09132766,0.020199997,-0.007062766,-0.03937374,0.056977626,0.08233455,0.0046662637,0.05991883,0.007925156,0.056960978,0.0067658825,0.008670272,-0.009282482,-0.0243777,-0.021244686,0.050959025,-0.014493971,0.05784121,0.047668993,-0.01026808,0.0049903328,-0.0062198574,0.015106417,0.036337197,-0.006768556,0.016416425,0.046386473,-0.014226223,0.036543466,0.024089895,0.06876008,-0.016349267,0.002417693,0.05448274,-0.00013331356,0.0043826364,0.003570689,0.0008934393,-0.007832118,0.06476363,-0.018207975,0.0034660893,-0.023757085,-0.017422073,0.013710662,0.0105139585,-0.007439359,0.013416087,-0.0723959,-0.058511976,0.024713874,-0.01514374,0.027039673,-0.043868884,0.0019647002,-0.024056258,0.05238929,-0.0019352341,0.06290568,-0.0002127991,0.04673067,0.017074332,0.025602782,-0.047165036,0.027563458,-0.06294854,-0.017980032,-0.0026671463,-0.022958292,0.05156976,0.014193668,0.007019754,-0.02401676,0.0711194,-0.027221372,0.015132022,0.06521119,0.00892824,-0.028358415,-0.051664464,0.051397186,-0.034284677,0.00071945065,0.012238464,-0.012550517,0.033903535,0.020882096,0.014820261,0.026536591,0.04006452,0.018022435,-0.0066494294,-0.011151431,-0.010657998,-0.015278982,-0.019247217,-0.01935788,0.009664022,0.005962782,-0.05056748,0.030911507,0.009094202,0.07256373,-0.006008701,-0.05452993,0.013959401,0.046187364,0.001764775,0.0030557194,0.021925997,-0.01652124,-0.00045160923,-0.042576477,0.004666856,-0.015066727,-0.011192279,0.0012610876,-0.004231911,-0.022175979,-0.0056705284,-0.04201487,-0.07475623,0.020894047,-0.025921665,0.004365117,-0.02645261,-0.0012483039,-0.033461783,-0.017040063,0.0365573,-0.011513048,-0.040985424,-0.03460206,-0.070109226,0.030308215,0.025099479,-0.011858699,-0.009728175,0.02461942,0.056817107,0.03178885,-0.05261945,-0.051070154,0.045762904,-0.004006687,-0.01782067,-0.01873717,-0.06485187,-0.0119332895,-0.07070995,0.03289475,0.05604506,0.01147361,-0.029904697,0.05008031,0.013030547,-0.023454964,-0.0063940343,0.046514235,-0.06124446,0.010959282,-0.0014555336,-0.010635161,0.06541478,0.008636971,0.022124497,0.073558405,-0.006287686,0.044676945,0.014237689,-0.045458596,-0.07192653,0.0014335056,-0.034481566,-0.000673941,0.038138364,0.017547078,0.046199825,0.001972882,0.013561763,-0.0075250976,-0.025094373,-0.018297339,-0.011091911,0.038423423,-0.023956442,0.060122192,-0.010816973,-0.019276379,0.016470823,-0.020549973,0.026407124,-0.017449258,0.005199127,0.02635739,0.036444783,-0.079273835,0.02361971,0.08673698,0.023573963,-0.006196084,0.013776036,0.049522366,0.06434371,0.033796996,0.010871252,-0.018428328,0.005839903,0.024055216,0.013894633,0.0248045,0.013072389,0.007003147,-0.012113282,0.071229525,-0.018808123,0.034572985,-0.013807104,0.02291531,0.009581817,-0.0059639923,0.027361196,0.032907862,0.03169079,-0.012552481,0.0018176758,-0.0311307,0.010866927,-0.035212163,0.009595629,-0.0015748675,-0.079096384,-0.04021995,-0.042791754,-0.006195504,-0.024189556,0.027992617,-0.034504954,-0.032296035,0.034345597,-0.05523635,-0.01195089,-0.05468412,-0.03167779,-0.010201096,0.038252138,0.02232113,-0.010126561,0.0013312126,0.016190426,-0.0038433468,0.012128832,-0.056527823,0.008796537,-0.05554311,0.02573742,-0.02009165,-0.0046436144,-0.033569857,0.008393546,-0.025719134,0.05206555,-0.011612211,-0.012622758,0.027326679,0.029835664,0.0064352197,0.059241794,0.0035549998,0.061016176,-0.0038147557,-0.04393841,-0.048624173,0.010853687,-0.08594841,-0.013710871,-0.011255648,-0.016551705,0.023575496,-0.055739347,-0.051346403,-0.043147158,0.00068641914,-0.033647448,-0.053206947,-0.00078493,0.0500377,-0.031482413,0.02399191,0.006707977,-0.022227589,0.011126066,-0.007125125,-0.009426304,0.0060011013,-0.015318223,-0.005792539,-0.0062310128,-0.0061858334,0.025585908,-0.0415168,-0.009181717,-0.021924417,-0.0067525306,-0.029811108,-0.022493586,-0.030529011,-0.024797602,-0.009614868,0.0698786,-0.012320381,-0.08013641,0.052991007,-0.06275037,-0.06664242,0.060143594,0.034706656,0.07247887,0.008898153,-0.051152818,0.04375856,-0.057954323,-0.0189365,0.052274227,-0.036985468,-0.003912335,-0.022876525,-0.0052631646,-0.07390624,0.020087436,0.0061092414,-0.048835773,-0.042404246,-0.022570893,0.020425064,0.000306143,-0.04593131,0.017665962,-0.0689379,0.0056318967,-0.010216948,-0.019939562,0.009541889,0.035829194,0.029661447,0.003815615,0.011508993,-0.026837578,-0.025104623,0.021670774,-0.014060308,0.0050376025,0.008918897,0.06857615,0.018303392,-0.0021675965,0.0053750323,-0.016027985,-0.06404527,0.041056924,0.005806868,-0.038187854,0.09383642,-0.01375992,0.0108819,-0.0070816963,0.020348543,0.0057799737,-0.053124253,-0.03701864,-0.012564851,0.04409775,-0.00709908,0.04922465,0.01400489,-0.03561201,0.018648556,-0.0022009201,0.010308903,0.07368374,-0.016195232,0.02293873,0.0068247253,0.01379926,-0.024148801,0.05299712,-0.009252129,0.022912053,0.016945587,0.03526844,0.0057444912,0.03770162,-0.009795206,-0.03016866,-0.002763111,-0.026207425,-0.0013235804,0.02565899,0.03309756,-0.04889563,0.029624064,0.0065701115,-0.058176693,-0.030553374,0.0077775735,-0.045097902,0.018817276,0.007694295,-0.037248556,-0.035228502,-0.017423226,-0.027170466,0.027568024,-0.02301862,0.011253318,0.0066083902,0.011214136,-0.047569126,-0.012590069,0.015272974,0.015109973,0.03322268,-0.06970835,0.035190586,0.03434372,0.023311751,-0.0037527676,-0.092639856,0.018762438,0.021708226,0.009553598,-0.070271365,-0.035076,0.027998883,0.014099061]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "f673e77536042977": {"spanId": "f673e77536042977", "traceId": "d54eca2a1969adf418f7dda9a08c8d42", "startTime": 1752607524912, "endTime": 1752607525791.2512, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"session_type\":\"no_preference\",\"working_hours\":\"flexible\",\"gender\":\"no_preference\",\"works_with_gender\":\"prefer-not-to-say\"}}}", "genkit:output": "{\"documents\":[]}", "genkit:state": "success"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": [{"time": 1752607525305.311, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752607525355.388, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752607525786.568, "annotation": {"attributes": {}, "description": "Firestore.runQuery: First response received"}}, {"time": 1752607525790.559, "annotation": {"attributes": {"response_count": 1}, "description": "Firestore.runQuery: Completed"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752607524912, "endTime": 1752607525791.2512}