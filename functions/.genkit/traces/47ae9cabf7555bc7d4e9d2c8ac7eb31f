{"traceId": "47ae9cabf7555bc7d4e9d2c8ac7eb31f", "spans": {"7f31000fbb0466d0": {"spanId": "7f31000fbb0466d0", "traceId": "47ae9cabf7555bc7d4e9d2c8ac7eb31f", "parentSpanId": "4171f525534aa845", "startTime": 1752608440111, "endTime": 1752608440510.1301, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 4/10\\n      Areas of concern: relationships, physical health\\n      Additional information: I need a personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.026471464,-0.**********,-0.048426505,0.**********,0.016879937,0.060726576,0.05275276,-0.007947898,-0.00483064,0.044975217,-0.009153229,0.08119629,0.033280004,-0.013913891,-0.022414567,-0.04407593,-0.046263136,0.047244005,-0.07442225,-0.0251873,-0.010281628,-0.0407316,-0.03276101,0.015413784,0.00455691,-0.**********,0.027290221,-0.02109485,0.025027206,0.022012541,0.**********,0.021135243,0.05530654,0.**********,0.02388149,0.029605549,-0.007045868,-0.012867484,-0.009582985,-0.06653413,-0.015560825,-0.030822061,0.**********,0.03256827,-0.**********,-0.06152731,0.013647539,-0.01881533,-0.043129962,0.020390011,0.**********,0.030946564,-0.04503401,0.033759817,-0.03674059,-0.017324345,0.017659059,-0.02480945,0.056013785,-0.026463306,0.025704684,-0.03667707,-0.016197167,-0.052116573,0.05273716,-0.022016104,0.043615542,0.030758318,-0.071832865,0.01797188,-0.021818995,0.024226911,-0.05984629,0.012796452,-0.014912552,-0.04391627,-0.026762143,0.028477428,0.03906021,-0.04440179,-0.027525121,-0.03918021,0.028786441,0.05169113,-0.028005918,0.04831719,-0.012458918,-0.04113099,-0.057872273,-0.015143702,0.042810902,0.02502629,0.004240879,-0.015666367,0.051094875,0.031538717,-0.04266544,-0.020258117,0.0103493305,0.013990226,0.025723815,0.046799663,-0.006606843,-0.056180265,0.048225813,0.076696,-0.044828836,-0.013299676,-0.0059642694,0.068044536,0.022460489,-0.0027699869,0.02671699,-0.032946832,0.029720508,0.01043492,-0.026878413,-0.01924478,0.025563126,0.0062685935,-0.013401825,0.090586096,-0.06934018,0.10235077,0.041545723,0.007160659,0.0056625092,-0.013959206,-0.04322473,-0.0407471,0.049454242,-0.052478988,-0.0052277157,0.018724553,-0.0017860999,0.0071334587,-0.004924268,-0.07678385,-0.013472045,0.06084907,-0.03579078,-0.032833867,-0.066850275,0.04483791,-0.009687514,-0.028517416,0.03629637,0.09969287,0.019020744,0.03632293,-0.082890354,0.010651906,0.060480025,0.06413003,-0.04063111,-0.013098891,0.02284459,-0.05286333,0.037642833,0.017653251,0.008446975,-0.05692229,-0.009251251,0.018265475,-0.04307388,-0.04184411,-0.08015333,-0.042544592,0.004477559,-0.023917487,-0.014428441,0.00055911386,-0.03660291,0.009305246,0.0022340482,0.015270463,-0.013839706,0.034368582,-0.03304388,-0.00645692,0.06258255,0.028323185,-0.005530036,-0.08153973,-0.025473867,-0.010056589,-0.043395314,0.011396443,-0.030024279,-0.001781892,0.041366693,-0.00856811,0.0443987,0.036026757,-0.019205976,0.06762784,0.07249063,-0.030555373,0.044152495,-0.03981962,0.01780448,-0.011506817,-0.036470864,-0.032401968,-0.04898849,-0.0076267077,-0.050303016,0.0060724365,-0.018114312,0.031889852,-0.03159711,-0.014488716,-0.025810484,-0.082539566,-0.010014863,0.03489628,0.012595457,-0.032569487,0.04108492,-0.058494177,0.008996032,0.028878622,0.0015008507,-0.01984625,0.060282785,0.00091822806,-0.097016394,-0.011498067,-0.059812684,-0.016332079,0.019227834,0.04885628,0.009718949,-0.03356248,-0.025465293,0.027650176,0.0037676848,-0.02018726,-0.0017144426,-0.046749637,-0.05558324,0.017719982,0.01485513,-0.025198042,0.028620265,-0.005466498,0.054719042,0.007994519,0.0031565719,0.013953516,0.07806078,0.0045630736,-0.036466148,-0.01162628,-0.0114762215,-0.04292573,0.029403588,-0.011865021,-0.02447884,0.017848015,0.045381863,-0.0015343105,-0.013443885,0.008075558,-0.018441427,-0.031260423,-0.0011509319,0.02695884,0.056635078,-0.04409403,0.007882633,-0.036234945,-0.029222647,-0.01932935,-0.045299403,0.0012871344,-0.0247165,0.0121239405,-0.05569484,0.029482149,0.04951227,-0.004003165,0.038676657,0.01845434,0.019459253,-0.087010935,-0.00088853587,0.016723853,0.011704094,-0.06358219,0.048795715,0.03572209,-0.047125656,-0.05799824,0.017982574,-0.030117292,0.041780073,0.054621443,0.017321523,-0.00077748083,0.010331205,0.017925192,-0.022178331,0.061842654,-0.03268349,0.032342862,-0.00910869,0.001462512,-0.03346149,0.020462852,0.0193067,-0.01181509,-0.013924301,-0.02287165,-0.051662344,0.004107665,-0.02355492,-0.016794922,0.011142768,-0.026411995,0.034575902,0.02915707,0.00027702813,0.009040024,0.022065163,0.031485002,0.029178863,0.03755019,-0.00421481,0.041612234,0.040123723,-0.014418641,-0.015599178,-0.10287002,0.0104233585,-0.041001663,-0.027288817,0.034650713,0.07157656,0.015608025,0.043561764,0.016484525,0.07357392,0.009449353,-0.0028424184,0.028995793,-0.005933763,-0.03258335,0.035269342,-0.024824617,0.058442887,0.087651305,0.015003184,0.016780738,0.021342346,-0.0023628348,0.05469378,0.0028658526,0.02666691,0.044302233,0.008884078,0.055350743,0.02970916,0.03630242,-0.028314192,-0.041378833,0.04114195,0.0026217264,0.013574192,0.009078609,0.0065761814,0.0049985787,0.04384108,-0.01443701,0.02066313,-0.018409245,-0.04153066,0.03433995,0.017441731,-0.012210509,0.01377852,-0.04815211,-0.059652723,0.015861416,-0.0077189016,0.026233101,-0.07581674,0.033799723,0.0011409303,0.037385754,-0.0032343687,0.043183967,-0.0024230878,0.024075385,0.004334156,0.021926412,-0.01182112,0.055652633,-0.03767192,-0.012819748,0.0036653834,-0.015393323,0.05518505,-0.00891291,0.029341519,-0.033774804,0.061263274,-0.036874022,-0.01929347,0.056569677,0.01313584,-0.014689634,-0.06744132,0.06733281,-0.013591078,0.0024736281,0.007792018,0.0071779485,0.03206386,0.027763277,0.05197194,0.05419537,0.057343937,-0.0042800177,-0.007712997,-0.027257463,-0.03356146,-0.001323376,-0.0065644877,-0.036014903,0.043446746,-0.011835586,-0.0154648805,0.06598871,-0.023053562,0.052029897,-0.0033960203,-0.02312032,0.0054247375,0.04356577,-0.008655384,-0.03416143,0.022976724,-0.02364455,0.008077247,-0.042362824,0.034341205,-0.030000027,-0.025473515,-0.032888494,0.039429758,-0.024476863,-0.0068970053,-0.042734694,-0.09694348,0.022118835,-0.022705255,-0.013836773,-0.0047824658,-0.033163477,-0.05817009,-0.041540045,0.04272825,-0.006929314,-0.03406527,-0.017256513,-0.06239232,0.051273964,-0.026098909,-0.029090302,0.0050137467,0.04117405,0.045434766,0.056621,-0.08529074,-0.046465557,0.06589211,-0.037546422,-0.02024662,-0.031144176,-0.06685697,-0.019456664,-0.038455956,0.055780705,0.06706406,0.015764304,-0.008981025,0.031602576,-0.016616225,-0.0613821,-0.020956062,0.049805492,-0.06720376,-0.010336611,0.0027402102,-0.0072538545,0.042032756,-0.0028093893,0.013172238,0.06550515,0.018301575,0.025046788,-0.0039963825,-0.051051084,-0.06563045,-0.018593974,-0.029773103,0.015329467,0.0144082885,-0.0018355689,-0.010432355,-0.026200458,-0.0005225182,-0.020228522,-0.062359538,-0.027564969,0.0060938,0.020808093,-0.031447083,0.047515295,0.005211059,-0.056820627,0.00454242,0.009453292,0.040610347,-0.013919081,0.020841481,0.0024987576,0.03239448,-0.065480255,0.007940683,0.06682691,0.043562245,-0.00045375657,0.015720233,0.06385685,0.064340815,0.037087426,0.04794007,-0.020507248,0.038149603,0.01625435,-0.0052820407,0.031085826,0.0129567245,-0.034244485,-0.007406354,0.100714475,0.0365498,0.013253227,-0.016189998,0.0106959585,0.014558332,-0.02343829,-0.025015447,0.007323202,0.04063292,-0.00021151235,-0.009206959,-0.013551164,-0.0010595095,-0.027446551,-0.0021443402,-0.013503215,-0.05488757,-0.04490382,-0.057340346,0.022927288,-0.03587365,-0.0003534826,-0.023983503,-0.0528685,0.0185908,-0.07007659,-0.0053142225,-0.036686942,-0.03572591,-0.028032316,0.026584053,0.018184017,-0.021309394,0.030848539,0.014386606,-0.017091544,0.04651105,-0.048285335,0.0337661,-0.041843038,0.0039290087,-0.025430556,0.031201387,-0.053376794,0.029547876,-0.02111168,0.07854945,0.0023922713,-0.031550866,0.014639706,0.029976249,0.012295467,0.061211217,-0.011890564,0.050423287,0.010058211,-0.042424455,-0.08547129,0.025664208,-0.045262687,-0.028306812,-0.025428306,-0.0037353244,0.01604296,-0.041367233,-0.070568986,-0.066203855,0.019187786,-0.0063573606,-0.050296243,0.007535105,0.038929958,0.0007601213,0.017597077,0.020563291,-0.0157024,0.03108373,-0.007894627,-0.027680127,0.000766286,0.033344183,-0.003959893,0.003549432,-0.029213866,0.013615616,-0.027557936,-0.0075652367,0.013437236,0.012095134,-0.0026862384,-0.030649599,-0.04472587,-0.01762034,-0.0013455866,0.056901775,-0.031724498,-0.086398825,0.0341537,-0.06473916,-0.09236109,0.05826516,0.020746369,0.06863351,0.035308693,-0.03910807,0.0415872,-0.036376126,0.008018701,0.047743067,-0.04543651,0.0037564896,-0.040905975,-0.022148294,-0.02116458,0.017895913,0.03523217,-0.07228593,-0.043704323,-0.03875517,-0.0052294587,0.01566076,0.012050995,0.028738778,-0.032219406,0.007547918,-0.027832815,-0.030458815,0.013612051,0.025259444,0.03333976,0.00012165205,0.013903384,-0.019308077,-0.025677089,0.045382813,0.006007916,-0.029243786,0.021429466,0.046093576,-0.0101583805,0.012025238,-0.020945983,-0.02408191,-0.070607565,0.0078052413,-0.005022205,-0.033592843,0.07912334,0.016146945,0.017706374,-0.0059823818,0.0135531295,0.015332322,-0.05842047,-0.03936028,0.005712922,0.047227982,0.016064866,0.058718648,0.016359461,-0.052850854,0.005639303,-0.014289807,0.0050386926,0.052760746,-0.02775429,0.012358778,0.01726852,0.00834946,0.0048452597,0.05358792,0.029965052,0.017316772,0.007996914,0.0064050094,-0.015308213,0.057440232,0.008551737,-0.007929417,0.014079576,-0.020059787,0.0034548314,0.016004635,0.0434507,-0.06305996,0.039116386,0.03456874,-0.051218655,-0.044606805,0.023946144,-0.037025087,0.019786786,-0.008957743,-0.035918865,-0.06300861,-0.007377123,-0.013854066,0.018811729,-0.032099348,0.031187601,-0.02173048,-0.0005655555,-0.06249496,-0.018768143,-0.026653707,0.010817956,0.010282273,-0.03173893,0.020623388,0.060515303,0.04253542,0.03873786,-0.100415796,0.030751564,-0.0084917825,-0.03659652,-0.06410397,0.009945375,0.025894273,-0.01925477]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "4171f525534aa845": {"spanId": "4171f525534aa845", "traceId": "47ae9cabf7555bc7d4e9d2c8ac7eb31f", "startTime": 1752608440103, "endTime": 1752608440959.5083, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 4/10\\n      Areas of concern: relationships, physical health\\n      Additional information: I need a personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"online_or_offline\":\"no_preference\",\"working_hours\":\"flexible\",\"session_type\":\"in_person\",\"location\":\"Cardiff\",\"works_with_gender\":\"male\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=location --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752608440515.2485, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752608440567.31, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752608440959.4048, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=location --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=location --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=session_type --field-config=order=ASCENDING,field-path=working_hours --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752608440103, "endTime": 1752608440959.5083}