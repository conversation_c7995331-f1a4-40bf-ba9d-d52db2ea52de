{"traceId": "3ba274ac08470680eed391ba12b7c5e8", "spans": {"e032903815e14fce": {"spanId": "e032903815e14fce", "traceId": "3ba274ac08470680eed391ba12b7c5e8", "parentSpanId": "1e27b9ff73e26d19", "startTime": 1752607526495, "endTime": 1752607527754.079, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "googleai/gemini-1.5-flash", "genkit:path": "/{generate,t:util}/{googleai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}", "genkit:output": "{\"candidates\":[{\"index\":0,\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"There is insufficient information provided to identify a therapist who works with \\\"prefer-not-to-says.\\\"  The context only gives a user mood score and areas of concern which are both blank.  More data is needed.\\n\"}]},\"finishReason\":\"stop\",\"custom\":{}}],\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"There is insufficient information provided to identify a therapist who works with \\\"prefer-not-to-says.\\\"  The context only gives a user mood score and areas of concern which are both blank.  More data is needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.182531564132027}],\"usageMetadata\":{\"promptTokenCount\":68,\"candidatesTokenCount\":46,\"totalTokenCount\":114,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":68}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":46}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"Jqt2aKShJ9P0hMIPk9rPoQU\"},\"usage\":{\"inputCharacters\":269,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":211,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":68,\"outputTokens\":46,\"totalTokens\":114},\"latencyMs\":1258.5904580000001}", "genkit:state": "success"}, "displayName": "googleai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "1e27b9ff73e26d19": {"spanId": "1e27b9ff73e26d19", "traceId": "3ba274ac08470680eed391ba12b7c5e8", "startTime": 1752607526454, "endTime": 1752607527812.7476, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"googleai/gemini-1.5-flash\",\"docs\":[],\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]}],\"config\":{}}", "genkit:output": "{\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"There is insufficient information provided to identify a therapist who works with \\\"prefer-not-to-says.\\\"  The context only gives a user mood score and areas of concern which are both blank.  More data is needed.\\n\"}]},\"finishReason\":\"stop\",\"usage\":{\"inputCharacters\":269,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":211,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":68,\"outputTokens\":46,\"totalTokens\":114},\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"There is insufficient information provided to identify a therapist who works with \\\"prefer-not-to-says.\\\"  The context only gives a user mood score and areas of concern which are both blank.  More data is needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.182531564132027}],\"usageMetadata\":{\"promptTokenCount\":68,\"candidatesTokenCount\":46,\"totalTokenCount\":114,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":68}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":46}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"Jqt2aKShJ9P0hMIPk9rPoQU\"},\"request\":{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with prefer-not-to-says.\\n      \"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}}", "genkit:state": "success"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generate", "startTime": 1752607526454, "endTime": 1752607527812.7476}