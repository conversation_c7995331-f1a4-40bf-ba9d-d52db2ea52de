{"traceId": "82870fefd2a11b0a3d0ae6da2c3c1b87", "spans": {"251ece68aa133f46": {"spanId": "251ece68aa133f46", "traceId": "82870fefd2a11b0a3d0ae6da2c3c1b87", "parentSpanId": "a05d8398d5606aec", "startTime": 1752609022223, "endTime": 1752609022630.6897, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, work life, physical health\\n      Additional information: coaching and personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.012176036,0.014417618,-0.049560416,-0.011568768,0.023897722,0.05580061,0.06618575,0.00019757985,-0.**********,0.035149246,-0.006190073,0.06834793,0.011828074,-0.**********,-0.024064194,-0.049136855,-0.040094417,0.038299326,-0.07922762,-0.04735318,-0.004616745,-0.024745293,-0.0342494,-0.014800587,-0.017181627,-0.011334715,0.01727671,-0.016963724,0.03549558,0.014449316,0.020571275,0.033946127,0.043479342,-0.010320638,0.037366975,0.032702327,-0.011776016,0.004222002,-0.016968785,-0.08853241,-0.026107512,-0.010180456,0.014646754,0.030491067,-0.014929628,-0.051930692,-0.01315456,-0.008253016,-0.055293523,0.009072407,0.014864355,0.023760313,-0.027144348,0.054163408,-0.05533153,-0.020985447,0.**********,-0.**********,0.046029553,-0.032434873,0.015495005,-0.02503839,-0.015198225,-0.06770218,0.093902364,-0.021461355,0.03784957,0.030136237,-0.073075995,0.029988876,-0.010882594,0.017418504,-0.070629865,0.0012568368,-0.0400434,-0.059793185,-0.009779814,0.017095167,0.042429272,-0.042099383,-0.041494954,-0.045761887,0.0020068851,0.05939325,-0.011123218,0.040867563,-0.01677518,-0.043684978,-0.059246786,-0.018936476,0.047396127,0.04352288,0.00029162475,-0.041841287,0.06888989,0.01805864,-0.05570278,-0.016735105,0.026384344,0.02314125,0.0055073267,0.027386146,0.01468574,-0.03908097,0.042212434,0.07392617,-0.04207993,-0.014818148,-0.006338524,0.060332224,0.023650814,-0.012384073,0.04217742,-0.041734125,0.008710396,0.032453645,0.017162753,-0.008032435,0.04722157,-0.012512891,-0.010585567,0.04025869,-0.06456962,0.1040053,0.05524216,-0.004211204,0.0029437083,-0.045934744,-0.02261791,-0.022070894,0.053919636,-0.046427697,-0.02012939,0.019538581,-0.0013946163,0.0050924057,0.026318468,-0.04866758,-0.009938622,0.06913342,-0.048513517,-0.0052954517,-0.058486674,0.045622453,-0.010141373,-0.048237976,0.037880246,0.079702586,0.011956433,0.0421892,-0.0924569,0.03161581,0.03996444,0.07098253,-0.047588993,-0.0024639897,0.027093401,-0.057187736,0.032377742,0.015762331,-0.006974647,-0.0592373,-0.030710792,0.035102352,-0.052863903,-0.049685456,-0.06088134,-0.04317148,0.008716216,-0.011865791,-0.005511585,-0.013584688,-0.07211569,0.024809705,-0.020044822,0.005138619,-0.032298896,0.019806104,-0.02469746,-0.005030248,0.07497654,0.011769818,-0.0043451907,-0.086156406,-0.016758325,0.020569557,-0.04971111,0.006370892,-0.017795194,-0.0003094447,0.035274126,-0.009239755,0.037130546,-0.00068012177,-0.025409918,0.046975035,0.075589046,-0.031333543,0.035610147,-0.03246164,0.0001570027,0.00290682,-0.028170748,-0.009667814,-0.051540587,-0.008112557,-0.026621966,0.0062226593,-0.0050662695,0.050001916,-0.026008165,-0.033656828,-0.01011098,-0.08911725,-0.024020635,0.04034172,-0.0001237982,-0.037090193,0.054241523,-0.07426183,0.004021793,0.025354363,0.0024261302,-0.005386086,0.060308177,0.0014276572,-0.0790907,0.0047380975,-0.07169623,-0.03890809,0.012171836,0.06386772,0.005261937,-0.014536687,-0.0059199776,0.019481758,0.018932816,-0.02872965,-0.008686473,-0.029401049,-0.04939215,0.034993075,-0.013725563,-0.025205204,0.028783668,0.018096283,0.07871377,0.0048274486,0.0022182327,0.00094146,0.059167027,-0.015297508,-0.060633335,-0.0054856674,0.0030561152,-0.019264925,0.0076410766,-0.0011202919,-0.01083183,0.016465288,0.038925804,-0.013326531,-0.034261122,0.0063002417,-0.0146776,-0.052998256,-0.025675135,0.016503677,0.04770025,-0.0423099,-0.00053083414,-0.03161639,-0.032407545,-0.02167118,-0.024965174,-0.006525475,-0.022818336,0.044270683,-0.062077057,0.029046116,0.0502661,0.0004962045,0.044905543,-0.014054474,-0.0071823345,-0.07444834,0.00821978,-0.01402079,-0.027019437,-0.0670219,0.046874452,0.03524111,-0.03070457,-0.052796785,0.025090722,-0.031096304,0.05316267,0.06333588,0.004674077,0.011818154,-0.0029971953,0.02242204,-0.026032679,0.060096234,-0.051294204,0.017765168,-0.024193414,0.007962533,-0.035152365,0.034185275,0.042441864,-0.010236319,0.0049025062,0.0019610964,-0.036957644,0.004120436,-0.056624036,0.0057578334,0.011919342,-0.042910986,0.057440605,0.012000868,-0.024186162,0.027817015,0.03307764,0.012011958,0.034959693,0.023723256,-0.011921957,0.0379037,0.02115795,-0.013097768,-0.0032862218,-0.078867696,0.023531545,-0.020813247,-0.03051122,0.058295675,0.071010716,-0.0032384596,0.033184245,0.004768502,0.072399035,0.010138571,0.015299589,0.0064734486,-0.059632964,-0.010279777,0.047024425,-0.018356336,0.06659661,0.069457784,-0.0064274627,0.012346571,0.0050732964,0.0032847202,0.06320033,-0.008180803,0.02184934,0.032959893,-0.005171664,0.033634216,0.016073408,0.050060447,-0.030142907,-0.01672235,0.052963812,-0.0027558147,0.010347057,0.014050934,0.01781927,0.014447247,0.076276064,-0.018866414,0.036523312,-0.009823383,-0.00876936,0.03755027,0.018466685,-0.020900665,0.010651754,-0.04947305,-0.050562035,0.025288176,-0.035542384,0.04924969,-0.058110934,0.04004769,-0.009189302,0.037216708,-0.008223121,0.04269862,0.041048862,-0.005053992,-0.009244808,0.01967371,-0.012518159,0.03880658,-0.03687541,-0.019771118,0.008567112,-0.009832613,0.062750556,-0.010816912,0.040130503,-0.025496723,0.076655194,-0.020937642,-0.005760206,0.064134695,0.022603502,-0.033994075,-0.055679116,0.04611733,-0.0042695603,0.008045045,0.023401711,0.007873132,0.028447839,-0.0010375504,0.02516549,0.041403275,0.0480056,-0.015710583,-0.010747417,-0.026756875,-0.026590012,-0.006483493,-0.010057545,-0.04530603,0.026424011,-0.015514083,-0.047227982,0.022716217,-0.012833137,0.05835113,-0.0104413545,-0.02981646,0.008707859,0.03023482,0.0019614412,-0.027760185,0.009936717,-0.02920921,0.00058727287,-0.040823698,0.0171885,-0.026653804,-0.030011777,-0.01982939,0.03412893,-0.042792138,-0.0042435974,-0.05111139,-0.09585827,0.022236682,-0.025889339,-0.017098235,-0.008320424,-0.027454073,-0.059297267,-0.022236884,0.03143594,-0.017424904,-0.041896418,-0.0018373886,-0.07548407,0.020872792,-0.017813047,-0.042990472,0.012016889,0.052093502,0.03018037,0.041552335,-0.071848854,-0.033455838,0.069145545,-0.02456703,-0.02657994,-0.033257876,-0.08148028,-0.011174266,-0.036775883,0.0661436,0.06846673,0.01944442,-0.02777439,0.048432846,0.0023114118,-0.05409814,-0.00766447,0.056828383,-0.054185856,0.0008720638,0.013595007,-0.024794694,0.029861232,0.011594316,0.009670282,0.06383256,0.0008963432,0.0488164,0.0023086194,-0.039335214,-0.05313647,-0.017150698,-0.041144956,0.03094638,0.025371509,0.0128855575,-0.0038026609,-0.022536436,0.00500905,-0.042097107,-0.05316363,-0.018709831,0.00013829564,0.018653017,-0.020753382,0.058785364,0.025427708,-0.03387788,0.007569225,0.0030039882,0.03868333,-0.0073164743,-0.0044373223,-0.0014754202,0.019137515,-0.06854691,0.0058476813,0.06568168,0.05815042,-0.007382555,0.026278486,0.057021234,0.060419433,0.020671496,0.025220798,-0.030876853,0.02966107,0.01204124,0.0112704,0.015850237,0.0051506143,-0.02155562,-0.010699436,0.100561865,0.030030174,0.020087825,-0.00727839,0.0052908696,0.014331362,-0.015960047,-0.021163499,0.023204919,0.03630162,0.018369203,-0.022590674,-0.02806203,0.014268171,-0.01497666,-0.0002770474,-0.0026615309,-0.0478285,-0.041956525,-0.05704548,0.0120286355,-0.016612664,0.026882121,-0.019527562,-0.053033892,0.03193752,-0.07004553,0.0032288574,-0.06359532,-0.015411662,-0.024618609,0.029063022,0.012515169,-0.005856128,0.003327439,0.04882934,0.011953859,0.040643208,-0.038774855,0.036654305,-0.041620415,0.007664934,-0.044697944,0.029504878,-0.038169168,0.01028479,-0.033214774,0.0698732,0.021165408,-0.035012007,0.010847358,0.018592937,0.02307746,0.060703643,-0.006458445,0.06660144,0.005727905,-0.035001114,-0.0883594,0.00095022464,-0.052840654,-0.01794339,-0.023274327,-0.010792047,0.03544049,-0.03485048,-0.041974455,-0.058279205,0.03058616,0.00044109498,-0.040144924,-0.012965776,0.049238868,-0.018791156,0.029271115,0.011353085,-0.033960607,0.028894199,0.014848104,-0.014156148,0.0057006776,0.048381716,-0.0016192297,-0.017404545,-0.0119982315,0.0024699871,-0.028961468,-0.0018560775,0.00007368924,-0.0015110245,-0.014094364,-0.035085697,-0.024262467,-0.028934438,-0.024223298,0.041965842,-0.006289043,-0.07067213,0.034120534,-0.07505344,-0.07801667,0.059419323,0.021385834,0.06161003,0.06936703,-0.03374572,0.034381177,-0.05532111,-0.008343656,0.048448015,-0.043048665,0.015923338,-0.00301012,-0.005566473,-0.07160049,0.017071024,0.042502146,-0.081823796,-0.030458124,-0.026124474,0.00030460214,-0.0031410642,-0.025958097,0.013484397,-0.02650324,0.0056469073,-0.019799516,-0.017539563,-0.001599088,0.028628238,0.045356613,0.010335838,-0.0020209958,-0.008598508,-0.017649034,0.055526894,-0.007971555,0.00066140277,0.015901612,0.046240926,0.003298542,0.0248754,0.0020858527,-0.017850732,-0.06597659,0.013848481,-0.0047330805,-0.033848286,0.078574315,0.008420515,0.017630938,-0.011755972,-0.0052937623,0.0067763184,-0.060804304,-0.027330333,0.0034527476,0.050762586,-0.0019965954,0.070298925,0.006703287,-0.055705994,0.018152863,-0.0058465046,0.00813662,0.06515502,-0.027259475,0.022328388,0.008948999,0.022502832,-0.0036160268,0.05924908,-0.0019508175,0.0065443437,-0.012408313,0.05009783,0.016162785,0.05259459,-0.0027301204,-0.010167628,0.029316016,-0.016054893,0.006974133,0.014956596,0.03657438,-0.069572,0.028996985,0.032368366,-0.025322117,-0.05043041,0.024770178,-0.027277322,0.0066418396,0.0049825567,-0.043055803,-0.07210714,-0.017143423,-0.025513573,0.02578781,-0.038090706,0.00640206,-0.012053082,0.0039602756,-0.06433004,-0.046316504,-0.012594904,0.009007158,0.02405087,-0.045084804,0.027113672,0.048566137,0.027530713,0.025806382,-0.09116761,0.008014611,0.0026351789,-0.018885562,-0.07821225,0.012450777,0.023392899,-0.025657134]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "a05d8398d5606aec": {"spanId": "a05d8398d5606aec", "traceId": "82870fefd2a11b0a3d0ae6da2c3c1b87", "startTime": 1752609022215, "endTime": 1752609023577.9736, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, work life, physical health\\n      Additional information: coaching and personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"online_or_offline\":\"no_preference\",\"working_hours\":\"flexible\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752609022636.6453, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752609022695.1023, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752609023577.731, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=order=ASCENDING,field-path=working_hours --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752609022215, "endTime": 1752609023577.9736}