{"traceId": "edfe76f812ab083c2149e7bc4b964af0", "spans": {"cd9ac74e2060974c": {"spanId": "cd9ac74e2060974c", "traceId": "edfe76f812ab083c2149e7bc4b964af0", "parentSpanId": "70f85bcee163c046", "startTime": 1752608700503, "endTime": 1752608701139.8079, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "googleai/gemini-1.5-flash", "genkit:path": "/{generate,t:util}/{googleai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}", "genkit:output": "{\"candidates\":[{\"index\":0,\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user mood score and areas of concern are missing, preventing any matching with a therapist.  Additional information is also needed.\\n\"}]},\"finishReason\":\"stop\",\"custom\":{}}],\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user mood score and areas of concern are missing, preventing any matching with a therapist.  Additional information is also needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.21094598525609726}],\"usageMetadata\":{\"promptTokenCount\":62,\"candidatesTokenCount\":39,\"totalTokenCount\":101,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":62}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":39}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"vK92aKGPJ4ahm9IP8qzOmAk\"},\"usage\":{\"inputCharacters\":256,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":214,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":62,\"outputTokens\":39,\"totalTokens\":101},\"latencyMs\":636.4715420000002}", "genkit:state": "success"}, "displayName": "googleai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "70f85bcee163c046": {"spanId": "70f85bcee163c046", "traceId": "edfe76f812ab083c2149e7bc4b964af0", "startTime": 1752608700469, "endTime": 1752608701181.011, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"googleai/gemini-1.5-flash\",\"docs\":[],\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"config\":{}}", "genkit:output": "{\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user mood score and areas of concern are missing, preventing any matching with a therapist.  Additional information is also needed.\\n\"}]},\"finishReason\":\"stop\",\"usage\":{\"inputCharacters\":256,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":214,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":62,\"outputTokens\":39,\"totalTokens\":101},\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"Insufficient information provided to identify the right person to work with.  The user mood score and areas of concern are missing, preventing any matching with a therapist.  Additional information is also needed.\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.21094598525609726}],\"usageMetadata\":{\"promptTokenCount\":62,\"candidatesTokenCount\":39,\"totalTokenCount\":101,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":62}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":39}]},\"modelVersion\":\"gemini-1.5-flash\",\"responseId\":\"vK92aKGPJ4ahm9IP8qzOmAk\"},\"request\":{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: \\n      Additional information: \\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"config\":{},\"docs\":[],\"tools\":[],\"output\":{}}}", "genkit:state": "success"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generate", "startTime": 1752608700469, "endTime": 1752608701181.011}