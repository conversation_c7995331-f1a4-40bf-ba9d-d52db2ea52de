{"traceId": "5e8d94dc51fc8b4d0e12900293fc7b98", "spans": {"27707f75c063ace9": {"spanId": "27707f75c063ace9", "traceId": "5e8d94dc51fc8b4d0e12900293fc7b98", "parentSpanId": "9927bea2a9f39ff2", "startTime": 1752609496988, "endTime": 1752609497398.313, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, physical health, work life\\n      Additional information: coaching & personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.009024676,0.016480979,-0.051456228,-0.01151769,0.023045862,0.055351894,0.06918763,-0.**********,-0.004546524,0.036703255,-0.003873561,0.06791745,0.**********,-0.004571125,-0.023682022,-0.048228603,-0.036748167,0.04000335,-0.077673584,-0.04621855,-0.006021708,-0.024704168,-0.034390528,-0.013485635,-0.017256275,-0.013409475,0.01762813,-0.01476983,0.036762983,0.013446667,0.0177356,0.036084153,0.043112848,-0.010920272,0.03903952,0.033804715,-0.012314122,0.**********,-0.013124314,-0.087329485,-0.02619157,-0.01137889,0.014166659,0.029112305,-0.014062231,-0.04812352,-0.013081433,-0.010787211,-0.052908476,0.009252606,0.013584862,0.025540609,-0.026531179,0.05625437,-0.056842435,-0.021689728,0.01084548,-0.007733003,0.045436986,-0.032844465,0.015800968,-0.026344076,-0.010719907,-0.06925373,0.09223702,-0.023063986,0.03866782,0.029503005,-0.07347997,0.029929752,-0.009905323,0.02005792,-0.06849101,-0.00011977689,-0.038863156,-0.06037115,-0.011641895,0.01683956,0.045328636,-0.042113587,-0.04316403,-0.042247865,0.0031590029,0.058861267,-0.0105616795,0.038512446,-0.016883621,-0.045706116,-0.058894392,-0.02215244,0.047881864,0.043350734,-0.00072362815,-0.039990507,0.067833655,0.017376095,-0.05371098,-0.016511789,0.025532614,0.025041934,0.004498082,0.026416112,0.01611278,-0.0398263,0.043789297,0.07028388,-0.043303043,-0.012346786,-0.0075529623,0.0620361,0.02476196,-0.0105879055,0.04252289,-0.041055065,0.009046108,0.03427882,0.017159352,-0.008538801,0.044839792,-0.013317238,-0.009965528,0.038689505,-0.06492335,0.103090726,0.056933656,-0.003763653,0.0033057404,-0.04420189,-0.02251755,-0.022639994,0.055643067,-0.04411786,-0.021029158,0.01814115,-0.0017949385,0.0027818976,0.02743155,-0.048468284,-0.010149968,0.069682434,-0.049118537,-0.0034803902,-0.060327195,0.045155838,-0.009232318,-0.047639187,0.03624079,0.08376201,0.012008323,0.044738036,-0.09353101,0.03177276,0.040664237,0.07109348,-0.04985793,-0.0015996267,0.02682605,-0.057425883,0.035419367,0.017351875,-0.004477966,-0.05508382,-0.030296372,0.03344355,-0.050687864,-0.05149488,-0.060354147,-0.042475503,0.0100278715,-0.014987263,-0.0061363927,-0.013522835,-0.07063712,0.02121732,-0.020528246,0.0032957357,-0.03169489,0.022959951,-0.02531903,-0.0054655224,0.07562691,0.011119611,-0.0017566918,-0.08669141,-0.017471226,0.02230176,-0.051808912,0.0058300095,-0.018757423,0.0007792752,0.03675465,-0.0109865125,0.035747655,0.002151774,-0.024055742,0.044736397,0.07610286,-0.033661485,0.036482256,-0.034357287,0.0040212683,0.0019206032,-0.028331734,-0.011135006,-0.051752545,-0.006346067,-0.026122866,0.009293258,-0.008049487,0.04801225,-0.026552344,-0.03516962,-0.009824862,-0.08881728,-0.025897471,0.040414676,0.00042093362,-0.03506331,0.053805765,-0.07296288,0.0059931055,0.025750212,0.0025049632,-0.0040509463,0.059442997,-0.00065707695,-0.07737362,0.00515202,-0.0712231,-0.037891716,0.013139724,0.06453526,0.003260258,-0.015820254,-0.0026048103,0.021108603,0.018489985,-0.029839505,-0.007814768,-0.027293239,-0.04878131,0.035822276,-0.016902743,-0.024131069,0.0293971,0.020145332,0.08046146,0.004398505,0.003403918,-0.00068053097,0.05832577,-0.013348516,-0.056689553,-0.0047571603,0.005343539,-0.016799713,0.009329924,-0.0032768722,-0.008189879,0.019059734,0.03862648,-0.012583934,-0.035327002,0.009605899,-0.016386587,-0.05140998,-0.025088064,0.018103346,0.04852827,-0.041759044,-0.0013919155,-0.032619257,-0.032573096,-0.024025755,-0.02466001,-0.008487717,-0.022497743,0.044883832,-0.06260888,0.025456963,0.04887387,-0.0037795806,0.05007834,-0.013487967,-0.0073366724,-0.072087996,0.009375819,-0.014325392,-0.029334527,-0.06744164,0.046363834,0.03492103,-0.031703446,-0.052827865,0.023608977,-0.028212298,0.05406448,0.06609068,0.0065638684,0.014229696,-0.0015367034,0.020937255,-0.024173874,0.05973213,-0.052193284,0.018740421,-0.027132789,0.007301253,-0.03559173,0.030774504,0.039951026,-0.011219196,0.0034455482,0.001991989,-0.035353344,0.0012973157,-0.055353455,0.006217534,0.010683237,-0.04290047,0.056375235,0.01681284,-0.021068733,0.028779516,0.032374665,0.013414128,0.036291335,0.024257818,-0.0167685,0.035696566,0.01915285,-0.013584417,-0.0058410694,-0.07974951,0.023020927,-0.019188585,-0.031664886,0.05738928,0.07353517,-0.007247393,0.03225634,0.0060548084,0.07307866,0.009816141,0.014901191,0.0055360626,-0.06165789,-0.010229547,0.046851467,-0.017164072,0.06675459,0.06904808,-0.0074929977,0.013343756,0.0059848414,0.0059452215,0.06560852,-0.0073019946,0.021774048,0.031459574,-0.0044703004,0.03241439,0.015367999,0.04880366,-0.031370774,-0.017804164,0.05321744,0.0004689993,0.010559954,0.01449294,0.01835746,0.013584391,0.07518692,-0.020720644,0.035028547,-0.00725317,-0.009048486,0.038535245,0.017425355,-0.020496191,0.0095166275,-0.050222937,-0.05095854,0.02650205,-0.034505464,0.04939516,-0.059067722,0.04085408,-0.01086099,0.035228923,-0.009648745,0.04240587,0.04012956,-0.0048863306,-0.0093328515,0.018969832,-0.01142356,0.03909438,-0.034433942,-0.018229684,0.0068580187,-0.013584415,0.06133143,-0.009803827,0.03959692,-0.02448127,0.07554739,-0.019884793,-0.0063595613,0.0638994,0.022112101,-0.037033867,-0.05600769,0.04519991,-0.0030514498,0.0080178,0.022214085,0.007743969,0.028421056,-0.0021370142,0.026220694,0.041182626,0.04851096,-0.018038219,-0.008430976,-0.031264335,-0.02688179,-0.009367299,-0.012379452,-0.0455463,0.028013071,-0.016257351,-0.04517269,0.021086069,-0.014187487,0.055337712,-0.011526836,-0.031314634,0.011085681,0.032182965,0.0022441128,-0.030254086,0.010719771,-0.029977394,-0.000911697,-0.04367458,0.015027626,-0.030869462,-0.02851399,-0.023295945,0.033395138,-0.04074275,-0.0046414253,-0.05249474,-0.0990796,0.02418806,-0.026962856,-0.016815986,-0.012259443,-0.028519405,-0.058584236,-0.024024248,0.031788528,-0.020683063,-0.04176224,-0.000991577,-0.075075455,0.023155525,-0.01831725,-0.043741133,0.0105051,0.053069253,0.02966875,0.041668814,-0.06808771,-0.03160703,0.0683482,-0.02441027,-0.026477022,-0.033525728,-0.08351353,-0.0123066725,-0.03972195,0.064827815,0.06663902,0.01994303,-0.025110314,0.047146663,0.0044067604,-0.05712901,-0.0072811707,0.056955006,-0.05378427,0.0031314457,0.012776516,-0.024601141,0.028039841,0.013841993,0.008084983,0.06447216,0.00085781614,0.048377197,0.0032433912,-0.036673926,-0.052601382,-0.016600147,-0.04269191,0.032493565,0.02515638,0.016991714,-0.0021997087,-0.02227272,0.0040426007,-0.040750533,-0.054315057,-0.018510142,0.0015126056,0.017970867,-0.01986332,0.058159683,0.022569431,-0.035596218,0.0066444585,0.0031260946,0.038218226,-0.005039221,-0.0056437515,-0.0055355136,0.019663105,-0.06677119,0.00662653,0.0648537,0.054802604,-0.005994824,0.024808725,0.058259062,0.062334582,0.021376446,0.02624905,-0.03249241,0.02975426,0.0146191325,0.009397704,0.015434597,0.0038140346,-0.02341692,-0.010785248,0.099181846,0.029995045,0.017884722,-0.008111742,0.0030039004,0.012287807,-0.017347291,-0.021435296,0.0223824,0.035444323,0.01853007,-0.020795139,-0.026557527,0.016475437,-0.016110605,-0.0030966403,-0.0029416298,-0.049524374,-0.04322701,-0.054325067,0.013106026,-0.017291414,0.02303792,-0.017870907,-0.052361097,0.03207381,-0.072084494,0.005997467,-0.062305234,-0.01676192,-0.023306154,0.029406719,0.012808688,-0.0047500557,0.0029068685,0.050821677,0.01155522,0.041080955,-0.03979788,0.035238665,-0.04192985,0.0066635627,-0.045931194,0.02918513,-0.038035166,0.010199942,-0.03592542,0.07049179,0.024205761,-0.034134407,0.011759088,0.020313002,0.02156308,0.061937504,-0.0050101485,0.0641068,0.0033251215,-0.03459495,-0.08666131,0.0061600064,-0.05197635,-0.016071275,-0.024284545,-0.0106567005,0.035471022,-0.03694826,-0.04021076,-0.0600125,0.031353626,0.0028238147,-0.041859724,-0.015808938,0.049318124,-0.016779205,0.026426723,0.011463331,-0.033683985,0.029796137,0.013931681,-0.011630552,0.0082356455,0.04798514,-0.001524915,-0.018905604,-0.013954271,-0.00008468876,-0.029043688,-0.002483159,-0.0009575424,-0.00082969334,-0.014014737,-0.0365757,-0.024103304,-0.02928683,-0.024011519,0.04370631,-0.009472246,-0.07066463,0.033741303,-0.07252357,-0.07788653,0.058907166,0.01964422,0.06242123,0.071893185,-0.036205478,0.034287002,-0.056883797,-0.009246924,0.04910257,-0.04164781,0.0145936385,-0.0012625625,-0.0063388282,-0.072327316,0.0137364725,0.04360189,-0.082478225,-0.031807743,-0.028394237,-0.0024033615,-0.0033179508,-0.02280301,0.0117185,-0.02803991,0.0064129094,-0.018611152,-0.017153254,-0.0039789975,0.028472811,0.046942223,0.0106610125,-0.0002637726,-0.011137888,-0.016206931,0.05631311,-0.005745063,0.0035852345,0.015467871,0.044501964,0.004690427,0.023474975,0.003654014,-0.015240094,-0.06561535,0.01337197,-0.005856998,-0.031616464,0.080054216,0.008608376,0.01851853,-0.010317291,-0.0043739956,0.0047303857,-0.059757393,-0.02499054,0.0011346142,0.049061496,-0.0013875972,0.071996,0.0074194837,-0.053630315,0.019138148,-0.0059431912,0.0076523772,0.06269833,-0.028976042,0.022491675,0.008082256,0.023973607,-0.0035169513,0.059653006,-0.0021330582,0.006797314,-0.012176237,0.053101636,0.014800728,0.048682027,0.00021692693,-0.009688693,0.03179741,-0.016785339,0.007379367,0.012785131,0.036371104,-0.06747342,0.028464872,0.03034052,-0.020775804,-0.04798509,0.023374785,-0.027421592,0.008435152,0.0046990323,-0.042314097,-0.07292931,-0.018310905,-0.028449727,0.023860218,-0.03647999,0.009747199,-0.013168229,0.005167334,-0.06478077,-0.048956662,-0.012514111,0.0095783435,0.024871383,-0.045138232,0.027373223,0.049041074,0.027785901,0.027587367,-0.09049426,0.006877353,0.0039000898,-0.01719793,-0.079221085,0.0115683675,0.019970339,-0.022552961]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "9927bea2a9f39ff2": {"spanId": "9927bea2a9f39ff2", "traceId": "5e8d94dc51fc8b4d0e12900293fc7b98", "startTime": 1752609496978, "endTime": 1752609498360.619, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, physical health, work life\\n      Additional information: coaching & personal trainer\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"online_or_offline\":\"no_preference\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752609497407.5217, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752609497465.6992, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752609498360.4106, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=online_or_offline --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752609496978, "endTime": 1752609498360.619}