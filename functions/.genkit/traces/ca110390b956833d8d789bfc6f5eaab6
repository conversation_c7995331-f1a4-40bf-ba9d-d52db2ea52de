{"traceId": "ca110390b956833d8d789bfc6f5eaab6", "spans": {"6af9cf1101896208": {"spanId": "6af9cf1101896208", "traceId": "ca110390b956833d8d789bfc6f5eaab6", "parentSpanId": "efbdab4eeda137dc", "startTime": 1752602420173, "endTime": 1752602420575.9385, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: finances, work life\\n      Additional information: I am looking to work with a professional career coach, some body with executive and leadership levels of experience. I would prefer to work with somebody female in this role.\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.044373017,-0.03019698,-0.049769536,0.012537686,0.018248785,0.06604975,0.06541246,-0.0031465197,0.002753508,0.032846417,-0.00004992047,0.07734512,0.017727107,-0.018351706,-0.0439499,-0.061247066,-0.04189776,0.0744533,-0.055448342,-0.024160756,-0.015332749,-0.01945238,-0.019929301,0.004791509,0.002392346,0.016306872,0.01959049,-0.04570479,0.029674673,-0.005837598,0.024113555,0.02187888,0.04530478,-0.03119676,0.017115226,0.009258601,-0.017950082,-0.000411683,0.0004289673,-0.08893856,-0.033045325,-0.013737515,-0.004289784,0.044296976,-0.004052988,-0.055120066,-0.0040620947,-0.008656472,-0.070417196,0.016702497,0.021314982,0.03796494,-0.024855932,0.04720278,-0.042333614,-0.005770492,0.023788445,-0.041495577,0.0592503,-0.028427929,0.039415084,-0.03324706,-0.028798254,-0.05802004,0.06408943,-0.021866444,0.044169173,0.03721935,-0.09317506,0.01957121,0.0010642728,-0.015078637,-0.0611539,0.021978935,-0.042173754,-0.04547971,0.009499569,0.0013546852,0.024599575,-0.028627625,-0.02291374,-0.012983343,0.036664613,0.079777256,-0.021255603,0.047284897,-0.031896368,-0.037039865,-0.04999241,-0.037954196,0.04315564,0.035641585,-0.018449536,0.00069708197,0.08870163,0.043303236,-0.028529378,-0.0034270585,0.03106315,0.014724532,0.020557398,0.07332108,0.010355544,-0.045210876,0.055369347,0.093930095,-0.0402145,-0.02410534,-0.037881676,0.06062692,0.017484285,-0.015661964,0.026159491,-0.050631296,0.0036084214,0.032853425,-0.0071935384,-0.022388363,0.024922095,0.02124525,-0.0312161,0.05274318,-0.05980749,0.10033545,0.056322463,-0.01404175,0.007641991,-0.038137753,-0.02563625,-0.058646094,0.02733052,-0.058344975,0.0046314555,0.048812058,-0.020263242,0.004397328,0.009812747,-0.08959803,-0.019276673,0.044901446,-0.043481488,-0.023843875,-0.07541197,0.05905399,-0.0028964952,-0.037091047,0.010350372,0.07964531,0.017569138,0.02923884,-0.07211233,-0.012436909,0.05222487,0.057289917,-0.0423095,-0.022471938,0.006270117,-0.047995523,0.023317622,0.021560457,0.043780126,-0.06674389,-0.018469194,0.010971037,-0.019341942,-0.031415466,-0.067281246,-0.036066722,0.027746808,0.008206419,-0.023380136,0.0100232605,-0.059073985,0.030839778,-0.014799079,0.028560897,0.014109277,0.012787774,-0.037508264,-0.020521058,0.085140266,0.035518475,-0.016230645,-0.072479926,-0.043293145,-0.004172487,-0.05000628,0.00996861,-0.009337379,-0.014352287,0.05715332,-0.02569009,0.034955893,0.00052122126,-0.043306336,0.063450426,0.036602926,-0.041713197,0.031158807,-0.040317904,-0.012294227,-0.018489901,-0.023944734,-0.033697274,-0.02301693,0.0063747233,-0.035838485,-0.0014927994,0.002741906,0.031057803,-0.053994168,-0.01954253,-0.021461489,-0.055052936,-0.010921415,0.03747669,0.007676701,-0.07061496,0.018064152,-0.028226815,-0.010832092,0.033926558,-0.0044178064,-0.013283266,0.048572958,0.012836935,-0.054043744,-0.022074528,-0.049988754,-0.024766786,-0.013907821,0.026504446,0.017179659,-0.021784242,-0.030618697,0.02409687,0.021753993,-0.0020065077,0.0005187144,-0.05139647,-0.05165341,0.016948545,-0.017332679,-0.006651135,0.029105334,-0.0079880925,0.07844904,0.0008246448,-0.011581112,-0.0007306784,0.03385299,-0.05686444,-0.05682881,-0.01344039,-0.011171281,-0.01526421,0.029233588,-0.007398688,-0.006187376,0.011513244,0.03935943,-0.010044885,-0.019234248,-0.008873851,-0.01779275,-0.033657517,-0.03046522,0.008027945,0.04583171,-0.013597438,0.008170964,-0.04698215,-0.030321497,-0.033742372,-0.04318061,0.008485257,-0.04647769,0.028269196,-0.040645696,0.017339049,0.07741295,-0.011274485,0.045140833,0.023679107,0.0056464584,-0.08963701,-0.020778814,0.018813735,-0.004388557,-0.07044299,0.04520321,0.032701734,-0.049099725,-0.07358083,0.020359807,-0.041398495,0.04343137,0.07388209,0.03550069,0.02094589,-0.012088053,0.0043736096,-0.033648424,0.05964392,-0.05221176,0.037535895,-0.00085557764,-0.023315499,-0.035146303,0.030157916,0.008479922,-0.018441489,-0.029441547,0.008961464,-0.04902462,-0.011930143,-0.063770235,0.00464942,0.0044511133,-0.006603257,0.04117963,0.0009162488,-0.016087912,0.020604352,0.0212938,0.024708185,0.04217399,0.0405626,-0.0057777152,0.01655991,0.03356509,-0.033224043,-0.013800579,-0.089027934,0.025640754,-0.035696454,-0.018677237,0.035183355,0.07912469,-0.015352508,0.03144856,-0.0018634896,0.064968325,-0.015178361,-0.0074464516,0.00951503,-0.026157951,-0.0059581925,0.07062724,-0.018223189,0.06876337,0.06382662,0.014155571,0.00005962422,0.008003493,-0.0006367477,0.05390816,0.016172921,0.02051279,0.043489594,0.0328308,0.043989807,0.01408335,0.045375757,-0.048475113,-0.021023955,0.036819473,0.006645364,0.030239053,-0.000050687784,-0.004495346,-0.0137293115,0.052707292,-0.019246386,0.010548978,-0.015863104,-0.034599382,0.025926573,0.03848215,-0.026853155,0.029409997,-0.041539818,-0.06904508,0.025776453,-0.017826105,0.026007516,-0.057200935,0.021857467,-0.016567959,0.023702536,0.013952662,0.06555539,0.017351873,0.009659354,-0.01971515,-0.0018291991,-0.017233822,0.052983463,-0.052295532,-0.021908538,0.00073818903,-0.014231595,0.039097667,-0.024560684,0.007370812,-0.021502336,0.060808845,-0.025687093,-0.003027929,0.08619857,0.026287336,-0.020722412,-0.10028175,0.0311496,-0.028286979,0.025718004,0.024552738,-0.015844548,0.02908519,0.013455445,0.03983052,0.042706188,0.07878353,0.008185243,0.015658453,-0.017614352,-0.017594872,-0.035934657,-0.023771634,0.006718675,0.019560268,-0.027976338,-0.027158856,0.07339448,-0.0055097425,0.05686362,-0.0141135,-0.0368827,0.01730425,0.03813394,0.007710992,-0.028351424,0.027840301,-0.028614137,0.0019921865,-0.04294324,0.024390383,0.009360995,-0.019713772,-0.007231033,0.034943443,-0.035287164,0.0070585087,-0.042783324,-0.08505771,0.013628351,-0.03361125,0.0026401083,0.011541837,-0.0041451636,-0.039069213,-0.0064468607,0.0392106,0.025285907,-0.032807313,-0.0046943557,-0.048040036,0.033393797,0.0003365355,-0.036820713,0.016962405,0.0342791,0.059105936,0.037080396,-0.076320924,-0.049451176,0.06473196,-0.057568,-0.03552897,-0.013855281,-0.08420828,-0.013122275,-0.026058804,0.013816927,0.059484996,0.012267883,-0.026428776,0.00977103,-0.004521002,-0.045455407,-0.005523908,0.035637528,-0.05140166,0.0011907007,-0.003000264,0.0078000547,0.043845747,0.010123834,0.014326416,0.057235047,0.01885097,0.03504287,-0.008873842,-0.064049274,-0.053787436,-0.004565266,-0.026520994,0.024188085,0.021090198,0.03685072,0.006743991,-0.013597584,0.014164285,-0.028239371,-0.063012384,-0.053570922,-0.012101871,0.020362688,0.00547389,0.049618088,-0.0000069269304,-0.038627233,-0.0005107126,0.016136998,0.02145704,-0.023911376,0.002003278,0.027068049,0.023943614,-0.07618523,0.010791779,0.065750755,0.050989307,0.019839099,0.029973425,0.06988086,0.05337333,0.03641898,0.021719392,-0.039997444,0.038576834,0.013355853,0.025335507,0.017342309,0.017300475,-0.01841621,-0.020287048,0.087708905,0.008321143,0.017158454,-0.014811294,0.029298386,0.026572151,-0.00991521,0.0063558132,-0.0062457323,0.03479666,0.015707156,0.0014520637,-0.02639684,0.015927196,-0.016558606,0.029543119,-0.023630952,-0.05589814,-0.04912667,-0.040207118,-0.0055545466,-0.044047233,0.012296673,-0.0016027451,-0.061640557,0.014728164,-0.05448872,-0.005891586,-0.071005926,-0.038687725,-0.02389409,0.011425792,0.034876656,-0.038716562,0.0244901,0.015793476,-0.016536007,0.03238581,-0.044806413,0.02588998,-0.033933677,0.050189245,-0.033201966,0.022681931,-0.032550424,0.014558364,-0.037297197,0.09025958,0.017520778,-0.005662012,0.016092103,0.032263312,-0.00622545,0.049566984,-0.013157609,0.05879978,-0.0018482124,-0.036941838,-0.08481641,0.0068308427,-0.055471588,-0.021044342,-0.0214538,0.011266641,0.01496122,-0.025008183,-0.05872932,-0.06561022,0.038266916,-0.019243103,-0.04679928,-0.009926253,0.038459428,-0.019673446,0.032926206,0.021197174,-0.02616158,-0.0022945998,0.0056930804,-0.01380317,-0.015232799,0.037997767,-0.00077686267,-0.017497772,0.011609591,0.02055976,-0.03489676,0.011114904,-0.022442762,0.015174476,-0.01774636,-0.034929484,-0.029606352,-0.034523968,0.018529931,0.0495713,-0.00713791,-0.052646972,0.028402193,-0.06845,-0.067207634,0.052721377,0.051066115,0.07163575,0.049418304,-0.035603065,0.042612713,-0.05720677,0.027965045,0.032057043,-0.028919289,-0.008242226,-0.022036461,-0.008193758,-0.046172507,0.0073044943,0.032553617,-0.055172183,-0.017396526,-0.030328983,0.006066645,0.013060767,-0.006017979,0.02103547,-0.035002243,0.004192463,-0.012238384,-0.024691775,-0.0015736943,0.046362523,0.038227767,-0.007327981,0.021272551,0.015220242,-0.039592005,0.051129956,0.0017096074,0.008553045,0.022177463,0.049251158,-0.013440849,0.013716397,-0.016572235,-0.01936852,-0.034489125,0.030988168,-0.007119521,-0.023852082,0.08903895,-0.04495718,0.0075825397,-0.01009001,0.027855141,-0.0034097508,-0.06041827,-0.015601976,-0.0135415755,0.030863589,0.0057897093,0.046296265,0.018350977,-0.055298824,0.0029350205,-0.012499185,0.0018974256,0.051462647,-0.022466658,0.026435591,0.026385115,0.02932074,0.008211553,0.064797744,-0.020482222,0.033993024,-0.017071076,0.045325764,0.011900178,0.03061034,-0.0057866704,-0.001543555,0.022775149,-0.023729108,0.007811292,0.017453117,0.041083977,-0.041394845,0.017347263,0.014039094,-0.027936747,-0.029869264,0.03214879,-0.02867,0.0032616197,0.013605706,-0.041366134,-0.07247436,-0.0141849695,-0.03341695,0.048833255,-0.025990631,-0.0075205374,-0.023160804,0.025695436,-0.07530832,0.0047792145,0.008574034,0.017519848,0.035582107,-0.030596359,0.025878517,0.030828558,0.031229375,0.0074252156,-0.08582981,0.024574459,-0.005901344,-0.026597122,-0.06300334,-0.010204596,0.047983363,-0.008495409]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "efbdab4eeda137dc": {"spanId": "efbdab4eeda137dc", "traceId": "ca110390b956833d8d789bfc6f5eaab6", "startTime": 1752602420166, "endTime": 1752602421031.1426, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: finances, work life\\n      Additional information: I am looking to work with a professional career coach, some body with executive and leadership levels of experience. I would prefer to work with somebody female in this role.\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\\n        And therapists that work with males.\\n      \"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"availability\":\"available\",\"hourly_rate\":{\">=\":25,\"<=\":60},\"works_with_gender\":\"male\"}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752602420583.5757, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752602420640.0442, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752602421030.846, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=availability --field-config=order=ASCENDING,field-path=hourly_rate --field-config=order=ASCENDING,field-path=works_with_gender --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752602420166, "endTime": 1752602421031.1426}