{"traceId": "b7a85e7f35b9fe220eba3d19490204e4", "spans": {"99a6f4cae52acbd3": {"spanId": "99a6f4cae52acbd3", "traceId": "b7a85e7f35b9fe220eba3d19490204e4", "parentSpanId": "7d7210759501ec83", "startTime": 1752606573087, "endTime": 1752606573295.3662, "attributes": {"transactional": false, "doc_count": 1, "otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "Batch.Commit", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "7d7210759501ec83": {"spanId": "7d7210759501ec83", "traceId": "b7a85e7f35b9fe220eba3d19490204e4", "parentSpanId": "1b40ddc19332ddd6", "startTime": 1752606573085, "endTime": 1752606573298.005, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Create", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "1b40ddc19332ddd6": {"spanId": "1b40ddc19332ddd6", "traceId": "b7a85e7f35b9fe220eba3d19490204e4", "startTime": 1752606573082, "endTime": 1752606573298.7927, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "CollectionReference.Add", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "CollectionReference.Add", "startTime": 1752606573082, "endTime": 1752606573298.7927}