{"traceId": "c6f2494eec88b1e4b60601384e072a29", "spans": {"067129fc7f14f06e": {"spanId": "067129fc7f14f06e", "traceId": "c6f2494eec88b1e4b60601384e072a29", "parentSpanId": "b9c79fe3d86f017e", "startTime": 1752604694481, "endTime": 1752604694934.1697, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, work life\\n      Additional information: Looking for executive coaching in Cardiff less than £75 per hour\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.030695949,-0.011805719,-0.083472416,0.0055421903,0.00043612637,0.0296371,0.07354656,-0.0147451,0.002076447,0.04082369,0.008748225,0.054284953,0.029766873,-0.010474026,-0.033952177,-0.052112013,-0.025598768,0.035946824,-0.074107595,-0.037976444,-0.007721238,-0.003304238,-0.0017783962,0.0035817127,-0.0007648549,0.013487087,0.005906936,-0.017281376,0.037510496,0.007838579,0.032761496,0.032538086,0.024372809,-0.025833232,0.036629245,0.016978825,-0.022133222,0.017582605,0.007738229,-0.07984772,-0.02790237,0.0014160502,0.0013486565,0.025697207,-0.012225234,-0.057119355,-0.016666615,-0.013998407,-0.07300932,0.0029273897,0.027806103,0.026028642,0.0004605719,0.06782065,-0.05766422,0.01660073,-0.0046252063,-0.014155866,0.0504138,-0.044528965,0.0380631,-0.025006669,-0.0063117635,-0.053963855,0.10061737,-0.030799096,0.034076646,0.012225047,-0.08767763,0.027270645,-0.0040395176,0.030860152,-0.053188458,0.016353479,-0.03414948,-0.071125954,-0.01818704,0.022890935,0.019812526,-0.040503103,-0.034077775,-0.023766844,-0.006687611,0.05905998,0.0008936274,0.01589887,-0.020624164,-0.027693808,-0.053405695,-0.038715914,0.06272786,0.029394059,0.030900002,-0.016920881,0.08006607,0.020301761,-0.047221184,-0.0048203864,0.025813911,0.029084058,0.0003374172,0.022834862,0.019922309,-0.013892755,0.051080026,0.094625376,-0.036064092,-0.027154973,-0.00719464,0.0866654,0.0017838546,-0.012815226,0.038698375,-0.033542786,-0.0002662315,0.030280128,0.0056371917,-0.013506207,0.046228196,0.0060210857,-0.018833147,0.02506868,-0.0586095,0.11274206,0.06145216,-0.008336391,0.0059075514,-0.043131825,-0.010672343,-0.04557821,0.09086208,-0.036367092,-0.019722525,0.032584332,-0.011327054,-0.009152921,0.00672995,-0.06467469,-0.010030472,0.085304424,-0.041289803,-0.013040844,-0.07652936,0.051361255,-0.0057535204,-0.053857427,0.035714474,0.08647938,0.016890544,0.038618244,-0.05337166,-0.004990701,0.036579978,0.071334556,-0.04211415,-0.0001499006,0.0040429896,-0.092150055,0.03796487,0.03159705,0.0065507432,-0.041221127,-0.018785127,0.015222016,-0.01658869,-0.046299756,-0.08446815,-0.021306716,0.0009072628,-0.026561113,-0.023766458,-0.025325617,-0.050436456,0.02423737,-0.045669,0.015172146,-0.05194858,0.012212108,-0.026955925,-0.0012360387,0.09784336,0.018842716,0.009810899,-0.07756355,-0.02143299,0.022111889,-0.056219466,0.0054831784,-0.038299803,0.0049672304,0.012751463,-0.015037211,0.028358549,-0.002555919,-0.031892303,0.029640594,0.056143206,-0.033093907,0.028891662,-0.03104422,0.004035328,-0.005063868,-0.03221664,-0.0032441085,-0.051078435,-0.015841015,-0.03408278,0.017917108,-0.008128856,0.043381456,-0.01723121,-0.03338803,-0.009917752,-0.06792493,0.0057904483,0.023407955,-0.006372677,-0.06808813,0.04857971,-0.05739402,-0.033855584,0.007942214,0.005486461,-0.001225024,0.055708464,-0.020132812,-0.067589544,0.00060677266,-0.056839433,-0.042654898,0.013551621,0.036212675,0.0056536403,-0.0040657804,-0.03425067,0.015452515,0.036822572,-0.018886492,-0.024709785,-0.041167494,-0.04939658,0.022347346,-0.017666776,-0.015364404,0.020046506,0.015210401,0.09390088,0.00586506,-0.0034435207,-0.0058959345,0.030800626,-0.042236768,-0.055423044,-0.012135789,0.016491026,-0.013273984,0.018495504,0.005141377,-0.0024476734,0.01989916,0.042453222,-0.031734865,-0.018621318,-0.020466633,-0.0023902748,-0.048595056,-0.03222178,0.014272888,0.06502291,-0.024373887,0.011025919,-0.036848675,-0.022055298,-0.048777673,-0.027789034,0.010569465,-0.03419224,0.0570489,-0.08207906,0.034213446,0.052066065,-0.007931685,0.038804796,0.0019054586,-0.0043411595,-0.0647826,0.0072050975,-0.017026434,-0.042503197,-0.07267333,0.039868064,0.055930283,-0.037258863,-0.042383436,0.03529081,-0.022484507,0.06198804,0.067708135,0.023400018,0.022252422,-0.0126663195,0.020941421,-0.013512659,0.06282128,-0.058078278,0.032100976,-0.01756536,-0.00006172389,-0.03449105,0.030913023,0.050562505,-0.01206914,0.0026425403,-0.0037631018,-0.044984505,-0.01537306,-0.061985187,0.0124840075,0.010639735,-0.03774497,0.057848334,0.0038546082,-0.03963071,0.009197278,0.022387573,-0.0006869161,0.03996116,0.03180184,-0.013430951,0.01911924,0.0035251717,-0.0146050695,-0.015499207,-0.065024026,0.026678959,-0.0054986826,-0.03593112,0.058230747,0.07808101,-0.010976978,0.01676648,-0.014448396,0.031021958,0.010416096,-0.002878954,0.011503723,-0.053965822,-0.0038547504,0.07859362,-0.02245654,0.040139265,0.052086204,-0.00090626365,0.01327224,0.013623994,0.021409906,0.06130735,0.018900871,0.022791559,0.022449495,-0.0056199604,0.021951409,0.0033863422,0.05175916,-0.03874694,-0.04026294,0.049283035,0.037630167,-0.0050866343,0.0041616927,0.04443833,-0.023283105,0.06742944,-0.024546728,0.03371679,-0.011525485,0.00261555,0.032102145,0.0138429515,-0.0026552032,0.007956165,-0.03780982,-0.04939136,0.03364436,-0.034937464,0.040167414,-0.051256523,0.03144326,-0.036689077,0.031558007,0.0024535374,0.06163036,0.04874494,-0.00017627583,-0.0107352855,0.022987852,0.008427656,0.039458897,-0.040360294,-0.022754135,-0.012077944,-0.035408776,0.039464924,-0.033554938,0.030310012,0.014254774,0.05894618,-0.024809994,-0.0032640037,0.06629467,0.044861242,-0.047063813,-0.05621729,0.030694043,-0.020009216,0.023949169,0.016061857,0.0244975,-0.002588868,-0.0031346793,0.023067912,0.03057302,0.051904928,-0.0055816798,0.010121428,-0.029840678,-0.0042532906,-0.019773155,-0.012367578,-0.02259781,0.040032048,-0.013726561,-0.039785042,0.018065121,-0.018916523,0.03670376,0.0023621446,-0.03582919,0.0037235217,0.020296136,0.018736983,-0.030228933,0.0018759508,-0.036980502,-0.007303231,-0.033909574,-0.00071768794,-0.014179841,-0.008039359,-0.019799232,0.045322318,-0.046429686,0.016957473,-0.062330075,-0.07025745,0.013538006,-0.028890502,-0.019379131,-0.005662656,-0.026106482,-0.065397434,-0.014257113,0.038601603,-0.011842304,-0.035478644,-0.003644181,-0.059154514,0.026121989,-0.0038435264,-0.01521333,0.03902035,0.03711565,0.027280938,0.041099377,-0.0661663,-0.019930067,0.055380516,-0.046181757,-0.034333047,-0.025595754,-0.08581225,-0.021156617,-0.015228174,0.042772695,0.041539557,0.007930031,-0.04192082,0.04016647,0.011639716,-0.058690187,-0.006370878,0.05748187,-0.04546521,-0.012766846,0.006906446,0.012771866,0.04288396,0.027138988,0.018871052,0.053459015,-0.002425578,0.035648793,0.01401883,-0.059284836,-0.04139193,-0.021519797,-0.054662235,0.050762437,0.028137092,0.02980395,0.02338223,-0.009953316,0.013396263,-0.039067168,-0.060011018,-0.0125034675,-0.007470006,0.019896714,-0.02249511,0.04368137,0.01778643,-0.02424364,0.019910883,0.013117326,0.037745114,-0.025975287,0.004811774,-0.0010620453,0.020389114,-0.057413757,0.022787336,0.07688173,0.055184633,0.0033076291,0.021061137,0.050494347,0.053828787,0.040715627,0.02982034,-0.029594969,0.015617145,0.021563452,0.028730672,0.0028487963,-0.0058205263,-0.010190203,-0.01827417,0.08352513,0.034292154,-0.001336932,-0.0061291903,0.008391421,0.023626186,-0.015282249,-0.02064071,-0.0013002428,0.052832585,0.014166848,-0.0127403885,-0.037962627,0.020351885,-0.018340003,-0.0011889889,-0.0019400037,-0.04896643,-0.04381599,-0.039244007,0.015947592,-0.03637769,0.013211457,0.017106924,-0.04435358,0.02698556,-0.06090037,0.009574331,-0.077834435,-0.05704437,-0.0107192695,0.051369183,0.017863646,-0.010718555,0.0009501373,0.023674298,0.021028852,0.058667466,-0.03510833,0.041004762,-0.022249086,0.044856794,-0.06552324,0.015160986,-0.026191222,0.010326786,-0.036459118,0.08868054,0.016782058,-0.022698062,0.01242574,0.0027431792,0.025556901,0.06625544,-0.00051607133,0.039366025,-0.0077339564,-0.03183196,-0.103648715,0.008675085,-0.08393494,-0.01562061,-0.013624192,-0.02308703,0.032161146,-0.046802934,-0.03842149,-0.07086615,0.038383357,0.0061344765,-0.050199214,-0.028700374,0.04946894,-0.029394547,0.024716394,0.004890319,-0.06281093,0.017193323,0.013136661,-0.0020386656,-0.006213406,0.03310181,0.0028385601,-0.023609363,0.018677492,-0.0037147962,-0.034706675,0.009634361,-0.005683725,-0.0042923433,-0.028924538,-0.032870825,-0.024665743,-0.033971682,0.013327425,0.04896509,-0.0031345033,-0.059773564,0.024643455,-0.060671944,-0.046122618,0.05658327,0.039546844,0.06993919,0.067522936,-0.044947628,0.037707914,-0.06512542,-0.0061912052,0.04136593,-0.041117936,-0.004151034,-0.011619613,-0.008055202,-0.051010918,0.008061519,0.030211044,-0.075146504,-0.026747849,-0.04185334,-0.0016067417,-0.000060054255,-0.023884507,0.017109327,-0.019744612,0.010220522,-0.0135768335,-0.014757923,-0.011978232,0.027951239,0.049476452,0.014758314,0.011290315,-0.009752554,-0.030755466,0.06969875,-0.015492162,0.026771855,0.03374443,0.036200933,-0.014370285,0.018216874,0.0049365903,-0.006341522,-0.014205479,0.014754475,0.00052432745,-0.021818286,0.07029717,-0.014917093,0.01202298,-0.01913076,0.013770748,-0.0055216793,-0.056540973,-0.029133443,-0.027060859,0.031405784,-0.010980001,0.062384017,0.018972922,-0.06866226,0.028461609,-0.011649818,0.014757662,0.018808229,-0.016781554,0.016707376,0.0159504,0.034480482,-0.0005990573,0.044015914,-0.026329452,0.047201604,-0.034111474,0.0663454,0.027756266,0.037389,0.011485803,-0.014517065,0.027392073,-0.011459253,0.030436127,0.026565837,0.012895142,-0.057352643,0.030253313,0.009598177,-0.023021437,-0.057740442,0.010600569,-0.02362786,0.018297287,0.007459576,-0.041732833,-0.039962802,-0.026537096,-0.031014621,0.020834157,-0.026470445,0.003734553,0.008378175,0.017122896,-0.08098892,-0.043330666,-0.009439542,0.023540737,0.0283655,-0.019533802,0.029013658,0.04133496,0.037718605,0.028978463,-0.059449483,0.0039582094,-0.00062812155,-0.009788168,-0.08409253,-0.005390266,0.027263427,-0.028753586]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "b9c79fe3d86f017e": {"spanId": "b9c79fe3d86f017e", "traceId": "c6f2494eec88b1e4b60601384e072a29", "startTime": 1752604694475, "endTime": 1752604695871.9163, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapists<PERSON>etriever", "genkit:isRoot": true, "genkit:path": "/{therapists<PERSON><PERSON>riever,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 5/10\\n      Areas of concern: relationships, work life\\n      Additional information: Looking for executive coaching in Cardiff less than £75 per hour\\n      Help the user find a the right person to work with.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10,\"where\":{\"compositeFilter\":{\"op\":\"AND\",\"filters\":[{\"fieldFilter\":{\"field\":{\"fieldReference\":\"fieldName\"},\"op\":\"GREATER_THAN_OR_EQUAL\",\"value\":{\"integerValue\":10}}},{\"fieldFilter\":{\"field\":{\"fieldReference\":\"fieldName\"},\"op\":\"LESS_THAN_OR_EQUAL\",\"value\":{\"integerValue\":50}}},{\"fieldFilter\":{\"field\":{\"fieldReference\":\"availability\"},\"op\":\"EQUAL\",\"value\":{\"stringValue\":\"available\"}}}]}}}}", "genkit:state": "error"}, "displayName": "therapists<PERSON>etriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding"}, "timeEvents": {"timeEvent": [{"time": 1752604694944.088, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752604694999.8594, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752604695871.8071, "annotation": {"attributes": {"exception.type": "9", "exception.message": "9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding", "exception.stacktrace": "Error: 9 FAILED_PRECONDITION: Missing vector index configuration. Please create the required index with the following gcloud command: gcloud firestore indexes composite create --project=mood-rings-beta --collection-group=therapists --query-scope=COLLECTION --field-config=order=ASCENDING,field-path=compositeFilter --field-config=vector-config='{\"dimension\":\"768\",\"flat\": \"{}\"}',field-path=embedding\n    at callErrorFromStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/call.js:32:19)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:359:73)\n    at Object.onReceiveStatus (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client-interceptors.js:324:181)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/resolving-call.js:135:78\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)\nfor call at\n    at ServiceClientImpl.makeServerStreamRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/client.js:342:32)\n    at ServiceClientImpl.<anonymous> (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@grpc/grpc-js/build/src/make-client.js:105:19)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/v1/firestore_client.js:237:29\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:38:28\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/normalCalls/timeout.js:44:16\n    at Object.request (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:234:40)\n    at makeRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:159:28)\n    at retryRequest (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/retry-request/index.js:119:5)\n    at StreamProxy.setStream (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streaming.js:225:37)\n    at StreamingApiCaller.call (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/google-gax/build/src/streamingCalls/streamingApiCaller.js:54:16)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at VectorQuery._getResponse (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:105:32)\n    at VectorQuery.get (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/vector-query.js:98:39)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/firebase/lib/firestore-retriever.js:106:10\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:205:14\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/action.js:126:27\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:65:16\n    at async /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:88:24\n    at async runInNewSpan (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js:77:10)"}, "description": "exception"}}]}}}, "displayName": "therapists<PERSON>etriever", "startTime": 1752604694475, "endTime": 1752604695871.9163}