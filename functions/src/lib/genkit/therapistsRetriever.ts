/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import {defineFirestoreRetriever} from "@genkit-ai/firebase";

import {getApp, initializeApp} from "firebase-admin/app";
import {getFirestore} from "firebase-admin/firestore";

// import { Activity } from './types';
// import { ai, getProjectId } from './genkit.config';

import {gemini15Flash, googleAI,
  textEmbedding004} from "@genkit-ai/googleai";
import {genkit} from "genkit";

// configure a Genkit instance
const ai = genkit({
  plugins: [
    googleAI({apiKey: "AIzaSyACzlRepDR-fZAHNYnbosimmtTrVb__iHg"}),
  ],
  model: gemini15Flash, // set default model

});

/**
 * Gets an existing Firebase Admin app instance or initializes a new one.
 * @return {App} The Firebase Admin app instance
 */
function getOrInitApp() {
  try {
    return initializeApp({
      projectId: "mood-rings-beta",
    });
  } catch (error) {
    console.error(error);
  }
  return getApp();
}

const app = getOrInitApp();
const firestore = getFirestore(app);


/**
 * Retriever for places based on the `description`
 * field using the Genkit retriever for Firestore.
 */
export const therapistsRetriever = defineFirestoreRetriever(ai, {
  name: "therapistsRetriever",
  firestore,
  collection: "therapists",
  contentField: "description",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
  metadataFields: ["user_id", "name", "job_title", "image_url",
    "availability", "areas_of_concern",
    "online_or_offline", "location", "hourly_rate"],
});

export const therapiesRetriever = defineFirestoreRetriever(ai, {
  name: "therapiesRetriever",
  firestore,
  collection: "therapies",
  contentField: "description",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
  metadataFields: ["name"],
});
