import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {gemini15Flash, googleAI} from "@genkit-ai/googleai";
import {genkit} from "genkit";
import {therapistsRetriever,
  therapiesRetriever} from "./lib/genkit/therapistsRetriever";
import {FieldValue} from "firebase-admin/firestore";
// import semanticRanker512 from "@genkit-ai/vertexai";
import {Document} from "@genkit-ai/ai/retriever";
import {GoogleGenerativeAI} from "@google/generative-ai";
import {connectDatabaseEmulator, getDatabase} from "firebase/database";

// import {
//   neo4j,
//   // neo4jRetrieverRef,
// } from "genkitx-neo4j";


// Emulator - Local Only
import {connectFunctionsEmulator, getFunctions} from "firebase/functions";
import {getApp, initializeApp} from "firebase/app";
const useEmulator = true;
const firebaseConfig = {
  apiKey: "AIzaSyAkLbnUC7I_kBUETtENh6Sgkc4nlr5IbLE",
  authDomain: "mood-rings-beta.firebaseapp.com",
  projectId: "mood-rings-beta",
  storageBucket: "mood-rings-beta.firebasestorage.app",
  messagingSenderId: "63584321569",
  appId: "1:63584321569:web:56e6d98e0cc7fb68322360",
  measurementId: "G-DY94X6B3ZF",
  databaseURL: useEmulator ? "http://127.0.0.1:9000/mood-rings-beta/us-central1/" : "https://mood-rings-beta-default-rtdb.europe-west1.firebasedatabase.app/",
};

initializeApp(firebaseConfig);

console.log({useEmulator});
console.log(process.env.FUNCTIONS_EMULATOR);

if (process.env.FUNCTIONS_EMULATOR && useEmulator) {
  const functions = getFunctions(getApp());
  const database = getDatabase(getApp());
  connectFunctionsEmulator(functions, "127.0.0.1", 5001);
  connectDatabaseEmulator(database, "127.0.0.1", 9000);
}

// Initialize Firebase Admin if not already initialized elsewhere
if (!admin.apps.length) {
  admin.initializeApp(firebaseConfig);
}

// CORS
const allowedOrigins = ["http://localhost:5173", "http://127.0.0.1:5173", "https://talktoneuro.com"];

// AI
const isEmulated = process.env.FUNCTIONS_EMULATOR === "true";
// console.log(process.env.GEMINI_API_KEY);
console.log({isEmulated});

// Type definitions for mood assessment
interface MoodAssessmentData {
  moodScore: number;
  concerns: string[];
  note: string;
  preferences: {
    therapistGender: "no_preference" | "male" | "female";
    sessionType: "in_person" | "online" | "no_preference";
    priceRange: {
      min: number;
      max: number;
    };
    location?: string;
    workingHours?: "9-5" | "flexible";
    availability: boolean;
    works_with_gender: "no_preference" | "male" | "female"
      | "non-binary" | "prefer-not-to-say";
    organisation_id?: string;
  };
  profile?: {
    dateOfBirth: string;
    gender: "male" | "female" | "non-binary";
    userLocation: string;
  };
  timestamp: number;
}

type SearchPreferences = {
  availability?: string;
  session_type?: string;
  working_hours?: string;
  gender?: string;
  location?: string;
  online_or_offline?: "in_person" | "online" | "no_preference";
  works_with_gender?: "male" | "female" | "no_preference"
  | "non-binary" | "prefer-not-to-say";
  organisation_id?: string;
  // hourly_rate?: {
  //   ">=": number;
  //   "<=": number;
  // };
  hourly_rate?: string;
}

// Validation function for mood assessment data
/**
 * Validates the structure and content of mood assessment data.
 * @param {MoodAssessmentData} data - The data object to validate
 * @return {boolean} True if data matches
 * MoodAssessmentData interface requirements:
 *   - moodScore: number between 1-10
 *   - note: non-empty string
 *   - concerns: array of strings
 *   - timestamp: number
 */
function isValidMoodAssessment(data: MoodAssessmentData)
  : data is MoodAssessmentData {
  return (
    typeof data === "object" &&
    typeof data.moodScore === "number" &&
    typeof data.note === "string" &&
    data.moodScore >= 1 &&
    data.moodScore <= 10 &&
    Array.isArray(data.concerns) &&
    data.concerns.every((concern: string) => typeof concern === "string") &&
    typeof data.timestamp === "number"
  );
}

// export const qaFlow = ai.defineFlow(
//   {
//     name: "therapyQA", inputSchema: z.string(), outputSchema: z.string(),
//   },
//   async (prompt) => {
//     console.log("prompt: ", prompt);

//     const docs = await ai.retrieve({
//       retriever: therapistsRetriever,
//       query: prompt,
//       options: {
//         limit: 3,
//       },
//     });

//     const {text} = await ai.generate({
//       model: gemini15Flash,
//       prompt: prompt,
//       docs,
//     });

//     console.log("response: ", text);

//     return text;
//   }
// );


/**
 * Replaces UIDs in a text string with corresponding
 * names from an array of uid-name pairs
 *
 * @param {string} text - The text string containing UIDs to be replaced
 * @param {TherapistProfileName[]} uidNamePairs -
 * Array of objects containing uid and name properties
 * @return {string} The text with UIDs replaced by corresponding names
 */
function replaceUidsWithNames(text: string,
  uidNamePairs: Array<{uid: string, name: string}>): string {
  // Create a map for quick lookup of names by uid
  const uidMap = new Map<string, string>();
  uidNamePairs.forEach((pair) => {
    uidMap.set(pair.uid, pair.name);
  });

  // Replace all instances of UIDs with their corresponding names
  // This regex looks for UIDs in various formats:
  // - Plain UID: XXjH7tD9rQ23fjbkSAXG
  // - UID in square brackets: [XXjH7tD9rQ23fjbkSAXG]
  // - UID with asterisks and brackets: **[XXjH7tD9rQ23fjbkSAXG]**
  return text.replace(/(?:\*\*\[|\[)?([a-zA-Z0-9]{20,})(?:\]\*\*|\])?/g,
    (match, uid) => {
    // Check if the extracted UID is in our map
      const name = uidMap.get(uid);

      if (name) {
      // If UID was in [brackets], put the name in brackets too
        if (match.startsWith("[") && match.endsWith("]")) {
          return `${name}`;
        }

        if (match.startsWith("(") && match.endsWith(")")) {
          return `**${name}**`;
        }

        // If UID was in **[brackets]**, put the name in the same format
        if (match.startsWith("**[") && match.endsWith("]**")) {
          return `**${name}**`;
        }
        // Otherwise just return the name
        return `**${name}**`;
      }

      // If no matching UID was found, return the original match
      return match;
    });
}

/**
 * Utility function to extract metadata into an array of uid-name pairs
 * This can be used with the UID replacement function from the previous example
 * @param {Document[]} documents - The function request object
 * @return {Array<{uid: string, name: string}>}
 */
function extractUidNamePairs(documents: Document[]):
  Array<{uid: string, name: string}> {
  return documents.map((doc) => ({
    uid: doc.metadata?.id,
    name: doc.metadata?.name,
  }));
}

/**
 * Reorders an array of Documents so
 * that a document with a specific ID appears first
 * @param {Document[]} documents
 * The array of Document objects to reorder
 * @param {String} targetId
 * The ID to prioritize and move to the first position
 * @return {Document[]} A new array with the target document first,
 * followed by all other documents
 */
function reorderDocumentsArray(documents: Document[], targetId: string)
  : Document[] {
  // Create a copy of the array to avoid mutating the original
  const reorderedDocs = [...documents];

  // Find the index of the document with the target ID
  const targetIndex =
    reorderedDocs.findIndex((doc) => doc.metadata?.id === targetId);

  // If the document with the target ID is found
  if (targetIndex !== -1) {
    // Remove the target document from its current position
    const targetDoc = reorderedDocs.splice(targetIndex, 1)[0];

    // Add the target document to the beginning of the array
    reorderedDocs.unshift(targetDoc);
  }

  return reorderedDocs;
}


// Firebase Function to handle mood assessment submissions
/**
 * Cloud Function that processes mood assessment submissions.
 * Validates the input data, processes it through AI for recommendations,
 * and returns matching therapist documents
 * along with AI-generated recommendations.
 * @param {Object} request - The function request object
 * @param {MoodAssessmentData} request.data
 * - The mood assessment data
 * @returns {Promise<{success: boolean, message: string,
 * timestamp: number,
 * aiRecommendations: string, aiDocs: TherapistDoc[]}>}
* @throws {functions.https.HttpsError}
* - Throws if data is invalid or processing fails
*/
export const submitMoodAssessment =
functions.https.onRequest(
  {cors: allowedOrigins, secrets: ["GEMINI_API_KEY"]},
  async (req, res) => {
    console.log("submitMoodAssessment: ", req.body.data);

    // Handle preflight requests
    if (req.method === "OPTIONS") {
      res.status(204).send();
      return;
    }

    // Ensure this is a POST request
    if (req.method !== "POST") {
      res.status(405).json({error: "Method not allowed"});
      return;
    }

    // Initialize AI configuration
    const ai = genkit({
      plugins: [
        googleAI({apiKey: process.env.GEMINI_API_KEY}),
        // neo4j([
        //   {
        //     indexId: "vector",
        //     embedder: textEmbedding004,
        //     embedderOptions: {outputDimensionality: 100},
        //   },
        // ]),
      ],
      model: gemini15Flash,
    });


    try {
      const data = req.body.data as MoodAssessmentData;

      // Define a type for AI-generated results
      interface AIAssessmentResults {
        aiRecommendations: string;
        therapies: Document[];
        therapists: Document[];
        aiTherapyRecommendations: string;
        timestamp: number;
      }

      // Define a type for the complete result record
      interface AssessmentResultRecord {
        request: MoodAssessmentData;
        results: AIAssessmentResults;
        createdAt: admin.firestore.FieldValue;
      }

      // Initialize Firestore
      const db = admin.firestore();

      // Validate the incoming data
      if (!data) {
        res.status(400).json({error: "No data provided"});
        return;
      }

      // Validate the request data
      if (!isValidMoodAssessment(data)) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Invalid mood assessment data format"
        );
      }

      console.log("data: ", data);

      const searchPreferences: SearchPreferences = {
        availability: data.preferences.availability ? "available" :
          undefined,
        online_or_offline: "no_preference",
        // gender: 'no_preference',
        // works_with_gender: "no_preference",
      };

      if (data.preferences.sessionType !== "no_preference") {
        searchPreferences.online_or_offline = data.preferences.sessionType;
      }

      if (data.preferences.works_with_gender !== "no_preference") {
        searchPreferences.works_with_gender =
        data.preferences.works_with_gender;
      }

      if (data.preferences.workingHours &&
        data.preferences.workingHours !== "flexible") {
        searchPreferences.working_hours = data.preferences.workingHours;
      }

      if (data.preferences.location) {
        // searchPreferences.location = data.preferences.location;
      }

      if (data.preferences.organisation_id) {
        searchPreferences.organisation_id = data.preferences.organisation_id;
      }

      // Add price range filtering if specified
      // if (data.preferences.priceRange &&
      //     data.preferences.priceRange.min !== undefined &&
      //     data.preferences.priceRange.max !== undefined) {
      //     // searchPreferences.hourly_rate =
      //     // ">=": data.preferences.priceRange.min,
      //     // "<= " + data.preferences.priceRange.max;

      //   // console.log(`Price range filter applied`);
      // }

      // Optional: Generate AI response based on mood assessment
      let aiPrompt = `
      User mood score: ${data.moodScore}/10
      Areas of concern: ${data.concerns.join(", ")}
      Additional information: ${data.note}
      Help the user find a the right person to work with.
      REQUIREMENTS: Only use data from the CONTEXT passed in.`;

      const aiTherapiesPrompt = `
      User mood score: ${data.moodScore}/10
      Areas of concern: ${data.concerns.join(", ")}
      Additional information: ${data.note}
      Help the user find a the therapy type for their needs.
      REQUIREMENTS: Only use data from the CONTEXT passed in.`;

      if (data.profile && data.profile.gender) {
        aiPrompt += `
        And therapists that work with ${data.profile.gender}s.
      `;
        searchPreferences.works_with_gender = data.profile.gender;
      }

      console.log("searchPreferences: ", searchPreferences);
      console.log("aiPrompt: ", aiPrompt);

      // // KAG
      // console.log(process.env.NEO4J_URI);
      // console.log(process.env.NEO4J_USERNAME);

      // // To specify an index:
      // const kagFactsRetriever = neo4jRetrieverRef({
      //   indexId: "vector", // "vector"
      // });
      // console.log("kagFactsRetriever: ", kagFactsRetriever);

      // // To use the index you configured when you loaded the plugin:
      // const KAGdocs = await ai.retrieve({
      //   retriever: kagFactsRetriever,
      //   query: "What is the best therapy for anxiety?",
      // });
      // console.log("KAGdocs.length: ", KAGdocs.length);

      // console.log("KAGdocs:", KAGdocs);

      // /// END KAG

      const docs = await ai.retrieve({
        retriever: therapistsRetriever,
        query: aiPrompt,
        options: {
          limit: 3,
          prerankK: 10,
          where: searchPreferences,
        },
      });

      const therapyDocs = await ai.retrieve({
        retriever: therapiesRetriever,
        query: aiTherapiesPrompt,
        options: {
          limit: 3,
          prerankK: 10,
        },
      });

      console.log("therapies.length: ", therapyDocs.length);
      console.log("docs.length: ", docs.length);

      const uidNamePairs = extractUidNamePairs(docs);
      console.log("UID-Name pairs for replacement function:");
      console.log(uidNamePairs);

      const uidTherapyPairs = extractUidNamePairs(therapyDocs);
      console.log("UID-Therapy pairs for replacement function:");
      console.log(uidTherapyPairs);

      const {text: aiResponse} = await ai.generate({
        model: gemini15Flash,
        prompt: aiPrompt,
        docs,
      });

      const {text: aiTherapyResponse} = await ai.generate({
        model: gemini15Flash,
        prompt: aiTherapiesPrompt,
        docs: therapyDocs,
      });

      // RERANK
      // ReRank Docs manually
      const matchTherapistIds =
        aiResponse.match(/(?:[|)?([a-zA-Z0-9]{20,})(?:\]\*\*|\])?/g);
      console.log({matchTherapistIds});

      let newDocs: Document[] = docs;
      if (matchTherapistIds && matchTherapistIds[0]) {
        const matchTherapistId = matchTherapistIds[0].slice(1, -1);
        newDocs = reorderDocumentsArray(docs, matchTherapistId);
        console.log({newDocs});
      }


      const cleanResponse = replaceUidsWithNames(aiResponse, uidNamePairs);
      console.log({cleanResponse});

      const cleanTherapyResponse =
        replaceUidsWithNames(aiTherapyResponse, uidTherapyPairs);
      console.log({cleanTherapyResponse});

      let newResultRef;

      // Store the request and results in Firestore
      try {
        const resultRecord: AssessmentResultRecord = {
          request: data,
          results: {
            aiRecommendations: cleanResponse,
            therapies: therapyDocs.map((doc) => doc?.metadata?.id),
            therapists: docs.map((doc) => doc?.metadata?.id),
            aiTherapyRecommendations: cleanTherapyResponse,
            timestamp: data.timestamp,
          },
          createdAt: FieldValue.serverTimestamp(),
        };

        newResultRef = await db.collection("results").add(resultRecord);

        console.log("Results saved to Firestore as: ", newResultRef.id);
      } catch (firestoreError) {
        console.error("Error saving to Firestore:", firestoreError);
        // Continue processing - don't fail the request if storage fails
      }

      // GenKit Reranking - BROKEN
      // Create documents from the retrieved data
      // const documents = docs.map((doc) => {
      //   // Extract the text content properly from each document
      //   const text = doc.content?.map((content) =>
      //     content.text || "").join(" ") || "";
      //   return Document.fromText(text, doc.metadata);
      // });
      // console.log("documents: ", documents);

      // // Create the query document once
      // const query = Document.fromText(aiPrompt);
      // console.log("query: ", query);

      // First, log the version of GenKit you're using
      // console.log("GenKit version:", ai?.version || "unknown");

      try {
        // console.log("Attempting reranking with correct document format...");

        // // Create properly formatted documents according to the error message
        // const formattedDocs = docs.map((doc, index) => {
        //   // Extract text content safely
        //   let textContent = "";
        //   if (doc.content) {
        //     if (Array.isArray(doc.content)) {
        //       textContent = doc.content
        //         .map((item) => (item && typeof item.text === "string") ?
        //           item.text : "")
        //         .join(" ");
        //     } else if (typeof doc.content === "string") {
        //       textContent = doc.content;
        //     } else if (doc.content) {
        //       textContent = "doc.content?.toString()";
        //     }
        //   }

        //   // Format according to expected structure
        //   return {
        //     content: [
        //       {
        //         text: textContent || `Document ${index}`,
        //       },
        //     ],
        //     metadata: doc.metadata || {id: `doc_${index}`},
        //   };
        // });

        // console.log("Formatted first doc:",
        // JSON.stringify(formattedDocs[0], null, 2));

        // // Format the query as a Document object as expected
        // const formattedQuery = {
        //   content: [
        //     {
        //       text: aiPrompt.substring(0, 500), // Truncate long queries
        //     },
        //   ],
        // };

        // console.log("Formatted query:",
        //   JSON.stringify(formattedQuery, null, 2));

        // // Attempt reranking with properly formatted documents
        // const rerankedDocs = await ai.rerank({
        //   reranker: semanticRanker512,
        //   query: formattedQuery,
        //   documents: formattedDocs,
        // });

        // console.log("Reranking succeeded!");
        // console.log("Reranked docs count:", rerankedDocs.length);

        // Process the results
        // const processedResults = rerankedDocs.map((doc) => {
        //   return {
        //     text: doc.content?.[0]?.text || "",
        //     score: doc.metadata?.score,
        //     id: doc.metadata?.id,
        //   };
        // });

        // Return the successful result
        res.status(200).json({
          data: {
            success: true,
            message: "Mood assessment submitted successfully",
            timestamp: data.timestamp,
            aiRecommendations: cleanResponse,
            aiDocs: docs,
            therapies: therapyDocs,
            aiTherapyRecommendations: cleanTherapyResponse,
            resultId: newResultRef?.id,
            // rerankedResults: processedResults,
          },
          status: "success",
        });
      } catch (error: unknown) {
        console.error("Reranking failed with correct format:", error);

        // Continue without reranking
        res.status(200).json({
          data: {
            success: true,
            message: "Mood assessment submitted (reranking unavailable)",
            timestamp: data.timestamp,
            aiRecommendations: cleanResponse,
            aiDocs: newDocs,
            error: "Reranking failed: " + error?.toString(),
          },
          status: "success",
        });
      }
    } catch (error) {
      console.error("Error processing mood assessment:", error);
      res.status(500).json({
        error: "Internal server error processing mood assessment",
        message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });


// Matching
export const matchRequest =
  functions.https.onRequest(
    {cors: allowedOrigins},
    async (req, res) => {
      console.log("matchRequest: ", req.body);

      // Set CORS headers
      // const origin = req.headers.origin;
      // if (origin && allowedOrigins.includes(origin)) {
      //   res.setHeader("Access-Control-Allow-Origin", origin);
      // }
      // res.set("Access-Control-Allow-Methods", "POST");
      // res.set("Access-Control-Allow-Headers", "Content-Type");
      // res.setHeader(
      //   "Access-Control-Allow-Headers", "Access-Control-Allow-Headers," +
      //   "Origin,Accept, X-Requested-With, Content-Type," +
      //   "Access-Control-Request-Method,Access-Control-Request-Headers");

      // Handle preflight requests
      if (req.method === "OPTIONS") {
        res.status(204).send("match request received");
        return;
      }

      // Ensure this is a POST request
      if (req.method !== "POST") {
        res.status(405).json({error: "Method not allowed"});
        return;
      }

      try {
        const {email, therapistId} = req.body.data;

        // Validate email
        if (!email || typeof email !== "string" || !email.includes("@")) {
          res.status(400).json({error: "Invalid email address"});
          return;
        }

        // Validate therapistId
        if (!therapistId || typeof therapistId !== "string") {
          res.status(400).json({error: "Invalid therapist ID"});
          return;
        }

        console.log(`Match for therapist ${therapistId} from ${email}`);

        // First check if a match already exists
        const db = admin.firestore();
        const matchQuery = await db.collection("matches")
          .where("therapistId", "==", therapistId)
          .where("email", "==", email)
          .get();

        if (!matchQuery.empty) {
          console.log(`Match already exists for ${therapistId} from ${email}`);

          // Get therapist contact email for existing match
          const therapistQuery = db.collection("therapists").doc(therapistId);
          const therapistDoc = await therapistQuery.get();
          const therapistContactEmail = therapistDoc.data()?.contact_email;

          res.status(200).json({
            data: {
              success: true,
              therapistContactEmail: therapistContactEmail || null,
            },
            message: "Match request already exists.",
          });
          return;
        }

        // If no match exists, create a new one
        const matchDoc = await db.collection("matches").add({
          email,
          therapistId,
          status: "pending",
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });

        console.log(`Match stored as ${matchDoc.id}`);

        const therapistQuery = db.collection("therapists").doc(therapistId);

        const thertapistDoc = await therapistQuery.get();
        const therapistEmail = thertapistDoc.data()?.contact_email;

        console.log("thertapistDoc: ", thertapistDoc);

        if (matchDoc.id && thertapistDoc.data()?.contact_email) {
          console.log(`Match sending email to ${therapistEmail}`);

          admin
            .firestore()
            .collection("mail")
            .add({
              to: thertapistDoc.data()?.contact_email,
              message: {
                subject: "New Match Request via Neuro!",
                text: "Hello!  /n Congratulations, somebody wishes to match with you via Neuro. See the match and accept here: https://talktoneuro.com/matches",
                html: "Hello! <br/> Congratulations, somebody wishes to match with you via Neuro. <br/> See the match and accept here: https://talktoneuro.com/matches",
              },
            })
            .then(() => console.log("Queued match email for delivery!"));

          res.status(200).json({
            data: {
              success: true,
              therapistContactEmail: therapistEmail || null,
            },
            message: "Match request submitted successfully.",
          });
        } else {
          console.error(`Match not stored for ${therapistId} from ${email}`);
          res.status(500).json({
            data: {success: false},
            error: "Internal server error processing match request",
          });
        }
      } catch (error) {
        console.error("Error processing match request:", error);
        res.status(500).json({
          data: {success: false},
          error: "Internal server error processing match request",
        });
      }
    });

export const onMatchStatusUpdated = functions.firestore
  .onDocumentUpdated("matches/{matchId}", async (event) => {
    console.log(`Match ${event.data?.after.id} onDocumentUpdated`);
    const beforeData = event.data?.before.data();
    const afterData = event.data?.after.data();
    // ... rest of your function

    // Check if status changed from pending to accepted
    if (beforeData?.status === "pending" && afterData?.status === "accepted") {
      console.log("Match changed from pending to accepted");

      try {
        // Get the therapist details
        const db = admin.firestore();
        const therapistDoc = await db.collection("therapists")
          .doc(afterData.therapistId)
          .get();

        const therapistData = therapistDoc.data();

        if (!therapistData) {
          throw new Error(`Therapist ${afterData.therapistId} not found`);
        }

        // Queue email to the person who requested the match
        await db.collection("mail").add({
          to: afterData.email,
          message: {
            subject: "Neuro Match Request Accepted!",
            text: `Great news! Your match request has been accepted
                   \n by ${therapistData.name || "your therapist"}. 
                   \nYou can now proceed to schedule your first session. 
                   \nVisit https://talktoneuro.com/matches to get started.`,
            html: `
              <p>Great news! Your match request has been accepted 
              <br/>by ${therapistData.name || "your therapist"}.</p>
              <p>You can now proceed to schedule your first session.</p>
              <p>Visit <a href="https://talktoneuro.com/matches">https://talktoneuro.com/matches</a> to get started.</p>
            `,
          },
        });

        console.log(`Accept email queued for delivery to ${afterData.email}`);
      } catch (error) {
        console.error("Error processing match acceptance:", error);
        throw new functions.https.HttpsError(
          "internal",
          "Error processing match acceptance notification"
        );
      }
    }
  });

// CHAT

type OnCallData = {
  message: string;
  sessionId: string;
  isSystemPrompt: boolean;
}

exports.processMessage = functions.https.onRequest(
  {cors: allowedOrigins},
  async (req, res) => {
    // console.log("processMessage: ", req.body);
    // console.log("processMessage res: ", res);

    // // Ensure user is authenticated
    // if (context && !res.auth) {
    //   throw new functions.https.HttpsError(
    //     'unauthenticated',
    //     'The function must be called while authenticated.'
    //   );
    // }

    // Initialize the Gemini Developer API backend service
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

    const data: OnCallData = req.body.data;
    const {message, sessionId, isSystemPrompt} = data;
    // const userId = context?.auth?.uid || "12345";
    // const userId = "12345";

    console.log({sessionId});
    console.log({isSystemPrompt});

    try {
      // // Get conversation history
      // const sessionRef = admin.database()
      //   .ref(`chatSessions/${userId}/${sessionId}`);
      // const sessionSnapshot = await sessionRef.once("value");
      // const session = sessionSnapshot.val();

      // if (!session) {
      //   throw new functions.https.HttpsError(
      //     "not-found",
      //     "Session not found"
      //   );
      // }

      // interface Message {
      //   id: string;
      //   text: string;
      //   sender: "user" | "ai";
      //   timestamp: number;
      // }

      // // Format conversation history for AI
      // const messages = session.messages as Message[];
      // console.log({messages});
      // // let conversationHistory = messages.map((msg: unknown) => ({
      // //   role: msg && msg?.sender === "user" ? "user" : "model",
      // //   parts: [{text: msg.text}],
      // // }));

      const conversationHistory = [
        {role: "user", parts: [{text: ""}]},
        {role: "model", parts: [
          {text: "Hi there! I'm here to ask you some questions " +
          "about how you've been feeling lately." +
          "This will help us understand your mental wellbeing better." +
          " Let's start with a few questions" +
          " about your mood and energy levels over" +
          " the past two weeks. Ready to begin?"}]},
      ];


      // If this is a system prompt,
      // don't include it in the visible conversation
      // const aiPrompt = isSystemPrompt ? message : "";

      const aiPrompt = `
  You are a warm, supportive, and non-judgmental therapist. 
  Engage the user in a gentle, flowing conversation 
  to check in on their mental and emotional well-being. 
  Ask each of the following 16 questions in order, 
  one at a time, to determine how often 
  they've experienced the described problem over the past 2 weeks.

  For each question, prompt for a single numerical response 
  (0, 1, 2, or 3) using these options:

0 = Not at all
1 = Several days
2 = More than half the days
3 = Nearly every day

Maintain an empathetic tone throughout, 
rephrasing questions naturally within the conversation. 
Do not skip any questions.

**The 16 questions to ask, in order, are:**

1. Little interest or pleasure in doing things.
2. Feeling down, depressed, or hopeless.
3. Trouble falling or staying asleep, or sleeping too much.
4. Feeling tired or having little energy.
5. Poor appetite or overeating.
6. Feeling bad about yourself, 
or that you are a failure or have let yourself or your family down.
7. Trouble concentrating on things, 
such as reading the newspaper or watching television.
8. Moving or speaking so slowly that other people could have noticed. 
Or the opposite, being so fidgety or restless that you have been moving 
around a lot more than usual.
9. Thoughts that you would be better off dead, 
or of hurting yourself in some way.
10. Feeling nervous, anxious, or on edge.
11. Not being able to stop or control worrying.
12. Worrying too much about different things.
13. Trouble relaxing.
14. Being so restless that it's hard to sit still.
15. Becoming easily annoyed or irritable.
16. Feeling afraid as if something awful might happen.

Once you have a value numerical answer for the question you just asked,
store it in an array of answers, against the question number.
When question has been anwred with a valid answer, 
respond with the current array of answers, before asking the next quetion.`;

      console.log({aiPrompt});

      // Create the model
      const model = genAI.
        getGenerativeModel({model: "models/gemini-1.5-flash",
          systemInstruction: aiPrompt});

      // Start a chat session
      const chat = model.startChat({
        history: conversationHistory,
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
        },
      });

      // Generate response
      const result = await chat.sendMessage(aiPrompt || message);
      const response = result.response;
      const responseText = response.text();

      // Check if this is the final message with scores
      let scores = null;
      if (responseText.includes("Depression score:") &&
        responseText.includes("Anxiety score:")) {
      // Extract scores using regex
        const depressionMatch =
          responseText.match(/Depression score:\s*(\d+)/i);
        const anxietyMatch =
          responseText.match(/Anxiety score:\s*(\d+)/i);

        if (depressionMatch && anxietyMatch) {
          scores = {
            depression: parseInt(depressionMatch[1], 10),
            anxiety: parseInt(anxietyMatch[1], 10),
          };
        }
      }

      res.status(200).json({
        data: {
          success: true,
          message: responseText,
          scores: scores,
          error: "Reranking failed: " + Error?.toString(),
        },
        status: "success",
      });
    } catch (error) {
      console.error("Error processing message:", error);
      throw new functions.https.HttpsError(
        "internal",
        "An error occurred while processing the message"
      );
    }
  });
