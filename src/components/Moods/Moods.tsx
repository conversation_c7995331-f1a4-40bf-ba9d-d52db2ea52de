import { analytics, db } from "../Firebase";
import {
  writeBatch,
  doc,
  collection,
} from "firebase/firestore";
import MoodForm from "./MoodForm";
import { FormEventHandler } from "react";
import { useState, useEffect } from "react";
import {useSearchParams} from "react-router"
import { Chart as ChartJS, RadialLinearScale } from "chart.js/auto";
import { Radar } from "react-chartjs-2";
import { logEvent } from "firebase/analytics";

ChartJS.register(RadialLinearScale);

type Score = {
  type: string;
  score: string;
}

interface MoodsPropTypes {
  embedded?: boolean
}

const Moods = ({embedded}: MoodsPropTypes) => {

    console.log({embedded});

    const [searchParams, setSearchParams] = useSearchParams();
    const isDemo: boolean = location.pathname === "/demo" || location.pathname === "/embed";
    let campaignId: string | undefined = undefined;

    let moodParam: string | undefined = undefined;
    let sleepParam: string | undefined = undefined;
    let socialParam: string | undefined = undefined;
    let healthParam: string | undefined = undefined;
    let familyParam: string | undefined = undefined;
    let financesParam: string | undefined = undefined;
    let romanceParam: string | undefined = undefined;
    let workParam: string | undefined = undefined;

    logEvent(analytics, 'moods_viewed', {
      content_type: window.location.pathname === "/demo" ? 'demo' : "moods",
    });

    if(searchParams.get("campaignId") && searchParams.get("campaignId") !== null ) {
      campaignId = searchParams.get("campaignId")?.toString();
    }

    if(searchParams.get("mood") && searchParams.get("mood") !== null ) {
      moodParam = searchParams.get("mood")?.toString();

      if(moodParam)
      localStorage.setItem("mood", moodParam);
    }

    if(searchParams.get("sleep") && searchParams.get("sleep") !== null ) {
      sleepParam = searchParams.get("sleep")?.toString();

      if(sleepParam)
      localStorage.setItem("sleep", sleepParam);
    }
    if(searchParams.get("social") && searchParams.get("social") !== null ) {
      socialParam = searchParams.get("social")?.toString();

      if(socialParam)
      localStorage.setItem("social", socialParam);
    }
    if(searchParams.get("health") && searchParams.get("health") !== null ) {
      healthParam = searchParams.get("health")?.toString();

      if(healthParam)
      localStorage.setItem("health", healthParam);
    }

    if(searchParams.get("family") && searchParams.get("family") !== null ) {
      familyParam = searchParams.get("family")?.toString();

      if(familyParam)
      localStorage.setItem("family", familyParam);
    }

    if(searchParams.get("romance") && searchParams.get("romance") !== null ) {
      romanceParam = searchParams.get("romance")?.toString();

      if(romanceParam)
      localStorage.setItem("romance", romanceParam);
    }

    if(searchParams.get("work") && searchParams.get("work") !== null ) {
      workParam = searchParams.get("work")?.toString();

      if(workParam)
      localStorage.setItem("work", workParam);
    }

    if(searchParams.get("finances") && searchParams.get("finances") !== null ) {
      financesParam = searchParams.get("finances")?.toString();

      if(financesParam)
      localStorage.setItem("finances", financesParam);
    }

    // const [campaign, setCampaign] = useState<CampaignType[]>([]);
    const [inputMood, setInputMood] = useState(moodParam || "5");
    const [inputSleep, setInputSleep] = useState(sleepParam || "5");
    const [inputSocial, setInputSocial] = useState(socialParam || "5");
    const [inputHealth, setInputHealth] = useState(healthParam || "5");
    const [inputFinances, setInputFinances] = useState(financesParam || "5");
    const [inputFamily, setInputFamily] = useState(familyParam || "5");
    const [inputRomance, setInputRomance] = useState(romanceParam || "5");
    const [inputWork, setInputWork] = useState(workParam || "5");

    type DataSet = {
          label: string;
          data: string[];
          fill: boolean;
          backgroundColor: string;
          borderColor: string;
          pointBackgroundColor: string;
          pointBorderColor: string;
          pointHoverBackgroundColor: string;
          pointHoverBorderColor: string;
    }

    type DataScore = {
      scores: [];
    }

    const getScoresHistory = () => {
      console.log("getScoresHistory");
      if (!localStorage.getItem("scores_history")) {

        console.log("getScoresHistory  not available");

        return [
          {
            label: "Users Scores",
            data: [inputMood, inputSleep, inputSocial, inputHealth, inputFamily, inputFinances, inputRomance, inputWork ],
            fill: true,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgb(54, 162, 235)',
            pointBackgroundColor: 'rgb(54, 162, 235)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(54, 162, 235)'
          }
        ];
        
      } else {

        const dataSets: DataSet[] = [
          {
            label: "Latest Scores",
            data: [inputMood, inputSleep, inputSocial, inputHealth, inputFamily, inputFinances, inputRomance, inputWork ],
            fill: true,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgb(54, 162, 235)',
            pointBackgroundColor: 'rgb(54, 162, 235)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(54, 162, 235)'
          }
        ];

        const storageArray = localStorage.getItem("scores_history");
        if(storageArray && JSON.parse(storageArray) !== null && isDemo) {
          JSON.parse(storageArray).forEach((dataScore: DataScore, index: number) => {

            const precision = 1; // 2 decimals
            const randomnum = Math.floor(Math.random() * (10 * precision - 1 * precision) + 1 * precision) / (1*precision);
            console.log({randomnum});

            const scoreArray: string[] = [];

            dataScore?.scores.forEach( (s: Score) => {
              scoreArray.push(s.score);
            }) 

            if(index < 5) {

              dataSets.push({
                label: `Users Scores ${index}`,
                data: scoreArray,
                fill: true,
                backgroundColor: `rgba(${randomnum/10 *255}, ${randomnum/10 * 255}, ${randomnum/10 *255}, ${randomnum/10})`,
                borderColor: `rgba(${randomnum/10 *255}, ${randomnum/10 * 255}, ${randomnum/10 *255}, ${randomnum/10})`,
                pointBackgroundColor: `rgba(${randomnum/10 *255}, ${randomnum/10 * 255}, ${randomnum/10 *255}, ${randomnum/10})`,
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: `rgba(${randomnum/10 *255}, ${randomnum/10 * 255}, ${randomnum/10 *255}, ${randomnum/10})`
              });

            }
            

          });
        }
        console.log("getScoresHistory dataSets: ", dataSets);
        return dataSets;
        
      }

    };

    const [chartData, setChartData] = useState({
      labels: ["Mood", "Sleep", "Social", "Health", "Family", "Finances", "Romance", "Work"], 
      datasets: [...getScoresHistory()]
    });

    useEffect(() => {
      // action on update of any input data ...

      setChartData({
        labels: ["Mood", "Sleep", "Social", "Health", "Family", "Finances", "Romance", "Work"], 
        datasets: [...getScoresHistory()]
      });


  }, [inputMood, inputSleep, inputSocial, inputHealth, inputFamily, inputFinances, inputRomance, inputWork]);
  

  // Update scores
  const updateScores: FormEventHandler<HTMLInputElement> = async (e) => {
      e.preventDefault();

      // set url params
      searchParams.set("mood", inputMood);
      searchParams.set("sleep", inputSleep);
      searchParams.set("social", inputSocial);
      searchParams.set("health", inputHealth);
      searchParams.set("family", inputFamily);
      searchParams.set("finances", inputFinances);
      searchParams.set("romance", inputRomance);
      searchParams.set("work", inputWork);
      setSearchParams(searchParams);

      // store locally
      const scoreArray: Score[] = [];
      if(inputMood){
        localStorage.setItem("mood", inputMood); 
        scoreArray.push({ type: "mood", score: inputMood});
      }
      if(inputSleep){
        localStorage.setItem("sleep", inputSleep);
        scoreArray.push({type: "sleep", score: inputSleep});
      }
      if(inputSocial){
        localStorage.setItem("social", inputSocial); 
        scoreArray.push({type: "social", score: inputSocial});
      }
      if(inputHealth){
        localStorage.setItem("health", inputHealth); 
        scoreArray.push({type: "health", score: inputHealth});
      }
      if(inputFamily){
        localStorage.setItem("family", inputFamily);
        scoreArray.push({ type: "family", score: inputFamily});
      }
      if(inputFinances){
        localStorage.setItem("finances", inputFinances);
        scoreArray.push({ type: "finances", score: inputMood});
      }
      if(inputRomance){
        localStorage.setItem("romance", inputRomance);
        scoreArray.push({ type: "romance", score: inputFinances}); 
      }
      if(inputWork){
        localStorage.setItem("work", inputWork);
        scoreArray.push({type: "work", score: inputWork});
      }

      const scoresHistoryArrayString = localStorage.getItem("scores_history");
      console.log({scoresHistoryArrayString});

      if(scoresHistoryArrayString){
        console.log({scoresHistoryArrayString});
        const scoresHistoryArray  = JSON.parse(scoresHistoryArrayString) || [];

        scoresHistoryArray.push({"date": Date.now().toString(), scores: scoreArray });
        localStorage.setItem("scores_history", JSON.stringify(scoresHistoryArray));
      } else {
        localStorage.setItem("scores_history", JSON.stringify([{"date":  Date.now().toString(), scores: scoreArray }]));
        console.log(localStorage.getItem("scores_history"));
      }

      if(!isDemo) {
        // post data to firebase...

        // Get a reference to the collection
        const collectionRef = collection(db, "campaigns/" + campaignId, "scores");

        // Define the data for the documents
        const dataArray = [
          { campaignId: campaignId, created_at: Date.now(), name: "mood", score: inputMood},
          { campaignId: campaignId, created_at: Date.now(), name: "sleep", score: inputSleep},
          { campaignId: campaignId, created_at: Date.now(), name: "finances", score: inputFinances},
          { campaignId: campaignId, created_at: Date.now(), name: "health", score: inputHealth},
          { campaignId: campaignId, created_at: Date.now(), name: "family", score: inputFamily},
          { campaignId: campaignId, created_at: Date.now(), name: "work", score: inputWork},
          { campaignId: campaignId, created_at: Date.now(), name: "romance", score: inputRomance},
          { campaignId: campaignId, created_at: Date.now(), name: "social", score: inputSocial},
        ];

        // Get a new write batch
        const batch = writeBatch(db);

        // Loop through the data array and add each document to the batch
        dataArray.forEach((data) => {
          const docRef = doc(collectionRef); // Create a new document reference with a unique ID
          batch.set(docRef, data); // Add the data to the batch for this document reference
        });


        // Commit at the end
        await batch.commit();
        window.location.href= "/";


      } else {

        let canvasEl = document.getElementsByTagName("canvas")[0];

        let canvasCopy = document.createElement("canvas");
        canvasCopy.width = canvasEl.width;
        canvasCopy.height = canvasEl.height;
        let ctx = canvasCopy.getContext("2d");

        if (ctx) {
          ctx.fillStyle = "white";
          ctx.fillRect(0, 0, canvasCopy.width, canvasCopy.height);
          ctx.font = 'italic 42px Arial';
          ctx.textAlign = 'center';
          ctx. textBaseline = 'middle';
          ctx.fillStyle = 'black';  // a color name or by using rgb/rgba/hex values
          ctx.fillText('www.talktoneuro.com', 230, 50); // text and position
          ctx.drawImage(canvasEl, 0, 0); // render canvas to new canvas
        };

        let image = canvasCopy.toDataURL("image/png").replace("image/png", "image/octet-stream");  // here is the most important part because if you dont replace you will get a DOM 18 exception.
        // window.location.href=image;

        let link = document.createElement('a');
        link.setAttribute('href', image);
        link.setAttribute('download', 'talk-to-neuro-scores.png');
        link.click();

      }
    };

    const clearScores = () => {
      localStorage.removeItem("scores_history");
      setChartData({
        labels: ["Mood", "Sleep", "Social", "Health", "Family", "Finances", "Romance", "Work"], 
        datasets: [...getScoresHistory()]
      });
    }

  return (
    <div className={!embedded ? "h-screen w-screen bg-teal-100 overflow-y-scroll" : "w-screen h-screen overscroll-none"}>
        <div className={!embedded ? "bg-white max-w-[1200px] w-full m-auto rounded-md shadow-xl mt-4" : "p-4 m-auto w-full overscroll-none"}>
        <div className="flex grid md:grid-flow-col md:ml-6">
          <div className="col-span-6">

            <div className="">
                <h1 className="text-2xl font-bold  text-gray-700 pl-4 pt-6 mr-4">How do you feel about the following areas, out of 10?</h1>
                <h2 className="text-lg  text-gray-700 pl-4 pt-2">Slide the sliders to adjust your scores.</h2>
            </div>

            <div className="p-2">
                {/* Radar */}
                <Radar
                  // redraw={true}
                  data={chartData}
                  options={{
                    elements: {
                      line: {
                        tension: 0.2,
                        
                      },
                    },
                    responsive: true,
                    maintainAspectRatio: true,
                    scales: {
                        r: {
                          pointLabels: {
                            font: {
                              size: 20
                            }
                          },
                          ticks: {
                            stepSize: 1,
                            maxTicksLimit: 10,
                          },
                          max: 10,
                          min: 0,
                        }
                    },
                    plugins: {
                      title: {
                        text: "",
                      },
                      legend: {
                          display: false
                      },
                    },
                    
                  }}
                />
            </div> 
            
            <div className="col-span-6 pr-4">
                {
                  isDemo ? 
                  <button onClick={clearScores} className="w-full mr-20 p-2 border-1 border-gray-600 rounded-lg text-gray-800 bg-gray-200 text-m shadow-md border-solid font-sm text-gray-700 hover:text-black-500 hover:bg-gray-300">
                  Clear All Scores
                </button> : ""
                }
            </div>

          </div>
          <div className="col-span-6 md-col-span-6">
            <div className="p-2">
                <MoodForm updateScores={updateScores}  inputMood={inputMood} setInputMood={setInputMood} inputSocial={inputSocial} setInputSocial={setInputSocial} inputSleep={inputSleep} setInputSleep={setInputSleep} inputHealth={inputHealth} setInputHealth={setInputHealth} inputFamily={inputFamily} setInputFamily={setInputFamily} inputWork={inputWork} setInputWork={setInputWork}  inputFinances={inputFinances} setInputFinances={setInputFinances} inputRomance={inputRomance} setInputRomance={setInputRomance} embedded={embedded} />
            </div>
          </div>
        </div>


        </div>
      </div>
  );
};

export default Moods;
