import { analytics } from "./Firebase";
import { logEvent } from "firebase/analytics";

const Landing = () => {

  logEvent(analytics, 'landing_viewed', {
    content_type: window.location.pathname === "/" ? 'landing' : "landing",
  });

  return (
  <div>

    <div className="container mx-auto md:h-screen">
      <div className="text-center px-3 lg:px-0">
        <h1
          className="my-4 text-2xl md:text-3xl lg:text-5xl font-bold text-gray-700 leading-tight"
        >
          We connect people with the right Therapist
        </h1>
        <p
          className="leading-normal text-gray-800 text-base md:text-xl lg:text-2xl mt-8 mb-8"
        >
          Let <PERSON><PERSON><PERSON> get to know you first, <br/> and then we'll match you with the right person...
        </p>

        {/* <a href="/demo">
        <button
          className="bg-gray-200 mx-auto lg:mx-0 font-extrabold rounded my-2 md:mb-4 py-4 px-8 shadow-lg"
        >
          Try The Free Demo
        </button>
        </a> */}
      </div>

      <div className="flex items-center w-full md:h-full mx-auto content-end overscroll-none my-8">
        <div id="demo" 
          className="browser-mockup py-2 flex flex-1 m-6 md:px-0 md:m-12 bg-white w-1/2 rounded shadow-xl overscroll-none"
        >
        {/* <!-- <img src="/assets/demo.png"  className="border-2 border-gray-100 border-solid rounded mr-4" alt="radar" /> --> */}
          {/* <iframe src="https://neuro-landing.web.app/embed" width="100%" height="800px" className="overscroll-none p-2"></iframe> */}
          <iframe src="/embed" width="100%" height="800px" className="overscroll-none p-2"></iframe>
        </div>
      </div>
    </div>

    <div id="anyone" className=" bg-white pt-4 md:mt-80">
      <div className="container max-w-5xl mx-auto m-8">
        <h2
          className="w-full my-2 text-5xl font-black leading-tight text-center text-gray-800"
        >
          For Anyone
        </h2>
        <div className="w-full mb-4">
          <div
            className="h-1 mx-auto gradient w-64 opacity-25 my-0 py-0 rounded-t"
          ></div>
        </div>

        <div className="flex flex-wrap">
          <div className="w-5/6 sm:w-1/2 p-6 mt-8">
            <div className="sm:mb-8 md:mb-24 text-xl">
              <h3 className="text-4xl text-gray-800 font-bold leading-none mb-3">
                Find targeted therapy for your needs.
              </h3>
              <p className="text-gray-600 mt-8">
                ✅ Track your mood and wellbeing first.
              </p>
              <p className="text-gray-600 mt-4">
               ✅ Understand yourself better, then meet the right therapist for you.
              </p>
            </div>
            <div className="mt-32 sm:mt-8 text-xl">
              <h3 className="text-4xl text-gray-800 font-bold leading-none mb-3">
                Get to know yourself through prompted self-journalling.
              </h3>
              <p className="text-gray-600 mt-8">
                📊 Build a summary of your progress,<br/>
                ready to work with your new therpaist.
              </p>
              <p className="text-gray-600 mt-4">
                ⚙️ Take these tools and data, <br/> with you on your new therapy journey.
              </p>
            </div>
          </div>
          <div className="w-full sm:w-1/2 p-6">
            <div className="md:mr-8">
              <img src="/journal.png" width="480px" height="480px" className="" alt="radar" />
           </div>
          </div>
        </div>
      </div>
    </div>

    <div className="bg-gray-100 border-b pb-4 fix">
      <div className="container mx-auto flex flex-wrap pt-4 pb-12">
        <h2
          className="w-full my-2 text-5xl font-black leading-tight text-center text-gray-800"
        >
          Then ... Match with a Therapist
        </h2>
        <div className="w-full mb-4">
          <div
            className="h-1 mx-auto gradient w-64 opacity-25 my-0 py-0 rounded-t"
          ></div>
        </div>

        <div className="w-full md:w-1/3 p-6 flex flex-col flex-grow flex-shrink">
          <div
            className="flex-1 bg-white rounded-t rounded-b-none overflow-hidden shadow"
          >
              <div className="min-h-[110px] m-4">
                <img alt="" src='/profile1-female.png' className="w-28 h-28 object-contain rounded-full" />
              </div>
              <h3 className="w-full text-gray-600 text-lg md:text-md px-6 mt-6">
                Dr F Neuro
              </h3>
              <div className="w-full font-bold text-xl text-gray-800 px-6">
                Hypno Therapist
              </div>
              <p className="text-gray-600 text-base px-6 mb-5">
                5 years professional experience. 
                <br/><br/>
                £60 per hour
              </p>
          </div>
          <div
            className="flex-none mt-auto bg-white rounded-b rounded-t-none overflow-hidden shadow px-6"
          >
            <div className="flex items-center justify-start mx-auto">
              <button
                disabled
                className="disabled:border-blue-200 bg-gray-700  lg:mx-0 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Match
              </button>
              <button
                disabled
                className="disabled:border-blue-200 disabled:border-gray-200  ml-4 bg-gray-500 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Reject
              </button>
            </div>
          </div>
        </div>

        <div className="w-full md:w-1/3 p-6 flex flex-col flex-grow flex-shrink">
          <div
            className="flex-1 bg-white rounded-t rounded-b-none overflow-hidden shadow"
          >
              <div className="min-h-[110px] m-4">
                <img alt="" src='/profile2-male.png' className="w-28 h-28 object-contain rounded-full" />
              </div>
              <h3 className="w-full text-gray-600 text-lg md:text-md px-6 mt-6">
                J D. Neuro
              </h3>
              <div className="w-full font-bold text-xl text-gray-800 px-6">
                EMDR Therapist
              </div>
              <p className="text-gray-600 text-base px-6 mb-5">
                3 years professional experience 
                <br/><br/>
                £50 per hour
              </p>
          </div>
          <div
            className="flex-none mt-auto bg-white rounded-b rounded-t-none overflow-hidden shadow px-6"
          >
            <div className="flex items-center justify-start mx-auto">
              <button
                disabled
                className="disabled:border-blue-200 bg-gray-700  lg:mx-0 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Match
              </button>
              <button
                disabled
                className="disabled:border-blue-200 disabled:border-gray-00  ml-4 bg-gray-500 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Reject
              </button>
            </div>
          </div>
        </div>

        <div className="w-full md:w-1/3 p-6 flex flex-col flex-grow flex-shrink">
          <div
            className="flex-1 bg-white rounded-t rounded-b-none overflow-hidden shadow"
          >
              <div className="min-h-[110px] m-4">
                <img alt="" src='/profile3-female.png' className="w-28 h-28 object-contain rounded-full" />
              </div>
              <h3 className="w-full text-gray-600 text-lg md:text-md px-6 mt-6">
                Dr. Z Neuro
              </h3>
              <div className="w-full font-bold text-xl text-gray-800 px-6">
                Trauma Therapist
              </div>
              <p className="text-gray-600 text-base px-6 mb-5">
                11 years professional experience 
                <br/><br/>
                £75 per hour
              </p>
          </div>
          <div
            className="flex-none mt-auto bg-white rounded-b rounded-t-none overflow-hidden shadow px-6"
          >
            <div className="flex items-center justify-start mx-auto">
              <button
                disabled
                className="disabled:border-blue-200 bg-gray-700  lg:mx-0 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Match
              </button>
              <button
                disabled
                className="disabled:border-blue-200 disabled:border-gray-200  ml-4 bg-gray-500 text-gray-100 font-extrabold rounded my-6 py-4 px-8 shadow-lg"
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
  );
};

export default Landing;
