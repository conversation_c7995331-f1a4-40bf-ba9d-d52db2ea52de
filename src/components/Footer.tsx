

const Footer = () => {

  return (
    <footer className="bg-white ">
      <div className="container mx-auto mt-8 px-8">
        <div className="w-full flex flex-col md:flex-row py-6">
          <div className="flex-1 mb-6">
            <a
              className="text-orange-600 no-underline hover:no-underline font-bold text-2xl lg:text-4xl"
              href="/"
            >
              <span className="inline-flex items-baseline">
                  <img src="/brain-solid-svgrepo-com.svg" alt="Neuro Logo" width="40" className="self-center w-35 h-35 rounded-full mx-1"  />
                  <span className="text-black">Neuro</span>
              </span>
            </a>
          </div>

          <div className="flex-1">
            <ul className="list-reset mb-6">
              <li className="mt-2 inline-block mr-2 md:block md:mr-0">
                Copyright Neuro &copy; 2025
              </li>
              <li className="mt-2 inline-block mr-2 md:block md:mr-0">
                <a
                  href="/terms"
                  className="font-light no-underline hover:underline text-gray-800 hover:text-orange-500"
                >
                  Terms & Conditions
                </a>
              </li>
            </ul>
          </div>

          <div className="flex-1">
            <ul className="list-reset mb-6">
              <li className="mt-2 inline-block mr-2 md:block md:mr-0">
                <a
                  href="mailto:<EMAIL>"
                  className="font-light no-underline hover:underline text-gray-800 hover:text-orange-500"
                  ><EMAIL></a>
              </li>

            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
