import { analytics } from "../Firebase";
import { FormEventHandler, useState } from "react";
import { useAuth } from '../contexts/AuthContext';
import SignupForm from "./SignUpForm";
import { getAuth, createUserWithEmailAndPassword, User } from "firebase/auth";
import {useSearchParams} from "react-router"
import { logEvent } from "firebase/analytics";

const Join = () => {
  const { getUser } = useAuth();
  const [currentUser, setCurrentUser] = useState<null|User>(getUser());

  const [inputEmail, setInputEmail] = useState("");
  const [inputPassword, setInputPassword] = useState("");
  const [searchParams] = useSearchParams();
  let accountType: string | undefined = undefined;

  if(searchParams.get("accountType") ) {
    accountType = searchParams.get("accountType")?.toString();

    if(accountType) {
      logEvent(analytics, 'join_therapist', {
          content_type: 'join',
          account_type: accountType
        });
    }
  }

  const auth = getAuth();

  //Create Invite Account
  const passwordSignup: FormEventHandler<HTMLInputElement> = async (e) => {
        e.preventDefault();
        console.log("emailLogin... ", inputEmail);

        if(inputPassword === "" || !inputPassword) {
            alert("Please enter a password");
            return;
        }

        // required
        // TODO - make a utility / common code
        if (inputEmail === "" && inputEmail.toLowerCase()
            .match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)) {
                alert("Please enter a valid email address");
                return;
        }

        createUserWithEmailAndPassword(auth, inputEmail, inputPassword)
        .then((userCredential) => {
            // Signed up 
            const user = userCredential.user;
            // ...
            console.log("Signed Up");
            setCurrentUser(user);

            if(accountType && accountType === 'therapist') {
              localStorage.setItem("neuro", accountType);
              logEvent(analytics, 'joined_therapist', {
                  content_type: 'joined',
                  account_type: accountType
                });
              window.location.href=("/thanks?accouuntType=" + accountType);
            };
        })
        .catch((error) => {
            const errorCode = error.code;
            const errorMessage = error.message;
            // ..
            console.log(errorCode);
            console.log(errorMessage);
        });
    };

  if(currentUser) {
    window.location.href=("/");
  }

  return (
    <div className="h-screen w-screen p-4 overflow-y-scroll">
        <div className="bg-white max-w-[800px] w-full m-auto rounded-md shadow-xl p-4 mt-4">
            <div className="col-span-8">
              <h1 className="text-4xl font-bold text-gray-700 pl-4 pt-6 leading-10">
                {accountType === "therapist" ? "Join Us As a Therapist" : "Join Us" }
              </h1>
              <div className="m-4">
                <p>Sign up with your email address to join Neuro alpha for therapists ...</p>
              </div>
              {currentUser ? "" : <SignupForm passwordSignup={passwordSignup} inputEmail={inputEmail} setInputEmail={setInputEmail} inputPassword={inputPassword} setInputPassword={setInputPassword}  /> }
            </div>
            
        </div>
    </div>
    
  );
};

export default Join;
