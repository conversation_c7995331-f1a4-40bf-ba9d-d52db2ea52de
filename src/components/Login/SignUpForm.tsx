import { FormEventHandler } from "react";

interface FormPropTypes {
  passwordSignup: FormEventHandler;
  inputEmail: string;
  setInputEmail: (input: string) => void;
  inputPassword: string;
  setInputPassword: (input: string) => void;
}

const SignupForm = ({ passwordSignup, inputEmail, setInputEmail, inputPassword, setInputPassword}: FormPropTypes) => {
  
    return (
        <form onSubmit={passwordSignup} className="grid bg-gray-200 p-8 rounded-lg mt-2">
            <div className="w-full text-lg m-1">
                <label htmlFor="email" >Email ...</label>
                <input
                    onChange={(e) => setInputEmail(e.target.value)}
                    name="email"
                    id="email" 
                    value={inputEmail}
                    className="w-full mt-2 p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-m rounded-lg mb-4 focus:outline-none focus:ring focus:border-red-500 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    type="text"
                    placeholder="<EMAIL>"
                />
            </div>
            <div className="w-full text-lg m-1">
                <label htmlFor="password" >Password ...</label>
                <input
                    onChange={(e) => setInputPassword(e.target.value)}
                    name="password" 
                    id="password"
                    value={inputPassword}
                    className="w-full mt-2 p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-m rounded-lg mb-4 focus:outline-none focus:ring focus:border-red-500 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    type="password"
                    placeholder="xxxxxx"
                />
            </div>

            <button className="ml-2 mt-2 p-6 w-full  rounded-lg text-gray-100 bg-teal-500 text-xl shadow-xl border-solid p-4 font-medium text-white hover:text-black-500 hover:bg-teal-700">
                    Sign Up
            </button>
        </form>    
  );
};

export default SignupForm;