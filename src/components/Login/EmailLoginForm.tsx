
import { FormEventHandler } from "react";

interface FormPropTypes {
  emailLogin: FormEventHandler;
  inputEmail: string;
  setInputEmail: (input: string) => void;
}

const EmailLoginForm = ({ emailLogin, inputEmail, setInputEmail}: FormPropTypes) => {
  
    return (
        <form onSubmit={emailLogin} className="grid bg-gray-200 p-8 rounded-lg mt-2">
            <div className="w-full text-lg m-1">
                <label htmlFor="name" >Enter your email address ...</label>
                <input
                    onChange={(e) => setInputEmail(e.target.value)}
                    name="name" 
                    value={inputEmail}
                    className="w-full mt-2 p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-m rounded-lg mb-4 focus:outline-none focus:ring focus:border-red-500 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    type="text"
                    placeholder="<EMAIL>"
                />
            </div>

            <button className="ml-2 mt-2 p-6 w-full border-2 border-gray-600 rounded-lg text-gray-800 bg-teal-700 text-xl shadow-xl border-solid p-4 font-medium text-gray-700 hover:text-black-500 hover:bg-teal-300">
                    Log in via Email Link
            </button>
        </form>    
  );
};

export default EmailLoginForm;