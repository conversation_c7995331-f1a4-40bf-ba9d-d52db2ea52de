import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import { useAuth } from '../contexts/AuthContext';
import LoginForm from "./LoginForm";
import { User } from "firebase/auth";
import { Link } from "react-router-dom";

const Join = () => {
  const { getUser, logIn, setCurrentUser } = useAuth();
  const [currentUser] = useState<null|User>(getUser());

  const [inputEmail, setInputEmail] = useState("");
  const [inputPassword, setInputPassword] = useState("");

  //Create Invite Account
  const passwordLogin: FormEventHandler<HTMLInputElement> = async (e) => {
        e.preventDefault();
        console.log("emailLogin... ", inputEmail);

        if(inputPassword === "" || !inputPassword) {
            alert("Please enter a password");
            return;
        }

        // required
        if (inputEmail === "" && inputEmail.toLowerCase()
            .match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)) {
                alert("Please enter a valid email address");
                return;
        }

        const currentUser: User | null = await logIn(inputEmail,inputPassword);
        setCurrentUser(currentUser);
  };

  if (currentUser) {
      window.location.href = "/"
  };

  return (
    <div className="h-screen w-screen p-4 overflow-y-scroll">
        <div className="bg-white max-w-[800px] w-full m-auto rounded-md shadow-xl p-8 mt-4">
            <div className="col-span-8">
              <h1 className="text-4xl font-bold text-gray-700 pt-6 leading-10">
                {currentUser ? "Already Signed In" : "Log In To Neuro" }
              </h1>
              <br/>
              <p>Log in to create or update your therapist profile</p>
            </div>
            {currentUser ? "" : <LoginForm passwordLogin={passwordLogin} inputEmail={inputEmail} setInputEmail={setInputEmail} inputPassword={inputPassword} setInputPassword={setInputPassword}  /> }
            <br/>
            <Link to="/forgot-password">Forgot Password?</Link>
        </div>
    </div>
    
  );
};

export default Join;
