export interface TherapistProfile {
  user_id: string;
  name: string;
  description: string;
  gender: 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
  works_with_gender: 'male' | 'female' | 'all' | 'non-binary';
  job_title: string;
  image_url: string;
  contact_email: string;
  hourly_rate: number;
  location: string;
  online_or_offline: 'online' | 'offline' | 'both';
  years_of_experience: number;
  qualifications: string[];
  success_description?: string;
  areas_of_concern?: string[];
  availability: string;
  embedding?: number[];
}

export const AREAS_OF_CONCERN = [
  'sleep',
  'relationships',
  'finances',
  'education',
  'work life',
  'social life',
  'family life',
  'physical health',
  'anxiety',
  'depression',
  'stress',
  'trauma'
] as const;