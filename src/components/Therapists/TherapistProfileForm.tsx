import { useState, useEffect } from 'react';
import { TherapistProfile, AREAS_OF_CONCERN } from './types';
import { useAuth } from '../contexts/AuthContext';
import { getFirestore, doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

interface Props {
  initialData?: TherapistProfile;
  onSubmit?: (data: TherapistProfile) => void;
  mode: 'create' | 'edit';
}

export const TherapistProfileForm = ({ initialData, onSubmit, mode }: Props) => {
  const { currentUser } = useAuth();
  const [formData, setFormData] = useState<TherapistProfile>({
    user_id: currentUser?.uid || '',
    availability: 'available',
    name: '',
    description: '',
    gender: 'prefer-not-to-say',
    works_with_gender: 'all',
    job_title: '',
    image_url: '',
    contact_email: currentUser?.email || '',
    hourly_rate: 30,
    location: '',
    online_or_offline: 'both',
    years_of_experience: 0,
    qualifications: [],
    success_description: '',
    areas_of_concern: [],
    ...initialData
  });

  const [qualification, setQualification] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  const fetchTherapistData = async () => {
    if (mode === 'edit' && currentUser?.uid) {
      try {
        const db = getFirestore();
        const therapistRef = doc(db, 'therapists', currentUser.uid);
        const therapistSnap = await getDoc(therapistRef);

        if (therapistSnap.exists()) {
          const therapistData = therapistSnap.data() as TherapistProfile;
          delete therapistData.embedding;
          setFormData(prevData => ({
            ...prevData,
            ...therapistData
          }));
        } else {
          setError('Therapist profile not found');
        }
      } catch (err) {
        console.error('Error fetching therapist data:', err);
        setError('Failed to load therapist profile');
      } finally {
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  };

  useEffect(()=>{
    // do stuff here...\
    if (mode === 'edit' && currentUser?.uid) {
      setLoading(true);
      fetchTherapistData();
    } else {
      setLoading(false);
    }
  }, []);


  if (loading) {
    return <div className="flex justify-center items-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-700"></div>
    </div>;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    if(mode === 'create' && !currentUser) {
      setError('Please log in to create a profile');
      return;
    } else {
      try {
        const db = getFirestore();
        const therapistRef = doc(db, 'therapists', currentUser!.uid);
        await setDoc(therapistRef, formData, { merge: true });
        
        if (onSubmit) {
          onSubmit(formData);
        }
      } catch (err) {
        setError('Failed to save profile');
        console.error(err);
      }
    }

    if(mode === 'edit' && !currentUser) {
      setError('Please log in to create a profile');
      return;
    } else {

      console.log({formData});

      try {
        const db = getFirestore();
        const therapistRef = doc(db, 'therapists', currentUser!.uid);
        await updateDoc(therapistRef, { ...formData });
        
        if (onSubmit) {
          onSubmit(formData);
        }
      } catch (err) {
        setError('Failed to save profile');
        console.error(err);
      }
    }


  };

  const addQualification = () => {
    if (qualification.trim()) {
      setFormData({
        ...formData,
        qualifications: [...formData.qualifications, qualification.trim()]
      });
      setQualification('');
    }
  };

  const removeQualification = (index: number) => {
    const newQualifications = [...formData.qualifications];
    newQualifications.splice(index, 1);
    setFormData({ ...formData, qualifications: newQualifications });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 mx-auto p-4">
      {error && <div className="text-red-500">{error}</div>}

      <div className='flex flex-row gap-4 bg-gray-100 p-4 rounded-md'>

        <div className="flex flex-col w-1/2 mr-4">
          <h3 className='text-lg font-bold text-gray-700'>Your Details</h3>
          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
              placeholder='Your Full Professional Name'
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Bio/Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="whitespace-pre-wrap p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              rows={4}
              required
              placeholder='A description of your practice, could be CV style or a profile from anohter service'
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Your Gender / Identity</label>
            <select
              value={formData.gender}
              onChange={(e) => setFormData({ ...formData, gender: e.target.value as any })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="non-binary">Non-binary</option>
              <option value="prefer-not-to-say">Prefer not to say</option>
            </select>
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Profile Image</label>
            <div className="mt-1 flex items-center gap-4">
              {formData.image_url && (
                <img 
                  src={formData.image_url} 
                  alt="Profile preview" 
                  className="h-20 w-20 object-cover rounded-full"
                />
              )}
              <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <input
                  type="file"
                  className="hidden"
                  accept="image/jpeg,image/png,image/gif"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (!file || !currentUser?.uid) return;

                    try {
                      // Get storage reference
                      const storage = getStorage();
                      const fileRef = ref(storage, `therapist-profiles/${currentUser.uid}/${file.name}`);

                      // Upload file
                      await uploadBytes(fileRef, file);

                      // Get download URL
                      const downloadURL = await getDownloadURL(fileRef);

                      // Update form data
                      setFormData({ ...formData, image_url: downloadURL });

                      // Update Firestore document
                      const db = getFirestore();
                      const therapistRef = doc(db, 'therapists', currentUser.uid);
                      await setDoc(therapistRef, { image_url: downloadURL }, { merge: true });
                    } catch (err) {
                      console.error('Error uploading image:', err);
                      setError('Failed to upload image');
                    }
                  }}
                />
                Upload Image
              </label>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              JPEG, PNG or GIF files are accepted
            </p>
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Contact Email (optional)</label>
            <input
              type="email"
              value={formData.contact_email}
              onChange={(e) => setFormData({ ...formData, contact_email: e.target.value })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Qualifications (optional)</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={qualification}
                onChange={(e) => setQualification(e.target.value)}
                placeholder="e.g. Level 4 Counselling"
                className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
              <button
                type="button"
                onClick={addQualification}
                className="p2mt-1 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Add
              </button>
            </div>
            <div className="mt-2 space-y-2">
              {formData.qualifications.map((qual, index) => (
                <div key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded">
                  <span>{qual}</span>
                  <button
                    type="button"
                    onClick={() => removeQualification(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Success Description</label>
            <textarea
              value={formData.success_description}
              onChange={(e) => setFormData({ ...formData, success_description: e.target.value })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              rows={4}
              placeholder='A description of your success stories or the kind of people you have helped previously'
            />
          </div>

        </div>

        <div className="flex flex-col w-1/2 mr-4">
          <h3 className='text-lg font-bold text-gray-700'>Your Practice</h3>
          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Hourly Rate (£)</label>
            <input
              type="number"
              min="30"
              value={formData.hourly_rate}
              onChange={(e) => setFormData({ ...formData, hourly_rate: Number(e.target.value) })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Location</label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
              placeholder='London, UK'
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Session Type</label>
            <select
              value={formData.online_or_offline}
              onChange={(e) => setFormData({ ...formData, online_or_offline: e.target.value as any })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="online">Online Only</option>
              <option value="offline">In-person Only</option>
              <option value="both">Both Online and In-person</option>
            </select>
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Years of Experience</label>
            <input
              type="text" 
              inputMode="numeric" 
              pattern="[0-9]*"
              min="0"
              placeholder='10'
              value={formData.years_of_experience}
              onChange={(e) => setFormData({ ...formData, years_of_experience: Number(e.target.value) })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Work with</label>
            <select
              value={formData.works_with_gender}
              onChange={(e) => setFormData({ ...formData, works_with_gender: e.target.value as any })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="all">All Genders</option>
              <option value="male">Males Only</option>
              <option value="female">Females Only</option>
              <option value="non-binary">Non-binary Only</option>
            </select>
          </div>

          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Type of Therapy</label>
            <input
              type="text"
              value={formData.job_title}
              onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
              className="p-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

        
          <div className='my-2'>
            <label className="block text-sm font-medium text-gray-700">Areas of Specialty</label>
            <div className="mt-2 grid grid-cols-2 gap-2">
              {AREAS_OF_CONCERN.map((area) => (
                <label key={area} className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.areas_of_concern?.includes(area)}
                    onChange={(e) => {
                      const newAreas = e.target.checked
                        ? [...formData.areas_of_concern!, area]
                        : formData.areas_of_concern?.filter(a => a !== area);
                      setFormData({ ...formData, areas_of_concern: newAreas });
                    }}
                    className="p2 rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  />
                  <span className="ml-2">{area}</span>
                </label>
              ))}
            </div>
          </div>
          
        </div>

      </div>
      
      


      <div>
        <button
          type="submit"
          className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-2xl font-large rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {mode === 'create' ? 'Create Profile' : 'Update Profile'}
        </button>
      </div>
    </form>
  );
};
