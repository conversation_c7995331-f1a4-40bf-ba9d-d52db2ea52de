import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import { TherapistProfileForm } from './TherapistProfileForm';
import type { TherapistProfile } from './types';

export const CreateTherapistProfile = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [exists, setExists] = useState(false);

  useEffect(() => {
    const checkExisting = async () => {
      if (!currentUser) {
        navigate('/login');
        return;
      }

      const db = getFirestore();
      const docRef = doc(db, 'therapists', currentUser.uid);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        setExists(true);
        navigate('/edit-profile');
      }
    };

    checkExisting();
  }, [currentUser, navigate]);

  const handleSubmit = (data: TherapistProfile) => {
    console.log('Profile created:', data);
    navigate(`/therapists/profile/${currentUser?.uid}`);
  };

  if (exists) {
    return null;
  }

  return (
    <div className="container mx-auto mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-4 px-4">Create Your Therapist Profile
        {currentUser ?
        ` for ${currentUser?.email}` : ""}
      </h1>
      <TherapistProfileForm mode="create" onSubmit={handleSubmit} />
    </div>
  );
};