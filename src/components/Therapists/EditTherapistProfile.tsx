import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import { TherapistProfileForm } from './TherapistProfileForm';
import type { TherapistProfile } from './types';

export const EditTherapistProfile = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [profileData, setProfileData] = useState<TherapistProfile | undefined>(undefined);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!currentUser) {
        navigate('/login');
        return;
      }

      const db = getFirestore();
      const docRef = doc(db, 'therapists', currentUser.uid);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        navigate('/therapists/create-profile');
        return;
      }

      setProfileData(docSnap.data() as TherapistProfile);
      setLoading(false);
    };

    fetchProfile();
  }, [currentUser, navigate]);

  const handleSubmit = (data: TherapistProfile) => {
    console.log('Profile updated:', data);
    navigate('/therapists/profile');
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Edit Your Therapist Profile</h1>
      <TherapistProfileForm mode="edit" initialData={profileData} onSubmit={handleSubmit} />
    </div>
  );
};