import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import type { TherapistProfile } from './types';

export const PublicViewTherapistProfile = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [profileData, setProfileData] = useState<TherapistProfile | undefined>(undefined);
  const [loading, setLoading] = useState(false);

  const { id } = useParams();

  useEffect(() => {
    const fetchProfile = async () => {
      // if (!currentUser) {
      //   navigate('/login');
      //   return;
      // }

      if(id) {

        const db = getFirestore();
        const docRef = doc(db, 'therapists', id);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          navigate('/create-profile');
          return;
        }

        console.log({docSnap});
  
        setProfileData(docSnap.data() as TherapistProfile);
        setLoading(false);
      }

    };

    fetchProfile();
  }, [currentUser, navigate]);


  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

      {profileData && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {/* Header Section */}
          <div className="px-6 py-8 border-b bg-gray-100 border-gray-200 flex items-center space-x-6">
            
            <img 
              src={profileData.image_url ? profileData.image_url : '/profile1-female.png'} 
              alt={profileData.name}
              className="w-32 h-32 rounded-full object-cover border-4 border-teal-100"
            />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{profileData.name}</h2>
              <p className="text-lg text-teal-600 font-medium">{profileData.job_title}</p>
              <p className="text-gray-500">£{profileData.hourly_rate}/hour • {profileData.years_of_experience} years experience</p>
              <p className="text-gray-500 capitalize">{profileData.availability}</p>
            </div>
          </div>


          {/* Main Content Grid */}
          <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left Column */}
            <div className="space-y-6 col-span-2">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 ml-2">About Me</h3>
                <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-600 mt-2 text-lg w-4/5 ml-2">{profileData.description}</p>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">

              {profileData?.areas_of_concern ?
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Areas of Expertise</h3>
                <div className="flex flex-wrap gap-2">
                  {profileData?.areas_of_concern?.map((area, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 bg-teal-50 text-teal-700 rounded-full text-sm"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>

              : ""}

              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Practice Details</h3>
                <p className="flex items-center text-gray-600">
                  <span className="font-medium w-32">Location:</span> {profileData.location}
                </p>
                <p className="flex items-center text-gray-600">
                  <span className="font-medium w-32">Session Type:</span> {profileData.online_or_offline}
                </p>
                <p className="flex items-center text-gray-600">
                  <span className="font-medium w-32">Gender:</span> {profileData.gender}
                </p>
                <p className="flex items-center text-gray-600">
                  <span className="font-medium w-32">Works with:</span> {profileData.works_with_gender}
                </p>
              </div>
              {profileData.success_description ?
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Success Stories</h3>
                <p className="text-gray-600">{profileData.success_description}</p>
              </div>
              : null}

              {profileData.qualifications ?
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Qualifications</h3>
                <ul className="list-disc list-inside text-gray-600">
                  {profileData.qualifications.map((qual, index) => (
                    <li className='list-disc' key={index}>{qual}</li>
                  ))}
                </ul>
              </div>
              : ""}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
