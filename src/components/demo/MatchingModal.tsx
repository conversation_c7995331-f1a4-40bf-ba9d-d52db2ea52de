import { FormEvent, useState } from "react";
import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react'
import { handleMatchRequest } from '../../utils/firebaseFunctions';

interface MatchingModalProps {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    therapistId: string;
    therapistName: string;
}

const MatchingModal = ({ isOpen, setIsOpen, therapistId, therapistName }: MatchingModalProps) => {
    const [email, setEmail] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);
    const [therapistContactEmail, setTherapistContactEmail] = useState<string | null>(null);

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setError(null);

        try {
            const response = await handleMatchRequest(therapistId, email);
            console.log("MatchingModal handleSubmit response: ", response);

            if (!response.success) {
                throw new Error('Failed to submit match request');
            };

            // Store therapist contact email if provided
            if (response.therapistContactEmail) {
                setTherapistContactEmail(response.therapistContactEmail);
            }

            setSuccess(true);

            if(!therapistContactEmail) {
                setTimeout(() => {
                    handleClose();
                }, 3000); // removed timeout to show contact email
            };

        } catch (err: any) {
            console.error('Error submitting match request:', err);
            setError(err.message || 'Failed to submit match request');
            setTimeout(() => {
                handleClose();
            }, 2000);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        setSuccess(false);
        setError(null);
        setTherapistContactEmail(null);
        setEmail("");
    };

    return (
        <Dialog open={isOpen} onClose={handleClose} className="relative z-10">
            <DialogBackdrop
                className="fixed inset-0 bg-gray-500/75 transition-opacity"
            />
            <div className="fixed inset-0 z-10 overflow-y-auto">
                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <DialogPanel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                        <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                            <DialogTitle as="h3" className="text-lg font-semibold leading-6 text-gray-900 mb-4">
                                Match Request with {therapistName}
                            </DialogTitle>

                            {error && (
                                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                                    {error}
                                </div>
                            )}

                            {success && (
                                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                                    <div className="font-semibold">Match request sent successfully!</div>
                                    {therapistContactEmail && (
                                        <div className="mt-2 text-sm">
                                            <strong>Therapist Contact:</strong> {therapistContactEmail}
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="mt-4 mb-6 p-4 bg-blue-50 border-l-4 border-blue-400 text-blue-700">
                                {/* <p className="font-medium">Therapist Contact Email</p> */}
                                <p>You may recieve a contact email in return for yours here, if {therapistName} is open to new clients.</p>
                            </div>
                            <form onSubmit={handleSubmit}>
                                <div className="my-4">
                                    <label htmlFor="email" className="mt-2 block text-sm font-medium text-gray-700">
                                        Enter your email address:
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        className="p-2 border text-md mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                                    <button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="inline-flex w-full justify-center rounded-md bg-teal-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-teal-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-teal-600 sm:col-start-2"
                                    >
                                        {isSubmitting ? 'Sending...' : 'Send A Match Request'}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleClose}
                                        className="mt-3 inline-flex w-full justify-center rounded-md bg-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-100 sm:col-start-1 sm:mt-0"
                                    >
                                        Close
                                    </button>
                                </div>
                            </form>
                        </div>
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
};

export default MatchingModal;