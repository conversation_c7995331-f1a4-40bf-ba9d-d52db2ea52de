
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { doc, updateDoc, getDoc, collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../Firebase';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { getStoredResultId } from '../../utils/assessmentStorage';

interface QuestionSet {
  title: string;
  description: string;
  questions: string[];
  scores: number[];
}

interface ConditionClassificationProps {
  resultId?: string;
  onCompletionChange?: (percentage: number) => void;
}

const ConditionClassification = ({ resultId, onCompletionChange }: ConditionClassificationProps) => {
  const { currentUser, getUser, signInAnonymously } = useAuth();
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    depression: false,
    anxiety: false,
    psychosis: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize question sets with their respective questions and default scores
  const [questionSets, setQuestionSets] = useState<{ [key: string]: QuestionSet }>({
    depression: {
      title: 'Depression (PHQ)',
      description: 'Over the last 2 weeks, how often have you been bothered by any of the following problems?',
      questions: [
        'Little interest or pleasure in doing things.',
        'Feeling down, depressed, or hopeless.',
        'Trouble falling or staying asleep, or sleeping too much.',
        'Feeling tired or having little energy.',
        'Poor appetite or overeating.',
        'Feeling bad about yourself, or that you are a failure or have let yourself or your family down.',
        'Trouble concentrating on things, such as reading the newspaper or watching television.',
        'Moving or speaking so slowly that other people could have noticed. Or the opposite, being so fidgety or restless that you have been moving around a lot more than usual.',
        // 'Thoughts that you would be better off dead, or of hurting yourself in some way.'
      ],
      scores: Array(8).fill(-1)
    },
    anxiety: {
      title: 'Anxiety (GAD)',
      description: 'Over the last 2 weeks, how often have you been bothered by any of the following problems?',
      questions: [
        'Feeling nervous, anxious, or on edge.',
        'Not being able to stop or control worrying.',
        'Worrying too much about different things.',
        'Trouble relaxing.',
        'Being so restless that it\'s hard to sit still.',
        'Becoming easily annoyed or irritable.',
        'Feeling afraid as if something awful might happen.'
      ],
      scores: Array(7).fill(-1)
    },
    psychosis: {
      title: 'Psychosis Screening (PANNS)',
      description: 'Over the last month, how often have you experienced any of the following?',
      questions: [
        'Have you experienced things that others could not see or hear, or believed things that others said were not true (e.g., hearing voices or feeling watched)?',
        'Have you felt emotionally numb, withdrawn from others, or found it hard to start or finish everyday tasks?',
        'Have you had trouble organizing your thoughts or speaking in a way others could easily follow?',
        'Have you felt down, anxious, or hopeless?',
        'Have you felt unusually irritable, suspicious of others, or acted aggressively?'
      ],
      scores: Array(5).fill(-1)
    }
  });

  // Handle score change for a specific question in a specific section
  const handleScoreChange = (sectionKey: string, index: number, value: number) => {
    setQuestionSets(prev => {
      const newQuestionSets = { ...prev };
      newQuestionSets[sectionKey].scores[index] = value;
      return newQuestionSets;
    });
  };

  // Toggle accordion section
  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  // Calculate completeness percentage for a section
  const getCompletenessScore = (sectionKey: string) => {
    const scores = questionSets[sectionKey].scores;
    const answeredQuestions = scores.filter(score => score >= 0).length;
    return Math.round((answeredQuestions / scores.length) * 100);
  };

  // Get severity level based on score
  const getSeverityLevel = (sectionKey: string) => {
    const score = calculateTotalScore(sectionKey);
    
    if (sectionKey === 'depression') {
      if (score <= 4) return { level: 'Minimal', color: 'text-green-600' };
      if (score <= 9) return { level: 'Mild', color: 'text-yellow-600' };
      if (score <= 14) return { level: 'Moderate', color: 'text-orange-600' };
      if (score <= 19) return { level: 'Mod. Severe', color: 'text-red-600' };
      return { level: 'Severe', color: 'text-red-800' };
    } else if (sectionKey === 'anxiety') {
      if (score <= 4) return { level: 'Minimal', color: 'text-green-600' };
      if (score <= 9) return { level: 'Mild', color: 'text-yellow-600' };
      if (score <= 14) return { level: 'Moderate', color: 'text-orange-600' };
      return { level: 'Severe', color: 'text-red-800' };
    } else if (sectionKey === 'psychosis') {
      if (score <= 5) return { level: 'Low Risk', color: 'text-green-600' };
      if (score <= 10) return { level: 'Moderate Risk', color: 'text-orange-600' };
      return { level: 'High Risk', color: 'text-red-800' };
    }
    
    // Fallback for unknown section keys
    return { level: 'Unknown', color: 'text-gray-600' };
  };

  // Calculate total score for a question set
  const calculateTotalScore = (tabKey: string) => {
    return questionSets[tabKey].scores
      .filter(score => score >= 0)
      .reduce((sum, score) => sum + score, 0);
  };

  // Get interpretation based on score
  const getInterpretation = (tabKey: string, score: number) => {
    if (tabKey === 'depression') {
      if (score <= 4) return 'Minimal depression';
      if (score <= 9) return 'Mild depression';
      if (score <= 14) return 'Moderate depression';
      if (score <= 19) return 'Moderately severe depression';
      return 'Severe depression';
    } else if (tabKey === 'anxiety') {
      if (score <= 4) return 'Minimal anxiety';
      if (score <= 9) return 'Mild anxiety';
      if (score <= 14) return 'Moderate anxiety';
      return 'Severe anxiety';
    } else if (tabKey === 'psychosis') {
      if (score <= 5) return 'Low risk';
      if (score <= 10) return 'Moderate risk';
      return 'High risk - further assessment recommended';
    }
    return '';
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    if(getCompletenessScore("depression") && getCompletenessScore("anxiety") && getCompletenessScore("psychosis") === 0) {
      return
    };

    setIsSubmitting(true);
    
    try {
      // Here you would typically send the data to your backend
      // For now, we'll just simulate a successful submission
      
      // Calculate scores for all tabs
      const depressionScore = calculateTotalScore('depression');
      const anxietyScore = calculateTotalScore('anxiety');
      const psychosisScore = calculateTotalScore('psychosis');
      
      // Create a results object
      const results = {
        depression: {
          score: depressionScore,
          interpretation: getInterpretation('depression', depressionScore),
          responses: questionSets.depression.scores,
          validResponses: questionSets.depression.scores.filter(score => score >= 0)
        },
        anxiety: {
          score: anxietyScore,
          interpretation: getInterpretation('anxiety', anxietyScore),
          responses: questionSets.anxiety.scores,
          validResponses: questionSets.anxiety.scores.filter(score => score >= 0)
        },
        psychosis: {
          score: psychosisScore,
          interpretation: getInterpretation('psychosis', psychosisScore),
          responses: questionSets.psychosis.scores,
          validResponses: questionSets.psychosis.scores.filter(score => score >= 0)
        },
        timestamp: new Date().toISOString()
      };
      
      console.log('Screening results:', results);

      // Get current user and assessment result ID
      let user = getUser();
      const assessmentResultId = getStoredResultId();

      // Ensure we have a user (anonymous if needed) for tracking
      if (!user) {
        try {
          user = await signInAnonymously();
          console.log('Created anonymous user for screening:', user?.uid);
        } catch (authError) {
          console.error('Failed to create anonymous user:', authError);
        }
      }

      console.log('User for screening save:', user?.uid || 'anonymous');
      console.log('Assessment result ID:', assessmentResultId || 'none');

      // Save screening results directly to Firestore
      try {
        const screeningData = {
          ...results,
          uid: user?.uid || 'anonymous',
          assessmentResultId: assessmentResultId || null,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(collection(db, 'screenings'), screeningData);

        console.log('Screening results saved successfully with ID:', docRef.id);

        // Optionally redirect to results page
        // navigate('/screening-results');

      } catch (saveError) {
        console.error('Error saving screening results:', saveError);
        // Still show success to user since the screening was completed
        // but log the database save error

      }
      
    } catch (error) {
      console.error('Error submitting assessment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate and update completion percentage
  useEffect(() => {
    const calculateCompletion = () => {
      let answeredQuestions = 0;
      let totalQuestions = 0;
      
      // Count answered questions across all tabs
      Object.values(questionSets).forEach(set => {
        totalQuestions += set.questions.length;
        set.scores.forEach(score => {
          if (score >= 0) {
            answeredQuestions++;
          }
        });
      });
      
      const percentage = Math.round((answeredQuestions / totalQuestions) * 100);
      
      // Propagate completion percentage to parent component
      if (onCompletionChange) {
        onCompletionChange(percentage);
      }
      
      return percentage;
    };
    
    calculateCompletion();
  }, [questionSets, onCompletionChange]);
  
  // Save scores to Firestore when they change
  useEffect(() => {
    const saveScores = async () => {
      if (!resultId) return;

      try {
        const resultRef = doc(db, 'results', resultId);
        const resultDoc = await getDoc(resultRef);

        if (resultDoc.exists()) {
          await updateDoc(resultRef, {
            'classification': {
              depression: questionSets.depression.scores,
              anxiety: questionSets.anxiety.scores,
              psychosis: questionSets.psychosis.scores,
              validResponses: {
                depression: questionSets.depression.scores.filter(score => score >= 0),
                anxiety: questionSets.anxiety.scores.filter(score => score >= 0),
                psychosis: questionSets.psychosis.scores.filter(score => score >= 0)
              },
              totalScores: {
                depression: calculateTotalScore('depression'),
                anxiety: calculateTotalScore('anxiety'),
                psychosis: calculateTotalScore('psychosis')
              },
              completeness: {
                depression: getCompletenessScore('depression'),
                anxiety: getCompletenessScore('anxiety'),
                psychosis: getCompletenessScore('psychosis')
              },
              timestamp: new Date()
            }
          });
        }
      } catch (error) {
        console.error('Error saving classification scores:', error);
      }
    };

    // Debounce the save operation to avoid too many writes
    const timeoutId = setTimeout(saveScores, 1000);
    return () => clearTimeout(timeoutId);
  }, [questionSets, resultId]);

  return (
    <div className="mx-auto p-4">
      {/* <div className="mb-4 text-gray-800 p-2">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6">
          ⚠ This is a screening tool only and not a diagnostic instrument. If you are in unewll or in crisis, please seek emergency help immediately.
        </div>
      </div> */}

      {/* Accordion navigation */}
      <div className="bg-gray-100 rounded-lg shadow-md">
        {Object.entries(questionSets).map(([sectionKey, section]) => {
          const completeness = getCompletenessScore(sectionKey);
          const severity = getSeverityLevel(sectionKey);
          const isExpanded = expandedSections[sectionKey];

          return (
            <div key={sectionKey} className="border-b border-gray-50 last:border-b-0">
              {/* Accordion Header */}
              <button
                onClick={() => toggleSection(sectionKey)}
                className="w-full bg-gray-200 px-6 py-4 text-left hover:bg-gray-200 focus:outline-none focus:bg-gray-200 transition-colors duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {section.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-gray-600">
                        Completeness: <span className="font-medium">{completeness}%</span>
                      </span>
                      <span className="text-gray-600">
                          Severity: <span className={`font-medium ${severity.color}`}>{severity.level}</span>
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    {isExpanded ? (
                      <ChevronUpIcon className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                    )}
                  </div>
                </div>
              </button>

              {/* Accordion Content */}
              {isExpanded && (
                <div className="px-6 pb-6">
                  <p className="my-8 text-gray-900 text-lg text-bold">{section.description}</p>

                  <div className="mb-4 grid grid-cols-6 gap-2 text-sm font-medium text-gray-500 border-b border-gray-200 pb-2">
                    <div className="col-span-2 text-left">Question</div>
                    <div className="hidden text-center">Default</div>
                    <div className="text-center">Never</div>
                    <div className="text-center">Sometimes</div>
                    <div className="text-center">Often</div>
                    <div className="text-center">Always</div>
                  </div>

                  {section.questions.map((question, index) => (
                    <div key={index} className="mb-6 border-b pb-4">
                      <div className="grid grid-cols-6 gap-2 items-center">
                        <div className="col-span-2">
                          <p className="text-gray-700 text-sm">{index + 1}. {question}</p>
                        </div>

                        {/* Default (-1) */}
                        <div className="hidden text-center">
                          <input
                            type="radio"
                            id={`${sectionKey}-q${index}-default`}
                            name={`${sectionKey}-q${index}`}
                            value="-1"
                            checked={section.scores[index] === -1}
                            onChange={(e) => handleScoreChange(sectionKey, index, parseInt(e.target.value))}
                            className="w-4 h-4 text-gray-600 bg-gray-100 border-gray-300 focus:ring-gray-500"
                          />
                          <label htmlFor={`${sectionKey}-q${index}-default`} className="block text-xs text-gray-500 mt-1">
                            {section.scores[index] === -1 && "Default"}
                          </label>
                        </div>

                        {/* Never (0) */}
                        <div className="text-center">
                          <input
                            type="radio"
                            id={`${sectionKey}-q${index}-never`}
                            name={`${sectionKey}-q${index}`}
                            value="0"
                            checked={section.scores[index] === 0}
                            onChange={(e) => handleScoreChange(sectionKey, index, parseInt(e.target.value))}
                            className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500"
                          />
                          <label htmlFor={`${sectionKey}-q${index}-never`} className="block text-xs text-gray-700 mt-1">
                            {section.scores[index] === 0 && "Never"}
                          </label>
                        </div>

                        {/* Sometimes (1) */}
                        <div className="text-center">
                          <input
                            type="radio"
                            id={`${sectionKey}-q${index}-sometimes`}
                            name={`${sectionKey}-q${index}`}
                            value="1"
                            checked={section.scores[index] === 1}
                            onChange={(e) => handleScoreChange(sectionKey, index, parseInt(e.target.value))}
                            className="w-4 h-4 text-yellow-600 bg-gray-100 border-gray-300 focus:ring-yellow-500"
                          />
                          <label htmlFor={`${sectionKey}-q${index}-sometimes`} className="block text-xs text-gray-700 mt-1">
                            {section.scores[index] === 1 && "Sometimes"}
                          </label>
                        </div>

                        {/* Often (2) */}
                        <div className="text-center">
                          <input
                            type="radio"
                            id={`${sectionKey}-q${index}-often`}
                            name={`${sectionKey}-q${index}`}
                            value="2"
                            checked={section.scores[index] === 2}
                            onChange={(e) => handleScoreChange(sectionKey, index, parseInt(e.target.value))}
                            className="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 focus:ring-orange-500"
                          />
                          <label htmlFor={`${sectionKey}-q${index}-often`} className="block text-xs text-gray-700 mt-1">
                            {section.scores[index] === 2 && "Often"}
                          </label>
                        </div>

                        {/* Always (3) */}
                        <div className="text-center">
                          <input
                            type="radio"
                            id={`${sectionKey}-q${index}-always`}
                            name={`${sectionKey}-q${index}`}
                            value="3"
                            checked={section.scores[index] === 3}
                            onChange={(e) => handleScoreChange(sectionKey, index, parseInt(e.target.value))}
                            className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500"
                          />
                          <label htmlFor={`${sectionKey}-q${index}-always`} className="block text-xs text-gray-700 mt-1">
                            {section.scores[index] === 3 && "Always"}
                          </label>
                        </div>
                      </div>
                    </div>
                  ))}

                  <div className="mt-8 p-4 bg-gray-100 rounded-lg">
                    <p className="font-semibold">Current Score: {calculateTotalScore(sectionKey)}</p>
                    <p>Interpretation: {getInterpretation(sectionKey, calculateTotalScore(sectionKey))}</p>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Submit button */}
        <div className="p-6 bg-gray-200">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-full px-4 py-3 bg-teal-500 text-white rounded-lg hover:bg-teal-600 disabled:bg-gray-400 font-medium"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Screening'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConditionClassification;
