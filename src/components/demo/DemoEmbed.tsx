import MoodAssessment from "./MoodAssessment";
import {useSearchParams} from "react-router"

const DemoEmbed = () => {

    const [searchParams] = useSearchParams();

    let organisationId : string | undefined = undefined;

    if(searchParams.get("organisation_id") !== null) {
        organisationId = searchParams.get("organisation_id")?.toString();
    };

    console.log({organisationId});

    return (
      <div className="h-screen w-screen overflow-y-scroll">
         <MoodAssessment embedded={true} organisationId={organisationId} />
      </div>
    );
  };
  
  export default DemoEmbed;
  