import React, { useState } from 'react';

interface ChatFormProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

const ChatForm: React.FC<ChatFormProps> = ({ onSendMessage, isLoading }) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      setMessage('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="chat-form">
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Type your message here..."
        disabled={isLoading}
        className="chat-input"
      />
      <button 
        type="submit" 
        disabled={isLoading || !message.trim()} 
        className="chat-send-button"
      >
        {isLoading ? 'Sending...' : 'Send'}
      </button>
    </form>
  );
};

export default ChatForm;