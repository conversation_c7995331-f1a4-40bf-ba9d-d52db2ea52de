
import React, { useState, useEffect, useRef } from 'react';
import { httpsCallable } from 'firebase/functions';
import { ref, push, onValue, set } from 'firebase/database';
import { functions, database } from '../Firebase'; // Adjust path as needed
import { useAuth } from '../contexts/AuthContext'; // Adjust path as needed
import ChatForm from './ChatForm';
import './Chat.css'; // You'll need to create this CSS file
import Markdown from 'react-markdown'

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: number;
}

interface ChatSession {
  messages: Message[];
  scores?: {
    depression: number;
    anxiety: number;
  };
  completed: boolean;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [assessmentComplete, setAssessmentComplete] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { currentUser } = useAuth();
  
  // Initial prompt for the AI
  const initialPrompt = 
  `You are a warm, supportive, and non-judgmental therapist. 
  Engage the user in a gentle, flowing conversation 
  to check in on their mental and emotional well-being. 
  Ask each of the following 16 questions in order, 
  one at a time, to determine how often 
  they've experienced the described problem over the past 2 weeks.

  For each question, prompt for a single numerical response 
  (0, 1, 2, or 3) using these options:

0 = Not at all
1 = Several days
2 = More than half the days
3 = Nearly every day

Maintain an empathetic tone throughout, 
rephrasing questions naturally within the conversation. 
Do not skip any questions.

**The 16 questions to ask, in order, are:**

1. Little interest or pleasure in doing things.
2. Feeling down, depressed, or hopeless.
3. Trouble falling or staying asleep, or sleeping too much.
4. Feeling tired or having little energy.
5. Poor appetite or overeating.
6. Feeling bad about yourself, 
or that you are a failure or have let yourself or your family down.
7. Trouble concentrating on things, 
such as reading the newspaper or watching television.
8. Moving or speaking so slowly that other people could have noticed. 
Or the opposite, being so fidgety or restless that you have been moving 
around a lot more than usual.
9. Thoughts that you would be better off dead, or of hurting yourself in some way.
10. Feeling nervous, anxious, or on edge.
11. Not being able to stop or control worrying.
12. Worrying too much about different things.
13. Trouble relaxing.
14. Being so restless that it's hard to sit still.
15. Becoming easily annoyed or irritable.
16. Feeling afraid as if something awful might happen.

Once you have a value numerical answer for the question you just asked,
store it in an array of answers, against the question number.
When question has been anwred with a valid answer, respond with the current array of answers, before asking the next quetion.`;

  // Function to call Firebase Functions
  const callAIFunction = httpsCallable(functions, 'processMessage');

  // Scroll to bottom of chat when messages update
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Initialize chat session
  useEffect(() => {
    if (!currentUser) return;

    const initializeChat = async () => {
      // Create a new session or load existing one
      const userSessionsRef = ref(database, `chatSessions/${currentUser.uid}`);
      
      // Check for existing incomplete session
      onValue(userSessionsRef, (snapshot) => {
        const sessions = snapshot.val();
        
        if (sessions) {
          // Find the most recent incomplete session
          const sessionEntries = Object.entries(sessions);
          const incompleteSession = sessionEntries.find(
            ([_, session]: [string, any]) => !session.completed
          );
          
          if (incompleteSession) {
            const [id, session] = incompleteSession as [string, ChatSession];
            setSessionId(id);
            setMessages(session.messages || []);
            return;
          }
        }
        
        // Create new session if no incomplete session exists
        createNewSession();
      }, { onlyOnce: true });
    };

    const createNewSession = async () => {
      const newSessionRef = push(ref(database, `chatSessions/${currentUser.uid}`));
      const newSessionId = newSessionRef.key;
      
      if (newSessionId) {
        setSessionId(newSessionId);
        
        // Initialize with system message
        const initialMessage: Message = {
          id: Date.now().toString(),
          text: "Hi there! I'm here to ask you some questions about how you've been feeling lately. This will help us understand your mental wellbeing better. Let's start with a few questions about your mood and energy levels over the past two weeks. Ready to begin?",
          sender: 'ai',
          timestamp: Date.now()
        };
        
        await set(newSessionRef, {
          messages: [initialMessage],
          completed: false,
          startedAt: Date.now()
        });
        
        setMessages([initialMessage]);
        
        // Send initial prompt to AI (hidden from user)
        processAIResponse(initialPrompt, newSessionId, true);
      }
    };

    initializeChat();
  }, [currentUser]);

  // Process user message and get AI response
  const processAIResponse = async (userMessage: string, chatSessionId: string, isSystemPrompt = false) => {
    if (!currentUser || !chatSessionId) return;
    
    setIsLoading(true);
    
    try {
      // If not a system prompt, save user message to database
      if (!isSystemPrompt) {
        const userMessageObj: Message = {
          id: Date.now().toString(),
          text: userMessage,
          sender: 'user',
          timestamp: Date.now()
        };
        
        // Update local state
        setMessages(prev => [...prev, userMessageObj]);
        
        // Save to Firebase
        const messagesRef = ref(database, `chatSessions/${currentUser.uid}/${chatSessionId}/messages`);
        const newMessageRef = push(messagesRef);
        await set(newMessageRef, userMessageObj);
      }
      
      // Call Firebase Function with context
      const response = await callAIFunction({
        message: userMessage,
        sessionId: chatSessionId,
        isSystemPrompt
      });
      
      // Extract response data
      const data = response.data as any;
      
      if (data.message) {
        const aiMessageObj: Message = {
          id: Date.now().toString(),
          text: data.message,
          sender: 'ai',
          timestamp: Date.now()
        };
        
        // Update local state (don't add system prompts to visible messages)
        if (!isSystemPrompt) {
          setMessages(prev => [...prev, aiMessageObj]);
        }
        
        // Save to Firebase
        const messagesRef = ref(database, `chatSessions/${currentUser.uid}/${chatSessionId}/messages`);
        const newMessageRef = push(messagesRef);
        await set(newMessageRef, aiMessageObj);
        
        // Check if assessment is complete
        if (data.scores) {
          setAssessmentComplete(true);
          
          // Save scores to Firebase
          await set(
            ref(database, `chatSessions/${currentUser.uid}/${chatSessionId}/scores`),
            data.scores
          );
          
          // Mark session as completed
          await set(
            ref(database, `chatSessions/${currentUser.uid}/${chatSessionId}/completed`),
            true
          );
        }
      }
    } catch (error) {
      console.error('Error processing message:', error);
      // Handle error - maybe add an error message to the chat
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sending a new message
  const handleSendMessage = (message: string) => {
    if (sessionId) {
      processAIResponse(message, sessionId);
    }
  };

  // Start a new assessment
  const handleStartNewAssessment = () => {
    setAssessmentComplete(false);
    setMessages([]);
    setSessionId(null);
    
    // This will trigger the useEffect to create a new session
    if (currentUser) {
      const createNewSession = async () => {
        const newSessionRef = push(ref(database, `chatSessions/${currentUser.uid}`));
        const newSessionId = newSessionRef.key;
        
        if (newSessionId) {
          setSessionId(newSessionId);
          
          // Initialize with system message
          const initialMessage: Message = {
            id: Date.now().toString(),
            text: "Hi there! I'm here to ask you some questions about how you've been feeling lately. This will help us understand your mental wellbeing better. Let's start with a few questions about your mood and energy levels over the past two weeks. Ready to begin?",
            sender: 'ai',
            timestamp: Date.now()
          };
          
          await set(newSessionRef, {
            messages: [initialMessage],
            completed: false,
            startedAt: Date.now()
          });
          
          setMessages([initialMessage]);
          
          // Send initial prompt to AI (hidden from user)
          processAIResponse(initialPrompt, newSessionId, true);
        }
      };
      
      createNewSession();
    }
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        {assessmentComplete && (
          <button 
            onClick={handleStartNewAssessment}
            className="new-assessment-button"
          >
            Start New Assessment
          </button>
        )}
      </div>
      
      <div className="chat-messages">
        {messages.length === 0 && (
          <div className="no-messages">
            <p>Start the assessment by clicking "Begin" below.</p>
          </div>
        )}
        {messages.length > 0 ? messages.map((msg) => (
          <div 
            key={msg.id} 
            className={`message ${msg.sender === 'user' ? 'user-message' : 'ai-message'}`}
          >
            <div className="message-bubble">
              <Markdown>{msg.text}</Markdown>
            </div>
            <div className="message-time">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )) : null}
        <div ref={messagesEndRef} />
      </div>
      
      <ChatForm 
        onSendMessage={handleSendMessage} 
        isLoading={isLoading} 
      />
      
      {assessmentComplete && (
        <div className="assessment-complete">
          <h3>Assessment Complete</h3>
          <p>Thank you for completing the assessment. Your responses have been recorded.</p>
        </div>
      )}
    </div>
  );
};

export default Chat;

