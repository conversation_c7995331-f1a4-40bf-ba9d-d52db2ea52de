import { useEffect } from 'react';
import CookieConsent from 'vanilla-cookieconsent';
import '../styles/cookieConsent.css';

const CookieConsentComponent = () => {
  useEffect(() => {
    // Debug: Check if consent already exists
    console.log('Cookie consent initialization...');
    console.log('Existing consent:', CookieConsent.getCookie());

    // For testing: uncomment the next line to reset consent
    localStorage.removeItem('cc_cookie');

    CookieConsent.run({
      guiOptions: {
        consentModal: {
          layout: 'box',
          position: 'bottom right',
          equalWeightButtons: false,
          flipButtons: false
        },
        preferencesModal: {
          layout: 'box',
          position: 'right',
          equalWeightButtons: false,
          flipButtons: false
        }
      },
      categories: {
        necessary: {
          readOnly: true
        },
        analytics: {},
        functionality: {}
      },
      language: {
        default: 'en',
        autoDetect: 'browser',
        translations: {
          en: {
            consentModal: {
              title: 'We use cookies!',
              description: 'Hi, this website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only after consent.',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Reject all',
              showPreferencesBtn: 'Manage preferences',
              footer: `
                <a href="/privacy">Privacy Policy</a>
                <a href="/terms">Terms & Conditions</a>
              `
            },
            preferencesModal: {
              title: 'Consent Preferences Center',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Reject all',
              savePreferencesBtn: 'Save preferences',
              closeIconLabel: 'Close modal',
              serviceCounterLabel: 'Service|Services',
              sections: [
                {
                  title: 'Cookie Usage',
                  description: 'We use cookies to ensure the basic functionalities of the website and to enhance your online experience. You can choose for each category to opt-in/out whenever you want. For more details relative to cookies and other sensitive data, please read the full <a href="/privacy" class="cc-link">privacy policy</a>.'
                },
                {
                  title: 'Strictly Necessary Cookies <span class="pm__badge">Always Enabled</span>',
                  description: 'These cookies are essential for the proper functioning of our website. Without these cookies, the website would not work properly.',
                  linkedCategory: 'necessary'
                },
                {
                  title: 'Analytics Cookies',
                  description: 'These cookies allow the website to remember the choices you have made in the past and provide enhanced, more personal features.',
                  linkedCategory: 'analytics'
                },
                {
                  title: 'Functionality Cookies',
                  description: 'These cookies collect information about how you use the website, which pages you visited and which links you clicked on. All of the data is anonymized and cannot be used to identify you.',
                  linkedCategory: 'functionality'
                },
                {
                  title: 'More information',
                  description: 'For any queries in relation to our policy on cookies and your choices, please <a class="cc-link" href="mailto:<EMAIL>">contact us</a>.'
                }
              ]
            }
          }
        }
      },
      onFirstConsent: ({ cookie }) => {
        console.log('First consent given:', cookie);
      },
      onConsent: ({ cookie }) => {
        console.log('Consent updated:', cookie);

        // Enable/disable analytics based on consent
        if (cookie.categories.includes('analytics')) {
          // Enable Google Analytics or other analytics tools
          console.log('Analytics enabled');
        } else {
          // Disable analytics
          console.log('Analytics disabled');
        }
      },
      onChange: ({ cookie, changedCategories }) => {
        console.log('Consent changed:', cookie, changedCategories);

        // Handle analytics toggle
        if (changedCategories.includes('analytics')) {
          if (cookie.categories.includes('analytics')) {
            console.log('Analytics enabled');
          } else {
            console.log('Analytics disabled');
          }
        }
      }
    });

    // Debug: Check what elements were created
    setTimeout(() => {
      console.log('Checking for cookie consent elements...');
      const ccMain = document.getElementById('cc-main');
      const ccDiv = document.querySelector('.cc_div');
      const ccWindow = document.querySelector('.cc-window');
      const ccBanner = document.querySelector('.cc-banner');

      console.log('Elements found:', {
        ccMain: ccMain ? 'YES' : 'NO',
        ccDiv: ccDiv ? 'YES' : 'NO',
        ccWindow: ccWindow ? 'YES' : 'NO',
        ccBanner: ccBanner ? 'YES' : 'NO'
      });

      if (ccMain) {
        console.log('cc-main styles:', {
          display: getComputedStyle(ccMain).display,
          visibility: getComputedStyle(ccMain).visibility,
          opacity: getComputedStyle(ccMain).opacity,
          zIndex: getComputedStyle(ccMain).zIndex,
          position: getComputedStyle(ccMain).position,
          bottom: getComputedStyle(ccMain).bottom,
          right: getComputedStyle(ccMain).right
        });
        console.log('cc-main innerHTML:', ccMain.innerHTML);
      }

      // Create a test element to verify our CSS is working
      const testDiv = document.createElement('div');
      testDiv.id = 'cc-test';
      testDiv.innerHTML = `
        <div class="cm">
          <div class="cm__header">
            <h2 class="cm__title">Test Cookie Banner</h2>
            <p class="cm__desc">This is a test to see if our CSS is working</p>
          </div>
          <div class="cm__footer">
            <button class="cm__btn cm__btn--primary">Accept</button>
            <button class="cm__btn cm__btn--secondary">Reject</button>
          </div>
        </div>
      `;
      testDiv.style.cssText = `
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #ccc !important;
        border-radius: 8px !important;
        padding: 20px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        max-width: 400px !important;
      `;
      document.body.appendChild(testDiv);

      // Remove test div after 5 seconds
      setTimeout(() => {
        testDiv.remove();
      }, 5000);
    }, 500);

    // Expose CookieConsent functions globally for footer button
    // @ts-ignore
    window.CC = {
      run: CookieConsent.run,
      getCookie: CookieConsent.getCookie,
      showPreferences: CookieConsent.showPreferences
    };

    // The CSS is imported via the external CSS file
    // Just add a small delay to ensure proper initialization
    const timeoutId = setTimeout(() => {
      // Force a style recalculation to ensure our CSS takes precedence
      const ccMain = document.getElementById('cc-main');
      if (ccMain) {
        ccMain.style.display = 'none';
        ccMain.offsetHeight; // Trigger reflow
        ccMain.style.display = '';
      }
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return null;
};

export default CookieConsentComponent;
