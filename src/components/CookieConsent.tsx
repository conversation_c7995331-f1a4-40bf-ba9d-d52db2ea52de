import { useEffect } from 'react';
import { run, getCookie, showPreferences } from 'vanilla-cookieconsent';
import '../styles/cookieConsent.css';

const CookieConsentComponent = () => {
  useEffect(() => {
    run({
      guiOptions: {
        consentModal: {
          layout: 'box',
          position: 'bottom right',
          equalWeightButtons: false,
          flipButtons: false
        },
        preferencesModal: {
          layout: 'box',
          position: 'right',
          equalWeightButtons: false,
          flipButtons: false
        }
      },
      categories: {
        necessary: {
          readOnly: true
        },
        analytics: {},
        functionality: {}
      },
      language: {
        default: 'en',
        autoDetect: 'browser',
        translations: {
          en: {
            consentModal: {
              title: 'We use cookies!',
              description: 'Hi, this website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only after consent.',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Reject all',
              showPreferencesBtn: 'Manage preferences',
              footer: `
                <a href="/privacy">Privacy Policy</a>
                <a href="/terms">Terms & Conditions</a>
              `
            },
            preferencesModal: {
              title: 'Consent Preferences Center',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Reject all',
              savePreferencesBtn: 'Save preferences',
              closeIconLabel: 'Close modal',
              serviceCounterLabel: 'Service|Services',
              sections: [
                {
                  title: 'Cookie Usage',
                  description: 'We use cookies to ensure the basic functionalities of the website and to enhance your online experience. You can choose for each category to opt-in/out whenever you want. For more details relative to cookies and other sensitive data, please read the full <a href="/privacy" class="cc-link">privacy policy</a>.'
                },
                {
                  title: 'Strictly Necessary Cookies <span class="pm__badge">Always Enabled</span>',
                  description: 'These cookies are essential for the proper functioning of our website. Without these cookies, the website would not work properly.',
                  linkedCategory: 'necessary'
                },
                {
                  title: 'Analytics Cookies',
                  description: 'These cookies allow the website to remember the choices you have made in the past and provide enhanced, more personal features.',
                  linkedCategory: 'analytics'
                },
                {
                  title: 'Functionality Cookies',
                  description: 'These cookies collect information about how you use the website, which pages you visited and which links you clicked on. All of the data is anonymized and cannot be used to identify you.',
                  linkedCategory: 'functionality'
                },
                {
                  title: 'More information',
                  description: 'For any queries in relation to our policy on cookies and your choices, please <a class="cc-link" href="mailto:<EMAIL>">contact us</a>.'
                }
              ]
            }
          }
        }
      },
      onFirstConsent: ({ cookie }) => {
        console.log('First consent given:', cookie);
      },
      onConsent: ({ cookie }) => {
        console.log('Consent updated:', cookie);

        // Enable/disable analytics based on consent
        if (cookie.categories.includes('analytics')) {
          // Enable Google Analytics or other analytics tools
          console.log('Analytics enabled');
        } else {
          // Disable analytics
          console.log('Analytics disabled');
        }
      },
      onChange: ({ cookie, changedCategories }) => {
        console.log('Consent changed:', cookie, changedCategories);

        // Handle analytics toggle
        if (changedCategories.includes('analytics')) {
          if (cookie.categories.includes('analytics')) {
            console.log('Analytics enabled');
          } else {
            console.log('Analytics disabled');
          }
        }
      }
    });

    // Expose CookieConsent functions globally for footer button
    // @ts-ignore
    window.CC = { run, getCookie, showPreferences };

    // Add custom CSS with correct class names for vanilla-cookieconsent
    const style = document.createElement('style');
    style.textContent = `
      /* Cookie consent custom styles - using correct class names */
      #cc-main {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
        z-index: 9999 !important;
      }

      /* Consent Modal Styles */
      #cc-main .cm {
        background: #ffffff !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        max-width: 400px !important;
      }

      #cc-main .cm__header {
        border-bottom: 1px solid #e5e7eb !important;
        padding: 1.5rem !important;
      }

      #cc-main .cm__title {
        color: #111827 !important;
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
      }

      #cc-main .cm__desc {
        color: #6b7280 !important;
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
        margin: 0.75rem 0 0 0 !important;
      }

      #cc-main .cm__footer {
        padding: 1.5rem !important;
        border-top: 1px solid #e5e7eb !important;
        display: flex !important;
        gap: 0.75rem !important;
        justify-content: flex-end !important;
        flex-wrap: wrap !important;
      }

      #cc-main .cm__btn {
        border-radius: 0.375rem !important;
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        padding: 0.5rem 1rem !important;
        border: none !important;
        cursor: pointer !important;
        transition: all 0.2s !important;
        text-decoration: none !important;
      }

      #cc-main .cm__btn--primary {
        background-color: #0d9488 !important;
        color: white !important;
      }

      #cc-main .cm__btn--primary:hover {
        background-color: #0f766e !important;
      }

      #cc-main .cm__btn--secondary {
        background-color: #f3f4f6 !important;
        color: #374151 !important;
        border: 1px solid #d1d5db !important;
      }

      #cc-main .cm__btn--secondary:hover {
        background-color: #e5e7eb !important;
      }

      /* Preferences Modal Styles */
      #cc-main .pm {
        background: #ffffff !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
        max-width: 600px !important;
        max-height: 80vh !important;
      }

      #cc-main .pm__header {
        background-color: #f9fafb !important;
        border-bottom: 1px solid #e5e7eb !important;
        padding: 1.5rem !important;
        border-radius: 0.5rem 0.5rem 0 0 !important;
      }

      #cc-main .pm__title {
        color: #111827 !important;
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
      }

      #cc-main .pm__body {
        padding: 1.5rem !important;
        max-height: 400px !important;
        overflow-y: auto !important;
      }

      #cc-main .pm__section {
        margin-bottom: 1.5rem !important;
      }

      #cc-main .pm__section-title {
        color: #111827 !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        margin: 0 0 0.5rem 0 !important;
      }

      #cc-main .pm__section-desc {
        color: #6b7280 !important;
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
        margin: 0 !important;
      }

      #cc-main .pm__footer {
        background-color: #f9fafb !important;
        border-top: 1px solid #e5e7eb !important;
        padding: 1.5rem !important;
        border-radius: 0 0 0.5rem 0.5rem !important;
        display: flex !important;
        gap: 0.75rem !important;
        justify-content: flex-end !important;
        flex-wrap: wrap !important;
      }

      /* Links */
      #cc-main .cc-link,
      #cc-main a {
        color: #0d9488 !important;
        text-decoration: none !important;
      }

      #cc-main .cc-link:hover,
      #cc-main a:hover {
        color: #0f766e !important;
        text-decoration: underline !important;
      }

      /* Badge */
      #cc-main .pm__badge {
        background-color: #10b981 !important;
        color: white !important;
        font-size: 0.75rem !important;
        font-weight: 500 !important;
        padding: 0.25rem 0.5rem !important;
        border-radius: 0.25rem !important;
        margin-left: 0.5rem !important;
      }

      /* Toggle switch styles */
      #cc-main .section__toggle {
        position: relative !important;
        display: inline-block !important;
        width: 44px !important;
        height: 24px !important;
      }

      #cc-main .section__toggle input {
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
      }

      #cc-main .toggle__slider {
        position: absolute !important;
        cursor: pointer !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background-color: #d1d5db !important;
        transition: 0.2s !important;
        border-radius: 24px !important;
      }

      #cc-main .toggle__slider:before {
        position: absolute !important;
        content: "" !important;
        height: 18px !important;
        width: 18px !important;
        left: 3px !important;
        bottom: 3px !important;
        background-color: white !important;
        transition: 0.2s !important;
        border-radius: 50% !important;
      }

      #cc-main input:checked + .toggle__slider {
        background-color: #0d9488 !important;
      }

      #cc-main input:checked + .toggle__slider:before {
        transform: translateX(20px) !important;
      }

      /* Overlay */
      #cc-main .cc__overlay {
        background-color: rgba(0, 0, 0, 0.5) !important;
      }

      /* Close button */
      #cc-main .pm__close-btn {
        background: none !important;
        border: none !important;
        font-size: 1.5rem !important;
        color: #6b7280 !important;
        cursor: pointer !important;
        padding: 0.5rem !important;
      }

      #cc-main .pm__close-btn:hover {
        color: #374151 !important;
      }
    `;
    document.head.appendChild(style);

    // Add a small delay to ensure styles are applied after the library renders
    setTimeout(() => {
      const existingStyle = document.getElementById('cc-custom-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
      style.id = 'cc-custom-styles';
      document.head.appendChild(style);
    }, 100);

    // Use MutationObserver to apply styles when cookie consent elements are added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.id === 'cc-main' || element.classList.contains('cc_div')) {
              // Reapply styles when cookie consent elements are detected
              const customStyle = document.getElementById('cc-custom-styles');
              if (customStyle) {
                customStyle.remove();
                document.head.appendChild(customStyle);
              }
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => {
      const customStyle = document.getElementById('cc-custom-styles');
      if (customStyle) {
        document.head.removeChild(customStyle);
      }
      observer.disconnect();
    };
  }, []);

  return null;
};

export default CookieConsentComponent;
