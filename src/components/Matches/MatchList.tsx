import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import { db } from '../Firebase';
import MatchCard from './MatchCard';

interface Match {
  id: string;
  email: string;
  therapistId: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

const MatchList = () => {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currentUser } = useAuth();

  useEffect(() => {
    if (!currentUser?.uid) return;

    const matchesQuery = query(
      collection(db, 'matches'),
      where('therapistId', '==', currentUser.uid)
    );

    const unsubscribe = onSnapshot(matchesQuery, 
      (snapshot) => {
        const matchesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate()
        })) as Match[];
        
        setMatches(matchesData);
        setLoading(false);
      },
      (error) => {
        console.error('Error fetching matches:', error);
        setError('Failed to load matches');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [currentUser]);

  if (loading) return <div className="text-center p-4">Loading matches...</div>;
  if (error) return <div className="text-red-500 p-4">{error}</div>;

  const pendingMatches = matches.filter(match => match.status === 'pending');
  const acceptedMatches = matches.filter(match => match.status === 'accepted');

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h2 className="text-2xl font-bold mb-6">Your Match Requests</h2>
      
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">Pending Matches ({pendingMatches.length})</h3>
        <div className="space-y-4">
          {pendingMatches.map(match => (
            <MatchCard key={match.id} match={match} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-4">Accepted Matches ({acceptedMatches.length})</h3>
        <div className="space-y-4">
          {acceptedMatches.map(match => (
            <MatchCard key={match.id} match={match} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MatchList;