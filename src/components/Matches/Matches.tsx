import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import MatchList from './MatchList.tsx';

const Matches = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <MatchList />
        </div>
      </div>
    </div>
  );
};

export default Matches;
