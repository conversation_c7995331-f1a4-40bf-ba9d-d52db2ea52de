import { useState } from 'react';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../Firebase';

interface MatchProps {
  match: {
    id: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

const MatchCard = ({ match }: MatchProps) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAccept = async () => {
    setIsUpdating(true);
    setError(null);

    try {
      const matchRef = doc(db, 'matches', match.id);
      await updateDoc(matchRef, {
        status: 'accepted',
        updatedAt: new Date()
      });
    } catch (err) {
      console.error('Error accepting match:', err);
      setError('Failed to accept match');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
      <div className="flex justify-between items-center">
        <div>
          <p className="text-lg font-medium">Match Request ({match.id})</p>
          <p className="text-md text-gray-900">
            Requested: {match.createdAt.toLocaleDateString()}
          </p>
          <p className="text-sm font-medium mt-1 capitalize">
            Status: <span className={`${match.status === 'pending' ? 'text-yellow-600' : 'text-green-600'}`}>
              {match.status}
            </span>
          </p>
        </div>
        
        {match.status === 'pending' && (
          <button
            onClick={handleAccept}
            disabled={isUpdating}
            className="px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isUpdating ? 'Accepting...' : 'Accept Match'}
          </button>
        )}
      </div>
      
      {error && (
        <p className="text-red-500 text-sm mt-2">{error}</p>
      )}
    </div>
  );
};

export default MatchCard;
