import React, { useState } from 'react';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '../Firebase'; // Adjust path as needed

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    setError('');
    
    try {
      await sendPasswordResetEmail(auth, email);
      setMessage('Password reset email sent! Check your inbox.');
      setEmail('');
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-screen w-screen p-4 overflow-y-scroll">
    <div className="bg-white max-w-[800px] w-full m-auto rounded-md shadow-xl p-8 mt-4">
        <div className="col-span-8">
          <h1 className="text-4xl font-bold text-gray-700 pt-6 leading-10">
            Reset Password
          </h1>
        </div>
        <div className="grid bg-gray-200 p-8 rounded-lg mt-2">
          <form onSubmit={handleSubmit}>
            <div className="w-full text-lg m-1">
              <label htmlFor="email">Your Email Address</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className='w-full mt-2 p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-m rounded-lg mb-4 focus:outline-none focus:ring focus:border-red-500 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500'
              />
            </div>
            <button className="ml-2 mt-2 p-6 w-full border-2 border-gray-600 rounded-lg text-gray-800 bg-teal-200 text-xl shadow-xl border-solid p-4 font-medium text-gray-700 hover:text-black-500 hover:bg-teal-300"type="submit" disabled={loading}>
              {loading ? 'Sending...' : 'Send Reset Email'}
            </button>
            <br/>
            {message && <div className="success-message">{message}</div>}
            {error && <div className="error-message">{error}</div>}
          </form>
        </div>
    </div>
</div>
  );
};

export default ForgotPassword;