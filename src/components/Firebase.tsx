// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getFirestore, 
  // connectFirestoreEmulator,
  // setLogLevel
 } from "firebase/firestore";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { connectDatabaseEmulator, getDatabase } from "firebase/database";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";
let useEmulator = import.meta.env.USE_EMULATOR || true; // default to false

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries


// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAkLbnUC7I_kBUETtENh6Sgkc4nlr5IbLE",
  authDomain: "mood-rings-beta.firebaseapp.com",
  projectId: "mood-rings-beta",
  storageBucket: "mood-rings-beta.firebasestorage.app",
  messagingSenderId: "63584321569",
  appId: "1:63584321569:web:56e6d98e0cc7fb68322360",
  measurementId: "G-DY94X6B3ZF",
  databaseURL: "https://mood-rings-beta-default-rtdb.europe-west1.firebasedatabase.app/"
};

// Initialize Firebase
export const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);

export const analytics = getAnalytics(app);
export const db = getFirestore(app);
export const database = getDatabase(app);
// export const functions = getFunctions(app, "127.0.0.1:5001");
export const functions = getFunctions(app);
if (location.hostname === "localhost" && useEmulator) {
  // connectFirestoreEmulator(db, '127.0.0.1', 8080);
  connectFunctionsEmulator(functions, "127.0.0.1", 5001);
  connectDatabaseEmulator(database, "127.0.0.1", 9000);
}
