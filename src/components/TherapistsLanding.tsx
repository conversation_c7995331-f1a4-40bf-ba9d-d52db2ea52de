import { analytics } from "./Firebase";
import { logEvent } from "firebase/analytics";

const TherapistsLanding = () => {

  logEvent(analytics, 'landing_viewed', {
    content_type: window.location.pathname === "/" ? 'landing' : "landing",
  });

  return (

    <div>
      <div className="container mx-auto md:h-screen mb-8">
      <div className="text-center px-3 lg:px-0">
        <h1
          className="my-2 text-2xl md:text-3xl lg:text-5xl font-bold text-gray-700 leading-tight capitalize "
        >
          Match with the right clients
        </h1>
        <p
          className="leading-normal text-gray-800 text-base md:text-xl lg:text-2xl mt-8 mb-4"
        >
          Our users work with Neuro and then we match them with the right Therapist
        </p>

        {/* <a href="/join">
          <button
            className="mx-auto bg-teal-700 lg:mx-0 hover:underline text-white no-underline font-extrabold rounded my-2 md:my-2 py-4 px-8 shadow-lg"
          >
            Join The Free Beta
          </button>
        </a> */}
      </div>

      <div className="flex items-center w-full mx-auto content-end overscroll-none">
        <div
          className="browser-mockup flex flex-1 m-6 md:px-0 md:m-12 bg-white w-1/2 rounded shadow-xl mb-8 overscroll-none"
        >
        <img src="/dashboard.png"  className="object-cover border-2 border-gray-100 border-solid rounded mr-4" alt="radar" />
        {/* <!-- <iframe src="http://localhost:5173/demo" width="100%" className="overscroll-none"></iframe> --> */}
        </div>
      </div>
    </div>



    <div id="therapists" className=" bg-white pt-4 md:mt-80">
      <div className="container max-w-5xl mx-auto m-8">
        <h2
          className="w-full my-2 text-5xl font-black leading-tight text-center text-gray-800"
        >
          For Therapists
        </h2>
        <div className="w-full mb-4">
          <div
            className="h-1 mx-auto gradient w-64 opacity-25 my-0 py-0 rounded-t"
          ></div>
        </div>


        <div className="flex flex-row">
          <div className="w-5/6 sm:w-1/2 p-6 text-xl">
            <h3 className="text-4xl text-gray-800 font-bold leading-none mb-4">
              Get warm introductions to new clients in need of your skills!
            </h3>
            <p className="text-gray-600 mt-8">
              ✅ Our users have already begun a self-work journey
            </p>
            <p className="text-gray-600 mt-4">
             ✅ They will have gained deeper personal insights.
            </p>
            <p className="text-gray-600 mt-4">
             ✅ And we'll have matched their needs to your skillset.
            </p>
          </div>
          <div className="w-full sm:w-1/2 p-6">
            <div className=" w-full flex flex-1 m-0 md:px-0 bg-white w-1/2 rounded shadow-xl mb-8 overscroll-none">
              <div className="md:mr-8">
                <img src="/inquiry.png" width="480px" height="480px" className="border-2 border-gray-100 p-2 border-solid rounded" alt="inquiry" />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap flex-col-reverse sm:flex-row mb-8">
          <div className="w-full sm:w-1/2 p-6 mt-6">
            <div className="md:mr-8">
              <img src="/clients.png" width="480px" height="480px" className="border-2 border-gray-100 p-2 border-solid rounded" alt="clients" />
            </div>
          </div>
          <div className="w-full sm:w-1/2 p-6 mt-6">
            <div className="align-middle text-xl mb-8">
              <h3 className="text-4xl text-gray-800 font-bold leading-none mb-3">
                Onboard existing clients for free.
              </h3>
              <p className="text-gray-600 mt-8">
                📈 Manage and monitor your <br/> clients progress between sessions.
              </p>
            </div>
            <div className="align-middle text-xl">
              <h3 className="text-4xl text-gray-800 font-bold leading-none mb-3">
                Track your progress as a therapist
              </h3>
              <p className="text-gray-600 mt-8">
                ⚠️ See data and issues
                across your entire clientelle,
                ahead of your next session.
              </p>
            </div>
          </div>
        </div>
      </div>


      {/* <div id="join" className="gradient w-full mx-auto text-center pt-6 pb-12">
        <h2
          className="w-full my-2 text-5xl font-black leading-tight text-center text-gray-700"
        >
          Join The Free Beta
        </h2>
        <div className="w-full mb-4">
          <div
            className="h-1 mx-auto bg-white w-1/6 opacity-25 my-0 py-0 rounded-t"
          ></div>
        </div>
      </div> */}
      </div>
    </div>
  );
};

export default TherapistsLanding;