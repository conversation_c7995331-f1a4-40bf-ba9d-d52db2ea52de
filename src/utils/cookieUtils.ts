/**
 * Utility functions for cookie consent management
 */

import { getCookie, showPreferences } from 'vanilla-cookieconsent';

/**
 * Check if a specific cookie category has been accepted
 * @param category - The cookie category to check ('necessary', 'analytics', 'functionality')
 * @returns boolean indicating if the category is accepted
 */
export const isCookieCategoryAccepted = (category: string): boolean => {
  try {
    const cookie = getCookie();
    return cookie && cookie.categories && cookie.categories.includes(category);
  } catch (error) {
    console.warn('Error checking cookie consent:', error);
    // Fallback to window object if direct import doesn't work
    try {
      // @ts-ignore
      if (window.CC && window.CC.getCookie) {
        // @ts-ignore
        const cookie = window.CC.getCookie();
        return cookie && cookie.categories && cookie.categories.includes(category);
      }
    } catch (fallbackError) {
      console.warn('Fallback cookie check also failed:', fallbackError);
    }
  }
  return false;
};

/**
 * Check if analytics cookies are accepted
 * @returns boolean indicating if analytics cookies are accepted
 */
export const isAnalyticsAccepted = (): boolean => {
  return isCookieCategoryAccepted('analytics');
};

/**
 * Check if functionality cookies are accepted
 * @returns boolean indicating if functionality cookies are accepted
 */
export const isFunctionalityAccepted = (): boolean => {
  return isCookieCategoryAccepted('functionality');
};

/**
 * Get all accepted cookie categories
 * @returns array of accepted category names
 */
export const getAcceptedCategories = (): string[] => {
  try {
    const cookie = getCookie();
    return cookie && cookie.categories ? cookie.categories : [];
  } catch (error) {
    console.warn('Error getting accepted categories:', error);
    // Fallback to window object
    try {
      // @ts-ignore
      if (window.CC && window.CC.getCookie) {
        // @ts-ignore
        const cookie = window.CC.getCookie();
        return cookie && cookie.categories ? cookie.categories : [];
      }
    } catch (fallbackError) {
      console.warn('Fallback get categories also failed:', fallbackError);
    }
  }
  return [];
};

/**
 * Show the cookie preferences modal
 */
export const showCookiePreferences = (): void => {
  try {
    showPreferences();
  } catch (error) {
    console.warn('Error showing cookie preferences:', error);
    // Fallback to window object
    try {
      // @ts-ignore
      if (window.CC && window.CC.showPreferences) {
        // @ts-ignore
        window.CC.showPreferences();
      }
    } catch (fallbackError) {
      console.warn('Fallback show preferences also failed:', fallbackError);
    }
  }
};

/**
 * Accept all cookies programmatically
 */
export const acceptAllCookies = (): void => {
  try {
    // @ts-ignore
    if (window.CC && window.CC.acceptCategory) {
      // @ts-ignore
      window.CC.acceptCategory(['necessary', 'analytics', 'functionality']);
    }
  } catch (error) {
    console.warn('Error accepting all cookies:', error);
  }
};

/**
 * Reject all non-essential cookies programmatically
 */
export const rejectAllCookies = (): void => {
  try {
    // @ts-ignore
    if (window.CC && window.CC.acceptCategory) {
      // @ts-ignore
      window.CC.acceptCategory(['necessary']);
    }
  } catch (error) {
    console.warn('Error rejecting cookies:', error);
  }
};
