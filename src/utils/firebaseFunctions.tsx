import { getFunctions, httpsCallable, HttpsCallableResult, connectFunctionsEmulator } from 'firebase/functions';
// import { getAuth, signInAnonymously, Auth } from 'firebase/auth';
import { app } from '../components/Firebase'; // adjust this import path as needed
let useEmulator = import.meta.env.USE_EMULATOR || true; // default to false

interface FirebaseCallOptions {
  requireAuth?: boolean;
  region?: string;
}

interface FirebaseCallError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Generic function to call Firebase Functions endpoints
 * @param functionName - The name of the Firebase function to call
 * @param data - The data to send to the function
 * @param options - Optional configuration for the function call
 * @returns Promise with the function result
 */
export async function callFirebaseFunction<T = any, R = any>(
  functionName: string,
  data: T,
  options: FirebaseCallOptions = {}
): Promise<R> {
  const {
    requireAuth = false,
    region = 'us-central1' // default region, adjust as needed
  } = options;

  try {
    // let auth: Auth | undefined;
    
    if (requireAuth) {
      // auth = getAuth(app);
      // await signInAnonymously(auth);
    }

    const functions = getFunctions(app, region);

    // Do this to check whether you're on an emulator or not. (Only if you're on the frontend side)
    if (useEmulator && (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1")) {
      console.log({useEmulator});  
      connectFunctionsEmulator(functions, "127.0.0.1", 5001);
    };

    const callable = httpsCallable<T, R>(functions, functionName);

    const result: HttpsCallableResult<R> = await callable(data);
    return result.data;

  } catch (error: any) {
    const firebaseError: FirebaseCallError = {
      code: error.code || 'unknown',
      message: error.message || 'An unknown error occurred',
      details: error.details
    };
    
    console.error('Firebase function call failed:', firebaseError);
    throw firebaseError;
  }
}

// Example type for mood assessment data
interface MoodAssessmentData {
  moodScore: number;
  concerns: string[];
  profile: any;
  preferences: any;
  note: string;
  timestamp?: number;
}

/**
 * Specific function to submit mood assessment data
 */
export async function submitMoodAssessment(
  data: MoodAssessmentData
): Promise<any> {
  return callFirebaseFunction<MoodAssessmentData, any>(
    'submitMoodAssessment', // replace with your actual function name
    {
      ...data,
      timestamp: Date.now()
    },
    { requireAuth: false }
  );
}

// Example type for match request data
interface MatchRequestData {
  therapistId: string;
  email: string;
}

/**
 * Specific function to handle match request
 */
export async function handleMatchRequest(
  therapistId: string,
  email: string
): Promise<any> {
  return callFirebaseFunction<MatchRequestData, any>(
    'matchRequest', // replace with your actual function name
    {
      therapistId,
      email
    },
    { requireAuth: false }
  );
}


