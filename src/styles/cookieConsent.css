/* <PERSON><PERSON> Custom Styles - Universal Approach */

/* Base container styling - covers all possible selectors */
#cc-main,
.cc_div,
.cc-window,
.cc-banner,
[data-cc="c-bar"],
[data-cc="c-modal"] {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  z-index: 9999 !important;
}

/* Consent Modal/Banner Styles */
#cc-main .cm,
.cc_div .cm,
.cc-window,
.cc-banner,
[data-cc="c-bar"],
[data-cc="c-modal"] {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  max-width: 450px !important;
  margin: 1rem !important;
  padding: 0 !important;
}

/* Header Styles */
#cc-main .cm__header,
.cc_div .cm__header,
.cc-header {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 1.5rem !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Title Styles */
#cc-main .cm__title,
.cc_div .cm__title,
.cc-header h1,
.cc-header h2,
.cc-title {
  color: #1e293b !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

/* Description Styles */
#cc-main .cm__desc,
.cc_div .cm__desc,
.cc-message,
.cc-compliance {
  color: #64748b !important;
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
  margin: 0.75rem 0 0 0 !important;
}

/* Footer/Button Container Styles */
#cc-main .cm__footer,
.cc_div .cm__footer,
.cc-compliance {
  background-color: #f8fafc !important;
  border-top: 1px solid #e2e8f0 !important;
  padding: 1.5rem !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
  display: flex !important;
  gap: 0.75rem !important;
  justify-content: flex-end !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}

/* Button Base Styles */
#cc-main .cm__btn,
.cc_div .cm__btn,
.cc-btn,
button[data-cc] {
  border-radius: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.625rem 1.25rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 40px !important;
  white-space: nowrap !important;
}

/* Primary Button (Accept All) */
#cc-main .cm__btn--primary,
.cc_div .cm__btn--primary,
.cc-btn.cc-allow,
button[data-cc="accept-all"] {
  background-color: #0d9488 !important;
  color: white !important;
  border: 2px solid #0d9488 !important;
}

#cc-main .cm__btn--primary:hover,
.cc_div .cm__btn--primary:hover,
.cc-btn.cc-allow:hover,
button[data-cc="accept-all"]:hover {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.3) !important;
}

/* Secondary Button (Reject All) */
#cc-main .cm__btn--secondary,
.cc_div .cm__btn--secondary,
.cc-btn.cc-deny,
button[data-cc="reject-all"] {
  background-color: #ffffff !important;
  color: #475569 !important;
  border: 2px solid #cbd5e1 !important;
}

#cc-main .cm__btn--secondary:hover,
.cc_div .cm__btn--secondary:hover,
.cc-btn.cc-deny:hover,
button[data-cc="reject-all"]:hover {
  background-color: #f1f5f9 !important;
  border-color: #94a3b8 !important;
  transform: translateY(-1px) !important;
}

/* Manage Preferences Button */
button[data-cc="show-preferencesModal"],
.cc-btn.cc-settings {
  background-color: transparent !important;
  color: #0d9488 !important;
  border: 2px solid transparent !important;
  text-decoration: underline !important;
  padding: 0.5rem 0.75rem !important;
}

button[data-cc="show-preferencesModal"]:hover,
.cc-btn.cc-settings:hover {
  color: #0f766e !important;
  background-color: rgba(13, 148, 136, 0.05) !important;
}

/* Preferences Modal Styles */
#cc-main .pm,
.cc_div .pm,
[data-cc="p-modal"] {
  background: #ffffff !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  max-width: 650px !important;
  max-height: 85vh !important;
  margin: 2rem auto !important;
  overflow: hidden !important;
}

#cc-main .pm__header,
.cc_div .pm__header,
[data-cc="p-modal"] .pm__header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #cbd5e1 !important;
  padding: 2rem !important;
  position: relative !important;
}

#cc-main .pm__title,
.cc_div .pm__title,
[data-cc="p-modal"] .pm__title {
  color: #1e293b !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin: 0 !important;
  line-height: 1.3 !important;
}

#cc-main .pm__body,
.cc_div .pm__body,
[data-cc="p-modal"] .pm__body {
  padding: 2rem !important;
  max-height: 50vh !important;
  overflow-y: auto !important;
  background-color: #ffffff !important;
}

#cc-main .pm__section,
.cc_div .pm__section,
[data-cc="p-modal"] .pm__section {
  margin-bottom: 2rem !important;
  padding: 1.5rem !important;
  background-color: #f8fafc !important;
  border-radius: 0.5rem !important;
  border: 1px solid #e2e8f0 !important;
}

#cc-main .pm__section:last-child,
.cc_div .pm__section:last-child {
  margin-bottom: 0 !important;
}

#cc-main .pm__section-title,
.cc_div .pm__section-title,
[data-cc="p-modal"] .pm__section-title {
  color: #1e293b !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0 0 0.75rem 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

#cc-main .pm__section-desc,
.cc_div .pm__section-desc,
[data-cc="p-modal"] .pm__section-desc {
  color: #64748b !important;
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
  margin: 0 !important;
}

#cc-main .pm__footer,
.cc_div .pm__footer,
[data-cc="p-modal"] .pm__footer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-top: 1px solid #cbd5e1 !important;
  padding: 2rem !important;
  display: flex !important;
  gap: 1rem !important;
  justify-content: flex-end !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}

/* Links */
#cc-main .cc-link,
#cc-main a,
.cc_div .cc-link,
.cc_div a {
  color: #0d9488 !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

#cc-main .cc-link:hover,
#cc-main a:hover,
.cc_div .cc-link:hover,
.cc_div a:hover {
  color: #0f766e !important;
  text-decoration: underline !important;
}

/* Badge */
#cc-main .pm__badge,
.cc_div .pm__badge {
  background-color: #0d9488 !important;
  color: white !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 1rem !important;
  margin-left: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

/* Toggle switch styles */
#cc-main .section__toggle,
.cc_div .section__toggle,
[data-cc] .section__toggle {
  position: relative !important;
  display: inline-block !important;
  width: 52px !important;
  height: 28px !important;
  margin-left: auto !important;
}

#cc-main .section__toggle input,
.cc_div .section__toggle input {
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

#cc-main .toggle__slider,
.cc_div .toggle__slider {
  position: absolute !important;
  cursor: pointer !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: #cbd5e1 !important;
  transition: all 0.3s ease !important;
  border-radius: 28px !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

#cc-main .toggle__slider:before,
.cc_div .toggle__slider:before {
  position: absolute !important;
  content: "" !important;
  height: 22px !important;
  width: 22px !important;
  left: 3px !important;
  bottom: 3px !important;
  background-color: white !important;
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

#cc-main input:checked + .toggle__slider,
.cc_div input:checked + .toggle__slider {
  background-color: #0d9488 !important;
}

#cc-main input:checked + .toggle__slider:before,
.cc_div input:checked + .toggle__slider:before {
  transform: translateX(24px) !important;
}

/* Overlay */
#cc-main .cc__overlay,
.cc_div .cc__overlay {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(4px) !important;
}

/* Close button */
#cc-main .pm__close-btn,
.cc_div .pm__close-btn {
  position: absolute !important;
  top: 1.5rem !important;
  right: 1.5rem !important;
  background: none !important;
  border: none !important;
  font-size: 1.5rem !important;
  color: #64748b !important;
  cursor: pointer !important;
  padding: 0.5rem !important;
  border-radius: 0.25rem !important;
  transition: all 0.2s !important;
}

#cc-main .pm__close-btn:hover,
.cc_div .pm__close-btn:hover {
  color: #1e293b !important;
  background-color: #f1f5f9 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  #cc-main .cm,
  .cc_div .cm,
  .cc-window,
  .cc-banner,
  [data-cc="c-bar"],
  [data-cc="c-modal"] {
    max-width: calc(100vw - 2rem) !important;
    margin: 1rem !important;
    border-radius: 0.5rem !important;
  }

  #cc-main .pm,
  .cc_div .pm,
  [data-cc="p-modal"] {
    max-width: calc(100vw - 1rem) !important;
    margin: 0.5rem auto !important;
    max-height: calc(100vh - 1rem) !important;
  }

  #cc-main .cm__footer,
  .cc_div .cm__footer,
  #cc-main .pm__footer,
  .cc_div .pm__footer {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  #cc-main .cm__btn,
  .cc_div .cm__btn,
  .cc-btn,
  button[data-cc] {
    width: 100% !important;
    justify-content: center !important;
  }

  #cc-main .pm__section-title,
  .cc_div .pm__section-title {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  #cc-main .section__toggle,
  .cc_div .section__toggle {
    margin-left: 0 !important;
  }
}

/* Focus states for accessibility */
#cc-main .cm__btn:focus,
.cc_div .cm__btn:focus,
.cc-btn:focus,
button[data-cc]:focus {
  outline: 2px solid #0d9488 !important;
  outline-offset: 2px !important;
}

#cc-main .section__toggle:focus-within,
.cc_div .section__toggle:focus-within {
  outline: 2px solid #0d9488 !important;
  outline-offset: 2px !important;
  border-radius: 28px !important;
}

/* Animation improvements */
#cc-main,
.cc_div {
  animation: ccFadeIn 0.3s ease-out !important;
}

@keyframes ccFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper stacking */
#cc-main,
.cc_div {
  position: fixed !important;
  z-index: 999999 !important;
}
