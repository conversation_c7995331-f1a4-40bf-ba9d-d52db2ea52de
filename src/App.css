@import url("https://rsms.me/inter/inter.css");
html {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
}

.fix {
    margin-top: -32px;
}

.filter-white {
    filter: invert(96%) sepia(4%) saturate(17%) hue-rotate(165deg) brightness(105%) contrast(105%);
}

.gradient {
  background-image:linear-gradient(90deg, rgba(128,216,216,1) 0%, rgba(216,232,232,1) 35%, rgb(48, 167, 167) 100%);
}

.bg-brain {
  background-image: url( '/assets/brain-solid-svgrepo-com.svg' );
  background-repeat: no-repeat;
  margin:0 auto; 
  background-position: 50% 50%;
  background-size: contain;
  background-size: 30%;
}

.bg-teal-dark {
  background-color: rgb(48, 167, 167);
  opacity: 96%;
}

.bg-teal-mid {
  background-color:rgba(128,216,216,1);
  opacity: 96%;
};

.bg-teal-light {
  background-color: rgb(48, 167, 167);
}

h1 {
  line-height: 3em;
}

button {
  background-color: teal;
  color: white;
  /* background-image: linear-gradient(315deg, #524f4e 0%, #e9e9e7 74%); */
}

.gradient2 {
  background-color: #f39f86;
  background-image: linear-gradient(315deg, #f39f86 0%, #f9d976 74%);
}

/* Browser mockup code
* Contribute: https://gist.github.com/jarthod/8719db9fef8deb937f4f
* Live example: https://updown.io
*/

.browser-mockup {
  border-top: 2em solid rgba(230, 230, 230, 0.7);
  position: relative;
}

.browser-mockup:before {
  display: block;
  position: absolute;
  content: "";
  top: -1.25em;
  left: 1em;
  width: 0.5em;
  height: 0.5em;
  border-radius: 50%;
  background-color: #f44;
  box-shadow: 0 0 0 2px #f44, 1.5em 0 0 2px #9b3, 3em 0 0 2px #fb5;
}

.browser-mockup > * {
  display: block;
}