import Landing from "./components/Landing";
import TherapistsLanding from "./components/TherapistsLanding";
import {Routes, Route} from "react-router-dom"
import Embed from "./components/Embed";
import Login from "./components/Login/LogIn";
import Join from "./components/Login/Join";
import { AuthProvider } from './components/contexts/AuthContext'
import HeaderNav from "./components/HeaderNav";
import MoodAssessment from "./components/demo/MoodAssessment";
import Footer from "./components/Footer";
import { CreateTherapistProfile } from './components/Therapists/CreateTherapistProfile';
import { EditTherapistProfile } from './components/Therapists/EditTherapistProfile';
import { ViewTherapistProfile } from './components/Therapists/ViewTherapistProfile';
import { PublicViewTherapistProfile } from './components/Therapists/PublicViewTherapistProfile';
import Invite from "./components/Invites/Invite";
import Matches from './components/Matches/Matches';
import DemoEmbed from "./components/demo/DemoEmbed";
import ForgotPassword from './components/auth/ForgotPassword';
import ResetPassword from './components/auth/ResetPassword';
import Chat from './components/demo/Chat';
import Result from './components/demo/Result';
import TherapyView from './components/Therapies/TherapyView';
import ConditionClassification from './components/demo/ConditionClassification';
import Terms from './components/legal/Terms';
import Privacy from './components/legal/Privacy';

const App = () => {

  const isEmbed = location.pathname === "/embed" || location.pathname === "/demo/embed";

  return (
    <div>
      { isEmbed ? 
       <Routes>
        <Route path="/embed" element={<Embed />} />
        <Route path="/demo/embed" element={<DemoEmbed />} />
       </Routes>
       : 
        <div className="h-fit gradient leading-relaxed tracking-wide min-w-8/10 mx-auto flex flex-col">
          <AuthProvider>
            <HeaderNav />   
            <>
            <Routes>
                <Route path="/" element={<MoodAssessment />} />
                <Route path="/about" element={<Landing />} />
                <Route path="/therapists" element={<TherapistsLanding />} />
                <Route path="/login" element={<Login />} />
                <Route path="/join" element={<Join />} />
                <Route path="/invite" element={<Invite />} />
                <Route path="/demo" element={<MoodAssessment />} />
                <Route path="/therapists/create-profile" element={<CreateTherapistProfile />} />
                <Route path="/therapists/edit-profile" element={<EditTherapistProfile />} />
                <Route path="/therapists/profile" element={<ViewTherapistProfile />} />
                <Route path="/therapists/profile/:id" element={<PublicViewTherapistProfile />} />
                <Route path="/therapies/:id" element={<TherapyView />} />
                <Route path="/matches" element={<Matches />} />
                <Route path="/forgot-password" element={<ForgotPassword />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/demo/chat" element={<Chat />} />
                <Route path="/result/:id" element={<Result />} />
                <Route path="/demo/assessment" element={<ConditionClassification />} />
                <Route path="/terms" element={<Terms />} />
                <Route path="/privacy" element={<Privacy />} />
            </Routes>
            </>
            <Footer />
          </AuthProvider>
        </div>
      }

    </div>
  );
};

export default App;
