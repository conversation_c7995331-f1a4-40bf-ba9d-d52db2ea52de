{"name": "react-tailwindcss-datepicker", "version": "1.7.3", "description": "A modern React Datepicker using Tailwind CSS 3", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "author": "onesine", "license": "MIT", "scripts": {"watch": "rollup -c -w", "clean": "rm -rf dist .rollup.cache tsconfig.tsbuildinfo", "lint": "eslint .", "lint:fix": "eslint --fix .", "pret": "prettier -c .", "pret:fix": "prettier --ignore-path .gitignore --config ./.prettierrc --write './**/*.{js,jsx,ts,tsx,css,md,json}'", "code-style": "npm run pret && npm run lint", "code-style:fix": "npm run pret:fix && npm run pret:fix", "build": "npm run code-style && npm run clean && rollup -c rollup.config.js --bundleConfigAsCjs", "pub": "npm run build && npm publish", "dev": "next dev -p 8888", "prepare": "husky"}, "repository": {"type": "git", "url": "https://github.com/onesine/react-tailwindcss-datepicker"}, "keywords": ["react-tailwindcss-datepicker", "react-datepicker", "tailwind-datepicker", "datepicker", "date-picker", "daterangepicker", "react-daterangepicker", "date-range", "date-range-picker", "tailwind-daterange-picker"], "peerDependencies": {"dayjs": "^1.11.12", "react": "^17.0.2 || ^18.2.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@tailwindcss/forms": "^0.5.7", "@types/node": "^22.3.0", "@types/react": "^18.3.3", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "autoprefixer": "^10.4.20", "dayjs": "^1.11.12", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "husky": "^9.1.4", "lint-staged": "^15.2.9", "next": "^14.2.5", "pinst": "^3.0.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^4.20.0", "tailwindcss": "^3.4.10", "tslib": "^2.6.3", "typescript": "^5.5.4"}, "lint-staged": {"*.{ts,tsx}": ["eslint", "prettier --write"], "*.{css,scss,json,md}": ["prettier --write"]}, "files": ["dist"]}