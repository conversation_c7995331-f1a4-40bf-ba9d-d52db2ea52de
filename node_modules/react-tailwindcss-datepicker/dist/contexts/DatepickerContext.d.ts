import { ReactNode, RefObject } from "react";
import { Configs, Period, DateValueType, DateType, DateRangeType, ClassNamesTypeProp, PopoverDirectionType, ColorKeys, WeekStringType, DateLookingType } from "../types";
interface DatepickerStore {
    arrowContainer: RefObject<HTMLDivElement> | null;
    asSingle?: boolean;
    calendarContainer: RefObject<HTMLDivElement> | null;
    changeDatepickerValue: (value: DateValueType, e?: HTMLInputElement | null | undefined) => void;
    changeDayHover: (day: DateType) => void;
    changeInputText: (text: string) => void;
    changePeriod: (period: Period) => void;
    classNames?: ClassNamesTypeProp;
    configs?: Configs;
    containerClassName?: ((className: string) => string) | string | null;
    dateLooking?: DateLookingType;
    dayHover: DateType;
    disabled?: boolean;
    disabledDates?: DateRangeType[] | null;
    displayFormat: string;
    hideDatepicker: () => void;
    i18n: string;
    input?: RefObject<HTMLInputElement>;
    inputClassName?: ((className: string) => string) | string | null;
    inputId?: string;
    inputName?: string;
    inputText: string;
    maxDate?: DateType | null;
    minDate?: DateType | null;
    period: Period;
    placeholder?: string | null;
    popoverDirection?: PopoverDirectionType;
    primaryColor: ColorKeys;
    readOnly?: boolean;
    required?: boolean;
    separator: string;
    showFooter?: boolean;
    startWeekOn?: WeekStringType | null;
    toggleClassName?: ((className: string) => string) | string | null;
    toggleIcon?: (open: boolean) => ReactNode;
    updateFirstDate: (date: Date) => void;
    value: DateValueType;
}
declare const DatepickerContext: import("react").Context<DatepickerStore>;
export default DatepickerContext;
