import {
  require_react
} from "./chunk-NVDSUZN5.js";
import {
  __toESM
} from "./chunk-ZC22LKFR.js";

// node_modules/@re-dev/react-truncate/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var _ = (e) => e == null ? void 0 : e.offsetWidth;
var q = (e) => e.replace(/\s+$/, "");
var ee = (e, t, n) => {
  if (t === n.length - 1)
    return import_react2.default.createElement("span", { key: t }, e);
  {
    let s = import_react2.default.createElement("br", { key: `${t}br` });
    return e ? [import_react2.default.createElement("span", { key: t }, e), s] : s;
  }
};
var te = (e, t) => {
  var _a;
  let n = document.createElement("div"), s = "innerText" in window.HTMLElement.prototype ? "innerText" : "textContent";
  n.innerHTML = (e == null ? void 0 : e.innerHTML.replace(/\r\n|\r|\n/g, t)) || "";
  let i = n[s], o = document.createElement("div");
  return o.innerHTML = "foo<br/>bar", ((_a = o[s]) == null ? void 0 : _a.replace(/\r\n|\r/g, `
`)) !== `foo
bar` && (n.innerHTML = n.innerHTML.replace(/<br.*?[/]?>/gi, `
`), i = n[s]), i || "";
};
var ne = ({ end: e, lastLineText: t, fullText: n, targetWidth: s, ellipsisWidth: i, measureWidth: o }) => {
  let l = t.length, w = Math.abs(e), h = w > l ? 0 : l - w, p = t.slice(0, h), m = h === 0 ? -l : e, y = n.slice(m), H = o(p) + o(y) + i;
  for (; H > s; )
    p = p.slice(0, p.length - 1), H = o(p) + o(y) + i;
  return { startFragment: p, endFragment: y };
};
var J = ({ children: e, ellipsis: t = "…", lines: n = 1, trimWhitespace: s = false, width: i = 0, separator: o = " ", middle: l = false, end: w = 5, onTruncate: h, ...p }) => {
  let [m, y] = (0, import_react.useState)(), [H, I] = (0, import_react.useState)(), [f, z] = (0, import_react.useState)(0), [N, k] = (0, import_react.useState)(0), d = (0, import_react.useRef)(null), a = (0, import_react.useRef)(null), E = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(() => {
    a && a.current && a.current.parentNode && a.current.parentNode.removeChild(a.current);
  }, [f]);
  let M = (0, import_react.useCallback)(() => {
    var _a;
    if (!((_a = d.current) == null ? void 0 : _a.parentElement))
      return;
    let r = i || Math.floor(d.current.parentElement.getBoundingClientRect().width);
    if (!r)
      return window.requestAnimationFrame(() => M());
    let c = window.getComputedStyle(d.current), F = [c.fontWeight, c.fontStyle, c.fontSize, c.fontFamily].join(" ");
    m && (m.font = F, m.letterSpacing = c.letterSpacing), z(r);
  }, [m, i]);
  (0, import_react.useEffect)(() => {
    let r = document.createElement("canvas");
    y(r.getContext("2d"));
  }, []), (0, import_react.useEffect)(() => (M(), window.addEventListener("resize", M), () => {
    window.removeEventListener("resize", M), window.cancelAnimationFrame(N);
  }), [M, N]);
  let S = (0, import_react.useCallback)((r) => {
    typeof h == "function" && k(window.requestAnimationFrame(() => {
      h(r);
    }));
  }, [h]), W = (0, import_react.useCallback)((r) => (m == null ? void 0 : m.measureText(r).width) || 0, [m]), U = (0, import_react.useMemo)(() => !Number.isSafeInteger(n) || n < 0 ? 0 : n, [n]), v = (0, import_react.useMemo)(() => l ? 1 : U, [U, l]), D = (0, import_react.useMemo)(() => {
    let r = Math.abs(w), c = Number.isFinite(r) ? Math.floor(r) : 0;
    return c > 0 ? -c : c;
  }, [w]), Q = (0, import_react.useCallback)(() => {
    let r = [], c = te(a.current, o), F = c.split(`
`).map((L) => L.split(o)), Y = _(E.current) || 0, Z = true;
    for (let L = 1; L <= v; L++) {
      let R = F[0];
      if (R.length === 0) {
        r.push(), F.shift(), L--;
        continue;
      }
      let C = R.join(o) || "";
      if (W(C) <= f && F.length === 1) {
        Z = false, r.push(C);
        break;
      }
      if (L === v) {
        let u = R.join(o), x = 0, b = u.length - 1;
        for (; x <= b; ) {
          let g = Math.floor((x + b) / 2), V = u.slice(0, g + 1);
          W(V) + Y <= f ? x = g + 1 : b = g - 1;
        }
        let T = u.slice(0, x);
        if (s)
          for (T = q(T); !T.length && r.length; ) {
            let g = r.pop();
            g && typeof g == "string" && (T = q(g));
          }
        if (l && D !== 0) {
          let { startFragment: g, endFragment: V } = ne({ end: D, lastLineText: T, fullText: c, targetWidth: f, ellipsisWidth: Y, measureWidth: W });
          C = import_react.default.createElement("span", null, g, t, V);
        } else
          C = import_react.default.createElement("span", null, T, t);
      } else {
        let u = 0, x = R.length - 1;
        for (; u <= x; ) {
          let b = Math.floor((u + x) / 2), T = R.slice(0, b + 1).join(o);
          W(T) <= f ? u = b + 1 : x = b - 1;
        }
        if (u === 0) {
          L = v - 1;
          continue;
        }
        C = R.slice(0, u).join(o), F[0].splice(0, u);
      }
      r.push(C);
    }
    return S(Z), r;
  }, [o, S, v, W, f, s, D, l, t]);
  return (0, import_react.useEffect)(() => {
    let r = !!(d.current && f);
    typeof window < "u" && r && (v > 0 ? I(Q().map(ee)) : (I(e), S(false)));
  }, [e, v, f, Q, S]), import_react.default.createElement("span", { ...p, ref: d }, import_react.default.createElement("span", null, H), import_react.default.createElement("span", { ref: a }, e), import_react.default.createElement("span", { ref: E, style: { position: "fixed", visibility: "hidden", top: 0, left: 0 } }, t));
};
var le = (0, import_react3.forwardRef)(({ children: e, ...t }, n) => {
  let { width: s, middle: i, lines: o, ...l } = t;
  return import_react3.default.createElement("div", { ref: n, style: { width: "100%" } }, import_react3.default.createElement(J, { ...l, middle: true }, e));
});
le.displayName = "MiddleTruncate";
var K = ({ type: e, label: t, className: n, toggleLines: s }) => {
  let i = (0, import_react5.useMemo)(() => e === "more" ? "… " : " ", [e]);
  return (0, import_react5.isValidElement)(t) ? t : import_react5.default.createElement("span", null, i, import_react5.default.createElement("a", { href: "#", className: n, onClick: s, "data-testid": `${e}-button` }, t));
};
var de = (0, import_react4.forwardRef)(({ defaultExpanded: e = false, expanded: t, lines: n = 3, more: s = "Expand", less: i = "Collapse", anchorClass: o, onToggle: l, children: w, ...h }, p) => {
  let { width: m, middle: y, end: H, ellipsis: I, ...f } = h, [, z] = (0, import_react4.useState)({}), N = (0, import_react4.useCallback)(() => z({}), []), k = (0, import_react4.useRef)(e), d = typeof t == "boolean", a = d ? t : k.current, E = (0, import_react4.useCallback)((M) => {
    M.preventDefault();
    let S = !a;
    d || (k.current = S, N()), l == null ? void 0 : l(S);
  }, [a, d, l, N]);
  return (0, import_react4.useImperativeHandle)(p, () => ({ toggleLines: E })), import_react4.default.createElement("div", { style: { width: "100%" } }, import_react4.default.createElement(J, { ...f, lines: a ? 0 : n, ellipsis: import_react4.default.createElement(K, { type: "more", label: s, className: o, toggleLines: E }) }, w), a && import_react4.default.createElement(K, { type: "less", label: i, className: o, toggleLines: E }));
});
de.displayName = "ShowMore";
export {
  le as MiddleTruncate,
  de as ShowMore,
  J as Truncate
};
//# sourceMappingURL=@re-dev_react-truncate.js.map
