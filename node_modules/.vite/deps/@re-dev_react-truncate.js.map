{"version": 3, "sources": ["../../@re-dev/react-truncate/dist/index.mjs"], "sourcesContent": ["/**\n * name: @re-dev/react-truncate\n * version: v0.5.0\n * description: Provides `Truncate`, `MiddleTruncate` and `ShowMore` React components for truncating multi-line spans and adding an ellipsis.\n * author: chengpeiquan <<EMAIL>>\n * homepage: https://truncate.js.org\n * license: MIT\n */\nimport P,{useCallback as j,useEffect as A,useMemo as G,useRef as O,useState as B}from\"react\";import $ from\"react\";var _=e=>e?.offsetWidth,q=e=>e.replace(/\\s+$/,\"\"),ee=(e,t,n)=>{if(t===n.length-1)return $.createElement(\"span\",{key:t},e);{let s=$.createElement(\"br\",{key:`${t}br`});return e?[$.createElement(\"span\",{key:t},e),s]:s}},te=(e,t)=>{let n=document.createElement(\"div\"),s=\"innerText\"in window.HTMLElement.prototype?\"innerText\":\"textContent\";n.innerHTML=e?.innerHTML.replace(/\\r\\n|\\r|\\n/g,t)||\"\";let i=n[s],o=document.createElement(\"div\");return o.innerHTML=\"foo<br/>bar\",o[s]?.replace(/\\r\\n|\\r/g,`\n`)!==`foo\nbar`&&(n.innerHTML=n.innerHTML.replace(/<br.*?[/]?>/gi,`\n`),i=n[s]),i||\"\"},ne=({end:e,lastLineText:t,fullText:n,targetWidth:s,ellipsisWidth:i,measureWidth:o})=>{let l=t.length,w=Math.abs(e),h=w>l?0:l-w,p=t.slice(0,h),m=h===0?-l:e,y=n.slice(m),H=o(p)+o(y)+i;for(;H>s;)p=p.slice(0,p.length-1),H=o(p)+o(y)+i;return{startFragment:p,endFragment:y}};var J=({children:e,ellipsis:t=\"\\u2026\",lines:n=1,trimWhitespace:s=!1,width:i=0,separator:o=\" \",middle:l=!1,end:w=5,onTruncate:h,...p})=>{let[m,y]=B(),[H,I]=B(),[f,z]=B(0),[N,k]=B(0),d=O(null),a=O(null),E=O(null);A(()=>{a&&a.current&&a.current.parentNode&&a.current.parentNode.removeChild(a.current)},[f]);let M=j(()=>{if(!d.current?.parentElement)return;let r=i||Math.floor(d.current.parentElement.getBoundingClientRect().width);if(!r)return window.requestAnimationFrame(()=>M());let c=window.getComputedStyle(d.current),F=[c.fontWeight,c.fontStyle,c.fontSize,c.fontFamily].join(\" \");m&&(m.font=F,m.letterSpacing=c.letterSpacing),z(r)},[m,i]);A(()=>{let r=document.createElement(\"canvas\");y(r.getContext(\"2d\"))},[]),A(()=>(M(),window.addEventListener(\"resize\",M),()=>{window.removeEventListener(\"resize\",M),window.cancelAnimationFrame(N)}),[M,N]);let S=j(r=>{typeof h==\"function\"&&k(window.requestAnimationFrame(()=>{h(r)}))},[h]),W=j(r=>m?.measureText(r).width||0,[m]),U=G(()=>!Number.isSafeInteger(n)||n<0?0:n,[n]),v=G(()=>l?1:U,[U,l]),D=G(()=>{let r=Math.abs(w),c=Number.isFinite(r)?Math.floor(r):0;return c>0?-c:c},[w]),Q=j(()=>{let r=[],c=te(a.current,o),F=c.split(`\n`).map(L=>L.split(o)),Y=_(E.current)||0,Z=!0;for(let L=1;L<=v;L++){let R=F[0];if(R.length===0){r.push(),F.shift(),L--;continue}let C=R.join(o)||\"\";if(W(C)<=f&&F.length===1){Z=!1,r.push(C);break}if(L===v){let u=R.join(o),x=0,b=u.length-1;for(;x<=b;){let g=Math.floor((x+b)/2),V=u.slice(0,g+1);W(V)+Y<=f?x=g+1:b=g-1}let T=u.slice(0,x);if(s)for(T=q(T);!T.length&&r.length;){let g=r.pop();g&&typeof g==\"string\"&&(T=q(g))}if(l&&D!==0){let{startFragment:g,endFragment:V}=ne({end:D,lastLineText:T,fullText:c,targetWidth:f,ellipsisWidth:Y,measureWidth:W});C=P.createElement(\"span\",null,g,t,V)}else C=P.createElement(\"span\",null,T,t)}else{let u=0,x=R.length-1;for(;u<=x;){let b=Math.floor((u+x)/2),T=R.slice(0,b+1).join(o);W(T)<=f?u=b+1:x=b-1}if(u===0){L=v-1;continue}C=R.slice(0,u).join(o),F[0].splice(0,u)}r.push(C)}return S(Z),r},[o,S,v,W,f,s,D,l,t]);return A(()=>{let r=!!(d.current&&f);typeof window<\"u\"&&r&&(v>0?I(Q().map(ee)):(I(e),S(!1)))},[e,v,f,Q,S]),P.createElement(\"span\",{...p,ref:d},P.createElement(\"span\",null,H),P.createElement(\"span\",{ref:a},e),P.createElement(\"span\",{ref:E,style:{position:\"fixed\",visibility:\"hidden\",top:0,left:0}},t))};import re,{forwardRef as ie}from\"react\";var le=ie(({children:e,...t},n)=>{let{width:s,middle:i,lines:o,...l}=t;return re.createElement(\"div\",{ref:n,style:{width:\"100%\"}},re.createElement(J,{...l,middle:!0},e))});le.displayName=\"MiddleTruncate\";import X,{forwardRef as pe,useCallback as se,useImperativeHandle as fe,useRef as ue,useState as me}from\"react\";import oe,{isValidElement as ae,useMemo as ce}from\"react\";var K=({type:e,label:t,className:n,toggleLines:s})=>{let i=ce(()=>e===\"more\"?\"\\u2026 \":\" \",[e]);return ae(t)?t:oe.createElement(\"span\",null,i,oe.createElement(\"a\",{href:\"#\",className:n,onClick:s,\"data-testid\":`${e}-button`},t))};var de=pe(({defaultExpanded:e=!1,expanded:t,lines:n=3,more:s=\"Expand\",less:i=\"Collapse\",anchorClass:o,onToggle:l,children:w,...h},p)=>{let{width:m,middle:y,end:H,ellipsis:I,...f}=h,[,z]=me({}),N=se(()=>z({}),[]),k=ue(e),d=typeof t==\"boolean\",a=d?t:k.current,E=se(M=>{M.preventDefault();let S=!a;d||(k.current=S,N()),l?.(S)},[a,d,l,N]);return fe(p,()=>({toggleLines:E})),X.createElement(\"div\",{style:{width:\"100%\"}},X.createElement(J,{...f,lines:a?0:n,ellipsis:X.createElement(K,{type:\"more\",label:s,className:o,toggleLines:E})},w),a&&X.createElement(K,{type:\"less\",label:i,className:o,toggleLines:E}))});de.displayName=\"ShowMore\";export{le as MiddleTruncate,de as ShowMore,J as Truncate};\n"], "mappings": ";;;;;;;;AAQA,mBAAqF;AAAQ,IAAAA,gBAAa;AAIihC,IAAAA,gBAAgC;AAAoN,IAAAA,gBAAuG;AAAQ,IAAAA,gBAAkD;AAJ95C,IAAI,IAAE,OAAG,uBAAG;AAAZ,IAAwB,IAAE,OAAG,EAAE,QAAQ,QAAO,EAAE;AAAhD,IAAkD,KAAG,CAAC,GAAE,GAAE,MAAI;AAAC,MAAG,MAAI,EAAE,SAAO;AAAE,WAAO,cAAAC,QAAE,cAAc,QAAO,EAAC,KAAI,EAAC,GAAE,CAAC;AAAE;AAAC,QAAI,IAAE,cAAAA,QAAE,cAAc,MAAK,EAAC,KAAI,GAAG,CAAC,KAAI,CAAC;AAAE,WAAO,IAAE,CAAC,cAAAA,QAAE,cAAc,QAAO,EAAC,KAAI,EAAC,GAAE,CAAC,GAAE,CAAC,IAAE;AAAA,EAAC;AAAC;AAAvN,IAAyN,KAAG,CAAC,GAAE,MAAI;AARrV;AAQsV,MAAI,IAAE,SAAS,cAAc,KAAK,GAAE,IAAE,eAAc,OAAO,YAAY,YAAU,cAAY;AAAc,IAAE,aAAU,uBAAG,UAAU,QAAQ,eAAc,OAAI;AAAG,MAAI,IAAE,EAAE,CAAC,GAAE,IAAE,SAAS,cAAc,KAAK;AAAE,SAAO,EAAE,YAAU,iBAAc,OAAE,CAAC,MAAH,mBAAM,QAAQ,YAAW;AAAA,QACvlB;AAAA,SACE,EAAE,YAAU,EAAE,UAAU,QAAQ,iBAAgB;AAAA,CACtD,GAAE,IAAE,EAAE,CAAC,IAAG,KAAG;AAAE;AAHkG,IAGhG,KAAG,CAAC,EAAC,KAAI,GAAE,cAAa,GAAE,UAAS,GAAE,aAAY,GAAE,eAAc,GAAE,cAAa,EAAC,MAAI;AAAC,MAAI,IAAE,EAAE,QAAO,IAAE,KAAK,IAAI,CAAC,GAAE,IAAE,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,EAAE,MAAM,GAAE,CAAC,GAAE,IAAE,MAAI,IAAE,CAAC,IAAE,GAAE,IAAE,EAAE,MAAM,CAAC,GAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAE,SAAK,IAAE;AAAG,QAAE,EAAE,MAAM,GAAE,EAAE,SAAO,CAAC,GAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAE,SAAM,EAAC,eAAc,GAAE,aAAY,EAAC;AAAC;AAAE,IAAI,IAAE,CAAC,EAAC,UAAS,GAAE,UAAS,IAAE,KAAS,OAAM,IAAE,GAAE,gBAAe,IAAE,OAAG,OAAM,IAAE,GAAE,WAAU,IAAE,KAAI,QAAO,IAAE,OAAG,KAAI,IAAE,GAAE,YAAW,GAAE,GAAG,EAAC,MAAI;AAAC,MAAG,CAAC,GAAE,CAAC,QAAE,aAAAC,UAAE,GAAE,CAAC,GAAE,CAAC,QAAE,aAAAA,UAAE,GAAE,CAAC,GAAE,CAAC,QAAE,aAAAA,UAAE,CAAC,GAAE,CAAC,GAAE,CAAC,QAAE,aAAAA,UAAE,CAAC,GAAE,QAAE,aAAAC,QAAE,IAAI,GAAE,QAAE,aAAAA,QAAE,IAAI,GAAE,QAAE,aAAAA,QAAE,IAAI;AAAE,mBAAAC,WAAE,MAAI;AAAC,SAAG,EAAE,WAAS,EAAE,QAAQ,cAAY,EAAE,QAAQ,WAAW,YAAY,EAAE,OAAO;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC;AAAE,MAAI,QAAE,aAAAC,aAAE,MAAI;AAX5lB;AAW6lB,QAAG,GAAC,OAAE,YAAF,mBAAW;AAAc;AAAO,QAAI,IAAE,KAAG,KAAK,MAAM,EAAE,QAAQ,cAAc,sBAAsB,EAAE,KAAK;AAAE,QAAG,CAAC;AAAE,aAAO,OAAO,sBAAsB,MAAI,EAAE,CAAC;AAAE,QAAI,IAAE,OAAO,iBAAiB,EAAE,OAAO,GAAE,IAAE,CAAC,EAAE,YAAW,EAAE,WAAU,EAAE,UAAS,EAAE,UAAU,EAAE,KAAK,GAAG;AAAE,UAAI,EAAE,OAAK,GAAE,EAAE,gBAAc,EAAE,gBAAe,EAAE,CAAC;AAAA,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,mBAAAD,WAAE,MAAI;AAAC,QAAI,IAAE,SAAS,cAAc,QAAQ;AAAE,MAAE,EAAE,WAAW,IAAI,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,OAAE,aAAAA,WAAE,OAAK,EAAE,GAAE,OAAO,iBAAiB,UAAS,CAAC,GAAE,MAAI;AAAC,WAAO,oBAAoB,UAAS,CAAC,GAAE,OAAO,qBAAqB,CAAC;AAAA,EAAC,IAAG,CAAC,GAAE,CAAC,CAAC;AAAE,MAAI,QAAE,aAAAC,aAAE,OAAG;AAAC,WAAO,KAAG,cAAY,EAAE,OAAO,sBAAsB,MAAI;AAAC,QAAE,CAAC;AAAA,IAAC,CAAC,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,QAAE,aAAAA,aAAE,QAAG,uBAAG,YAAY,GAAG,UAAO,GAAE,CAAC,CAAC,CAAC,GAAE,QAAE,aAAAC,SAAE,MAAI,CAAC,OAAO,cAAc,CAAC,KAAG,IAAE,IAAE,IAAE,GAAE,CAAC,CAAC,CAAC,GAAE,QAAE,aAAAA,SAAE,MAAI,IAAE,IAAE,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,QAAE,aAAAA,SAAE,MAAI;AAAC,QAAI,IAAE,KAAK,IAAI,CAAC,GAAE,IAAE,OAAO,SAAS,CAAC,IAAE,KAAK,MAAM,CAAC,IAAE;AAAE,WAAO,IAAE,IAAE,CAAC,IAAE;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,QAAE,aAAAD,aAAE,MAAI;AAAC,QAAI,IAAE,CAAC,GAAE,IAAE,GAAG,EAAE,SAAQ,CAAC,GAAE,IAAE,EAAE,MAAM;AAAA,CACh7C,EAAE,IAAI,OAAG,EAAE,MAAM,CAAC,CAAC,GAAE,IAAE,EAAE,EAAE,OAAO,KAAG,GAAE,IAAE;AAAG,aAAQ,IAAE,GAAE,KAAG,GAAE,KAAI;AAAC,UAAI,IAAE,EAAE,CAAC;AAAE,UAAG,EAAE,WAAS,GAAE;AAAC,UAAE,KAAK,GAAE,EAAE,MAAM,GAAE;AAAI;AAAA,MAAQ;AAAC,UAAI,IAAE,EAAE,KAAK,CAAC,KAAG;AAAG,UAAG,EAAE,CAAC,KAAG,KAAG,EAAE,WAAS,GAAE;AAAC,YAAE,OAAG,EAAE,KAAK,CAAC;AAAE;AAAA,MAAK;AAAC,UAAG,MAAI,GAAE;AAAC,YAAI,IAAE,EAAE,KAAK,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,SAAO;AAAE,eAAK,KAAG,KAAG;AAAC,cAAI,IAAE,KAAK,OAAO,IAAE,KAAG,CAAC,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,CAAC;AAAE,YAAE,CAAC,IAAE,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAA,QAAC;AAAC,YAAI,IAAE,EAAE,MAAM,GAAE,CAAC;AAAE,YAAG;AAAE,eAAI,IAAE,EAAE,CAAC,GAAE,CAAC,EAAE,UAAQ,EAAE,UAAQ;AAAC,gBAAI,IAAE,EAAE,IAAI;AAAE,iBAAG,OAAO,KAAG,aAAW,IAAE,EAAE,CAAC;AAAA,UAAE;AAAC,YAAG,KAAG,MAAI,GAAE;AAAC,cAAG,EAAC,eAAc,GAAE,aAAY,EAAC,IAAE,GAAG,EAAC,KAAI,GAAE,cAAa,GAAE,UAAS,GAAE,aAAY,GAAE,eAAc,GAAE,cAAa,EAAC,CAAC;AAAE,cAAE,aAAAE,QAAE,cAAc,QAAO,MAAK,GAAE,GAAE,CAAC;AAAA,QAAC;AAAM,cAAE,aAAAA,QAAE,cAAc,QAAO,MAAK,GAAE,CAAC;AAAA,MAAC,OAAK;AAAC,YAAI,IAAE,GAAE,IAAE,EAAE,SAAO;AAAE,eAAK,KAAG,KAAG;AAAC,cAAI,IAAE,KAAK,OAAO,IAAE,KAAG,CAAC,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,CAAC,EAAE,KAAK,CAAC;AAAE,YAAE,CAAC,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAA,QAAC;AAAC,YAAG,MAAI,GAAE;AAAC,cAAE,IAAE;AAAE;AAAA,QAAQ;AAAC,YAAE,EAAE,MAAM,GAAE,CAAC,EAAE,KAAK,CAAC,GAAE,EAAE,CAAC,EAAE,OAAO,GAAE,CAAC;AAAA,MAAC;AAAC,QAAE,KAAK,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,CAAC,GAAE;AAAA,EAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,aAAO,aAAAH,WAAE,MAAI;AAAC,QAAI,IAAE,CAAC,EAAE,EAAE,WAAS;AAAG,WAAO,SAAO,OAAK,MAAI,IAAE,IAAE,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,EAAE,KAAE;AAAA,EAAG,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,aAAAG,QAAE,cAAc,QAAO,EAAC,GAAG,GAAE,KAAI,EAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,MAAK,CAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,KAAI,EAAC,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,KAAI,GAAE,OAAM,EAAC,UAAS,SAAQ,YAAW,UAAS,KAAI,GAAE,MAAK,EAAC,EAAC,GAAE,CAAC,CAAC;AAAC;AAA0C,IAAI,SAAG,cAAAC,YAAG,CAAC,EAAC,UAAS,GAAE,GAAG,EAAC,GAAE,MAAI;AAAC,MAAG,EAAC,OAAM,GAAE,QAAO,GAAE,OAAM,GAAE,GAAG,EAAC,IAAE;AAAE,SAAO,cAAAC,QAAG,cAAc,OAAM,EAAC,KAAI,GAAE,OAAM,EAAC,OAAM,OAAM,EAAC,GAAE,cAAAA,QAAG,cAAc,GAAE,EAAC,GAAG,GAAE,QAAO,KAAE,GAAE,CAAC,CAAC;AAAC,CAAC;AAAE,GAAG,cAAY;AAA0L,IAAI,IAAE,CAAC,EAAC,MAAK,GAAE,OAAM,GAAE,WAAU,GAAE,aAAY,EAAC,MAAI;AAAC,MAAI,QAAE,cAAAC,SAAG,MAAI,MAAI,SAAO,OAAU,KAAI,CAAC,CAAC,CAAC;AAAE,aAAO,cAAAC,gBAAG,CAAC,IAAE,IAAE,cAAAC,QAAG,cAAc,QAAO,MAAK,GAAE,cAAAA,QAAG,cAAc,KAAI,EAAC,MAAK,KAAI,WAAU,GAAE,SAAQ,GAAE,eAAc,GAAG,CAAC,UAAS,GAAE,CAAC,CAAC;AAAC;AAAE,IAAI,SAAG,cAAAC,YAAG,CAAC,EAAC,iBAAgB,IAAE,OAAG,UAAS,GAAE,OAAM,IAAE,GAAE,MAAK,IAAE,UAAS,MAAK,IAAE,YAAW,aAAY,GAAE,UAAS,GAAE,UAAS,GAAE,GAAG,EAAC,GAAE,MAAI;AAAC,MAAG,EAAC,OAAM,GAAE,QAAO,GAAE,KAAI,GAAE,UAAS,GAAE,GAAG,EAAC,IAAE,GAAE,CAAC,EAAC,CAAC,QAAE,cAAAC,UAAG,CAAC,CAAC,GAAE,QAAE,cAAAC,aAAG,MAAI,EAAE,CAAC,CAAC,GAAE,CAAC,CAAC,GAAE,QAAE,cAAAC,QAAG,CAAC,GAAE,IAAE,OAAO,KAAG,WAAU,IAAE,IAAE,IAAE,EAAE,SAAQ,QAAE,cAAAD,aAAG,OAAG;AAAC,MAAE,eAAe;AAAE,QAAI,IAAE,CAAC;AAAE,UAAI,EAAE,UAAQ,GAAE,EAAE,IAAG,uBAAI;AAAA,EAAE,GAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,aAAO,cAAAE,qBAAG,GAAE,OAAK,EAAC,aAAY,EAAC,EAAE,GAAE,cAAAC,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,OAAM,OAAM,EAAC,GAAE,cAAAA,QAAE,cAAc,GAAE,EAAC,GAAG,GAAE,OAAM,IAAE,IAAE,GAAE,UAAS,cAAAA,QAAE,cAAc,GAAE,EAAC,MAAK,QAAO,OAAM,GAAE,WAAU,GAAE,aAAY,EAAC,CAAC,EAAC,GAAE,CAAC,GAAE,KAAG,cAAAA,QAAE,cAAc,GAAE,EAAC,MAAK,QAAO,OAAM,GAAE,WAAU,GAAE,aAAY,EAAC,CAAC,CAAC;AAAC,CAAC;AAAE,GAAG,cAAY;", "names": ["import_react", "$", "B", "O", "A", "j", "G", "P", "ie", "re", "ce", "ae", "oe", "pe", "me", "se", "ue", "fe", "X"]}