{"version": 3, "sources": ["../../vanilla-cookieconsent/dist/cookieconsent.esm.js"], "sourcesContent": ["/*!\n* CookieConsent 3.1.0\n* https://github.com/orestbida/cookieconsent\n* Author <PERSON><PERSON> Bida\n* Released under the MIT License\n*/\nconst e='opt-in',t='opt-out',o='show--consent',n='show--preferences',a='disable--interaction',s='data-category',c='div',r='button',i='aria-hidden',l='btn-group',d='click',f='data-role',_='consentModal',u='preferencesModal';class p{constructor(){this.t={mode:e,revision:0,autoShow:!0,lazyHtmlGeneration:!0,autoClearCookies:!0,manageScriptTags:!0,hideFromBots:!0,cookie:{name:'cc_cookie',expiresAfterDays:182,domain:'',path:'/',secure:!0,sameSite:'Lax'}},this.o={i:{},l:'',_:{},u:{},p:{},m:[],v:!1,h:null,C:null,S:null,M:'',D:!0,T:!1,k:!1,A:!1,N:!1,H:[],V:!1,I:!0,L:[],j:!1,F:'',P:!1,O:[],R:[],B:[],$:[],G:!1,J:!1,U:!1,q:[],K:[],W:[],X:{},Y:{},Z:{},ee:{},te:{},oe:[]},this.ne={ae:{},se:{}},this.ce={},this.re={ie:'cc:onFirstConsent',le:'cc:onConsent',de:'cc:onChange',fe:'cc:onModalShow',_e:'cc:onModalHide',ue:'cc:onModalReady'}}}const g=new p,m=(e,t)=>e.indexOf(t),b=(e,t)=>-1!==m(e,t),v=e=>Array.isArray(e),y=e=>'string'==typeof e,h=e=>!!e&&'object'==typeof e&&!v(e),C=e=>'function'==typeof e,w=e=>Object.keys(e),S=e=>Array.from(new Set(e)),x=()=>document.activeElement,M=e=>e.preventDefault(),D=(e,t)=>e.querySelectorAll(t),T=e=>e.dispatchEvent(new Event('change')),k=e=>{const t=document.createElement(e);return e===r&&(t.type=e),t},E=(e,t,o)=>e.setAttribute(t,o),A=(e,t,o)=>{e.removeAttribute(o?'data-'+t:t)},N=(e,t,o)=>e.getAttribute(o?'data-'+t:t),H=(e,t)=>e.appendChild(t),V=(e,t)=>e.classList.add(t),I=(e,t)=>V(e,'cm__'+t),L=(e,t)=>V(e,'pm__'+t),j=(e,t)=>e.classList.remove(t),F=e=>{if('object'!=typeof e)return e;if(e instanceof Date)return new Date(e.getTime());let t=Array.isArray(e)?[]:{};for(let o in e){let n=e[o];t[o]=F(n)}return t},P=()=>{const e={},{O:t,X:o,Y:n}=g.o;for(const a of t)e[a]=$(n[a],w(o[a]));return e},O=(e,t)=>dispatchEvent(new CustomEvent(e,{detail:t})),R=(e,t,o,n)=>{e.addEventListener(t,o),n&&g.o.m.push({pe:e,ge:t,me:o})},B=()=>{const e=g.t.cookie.expiresAfterDays;return C(e)?e(g.o.F):e},$=(e,t)=>{const o=e||[],n=t||[];return o.filter((e=>!b(n,e))).concat(n.filter((e=>!b(o,e))))},G=e=>{g.o.R=S(e),g.o.F=(()=>{let e='custom';const{R:t,O:o,B:n}=g.o,a=t.length;return a===o.length?e='all':a===n.length&&(e='necessary'),e})()},J=(e,t,o,n)=>{const a='accept-',{show:s,showPreferences:c,hide:r,hidePreferences:i,acceptCategory:l}=t,f=e||document,_=e=>D(f,`[data-cc=\"${e}\"]`),u=(e,t)=>{M(e),l(t),i(),r()},p=_('show-preferencesModal'),m=_('show-consentModal'),b=_(a+'all'),v=_(a+'necessary'),y=_(a+'custom'),h=g.t.lazyHtmlGeneration;for(const e of p)E(e,'aria-haspopup','dialog'),R(e,d,(e=>{M(e),c()})),h&&(R(e,'mouseenter',(e=>{M(e),g.o.N||o(t,n)}),!0),R(e,'focus',(()=>{g.o.N||o(t,n)})));for(let e of m)E(e,'aria-haspopup','dialog'),R(e,d,(e=>{M(e),s(!0)}),!0);for(let e of b)R(e,d,(e=>{u(e,'all')}),!0);for(let e of y)R(e,d,(e=>{u(e)}),!0);for(let e of v)R(e,d,(e=>{u(e,[])}),!0)},U=(e,t)=>{e&&(t&&(e.tabIndex=-1),e.focus(),t&&e.removeAttribute('tabindex'))},z=(e,t)=>{const o=n=>{n.target.removeEventListener('transitionend',o),'opacity'===n.propertyName&&'1'===getComputedStyle(e).opacity&&U((e=>1===e?g.ne.be:g.ne.ve)(t))};R(e,'transitionend',o)};let q;const K=e=>{clearTimeout(q),e?V(g.ne.ye,a):q=setTimeout((()=>{j(g.ne.ye,a)}),500)},Q=['M 19.5 4.5 L 4.5 19.5 M 4.5 4.501 L 19.5 19.5','M 3.572 13.406 L 8.281 18.115 L 20.428 5.885','M 21.999 6.94 L 11.639 17.18 L 2.001 6.82 '],W=(e=0,t=1.5)=>`<svg viewBox=\"0 0 24 24\" stroke-width=\"${t}\"><path d=\"${Q[e]}\"/></svg>`,X=e=>{const t=g.ne,o=g.o;(e=>{const n=e===t.he,a=o.i.disablePageInteraction?t.ye:n?t.Ce:t.ye;R(a,'keydown',(t=>{if('Tab'!==t.key||!(n?o.k&&!o.A:o.A))return;const a=x(),s=n?o.q:o.K;0!==s.length&&(t.shiftKey?a!==s[0]&&e.contains(a)||(M(t),U(s[1])):a!==s[1]&&e.contains(a)||(M(t),U(s[0])))}),!0)})(e)},Y=['[href]',r,'input','details','[tabindex]'].map((e=>e+':not([tabindex=\"-1\"])')).join(','),Z=e=>{const{o:t,ne:o}=g,n=(e,t)=>{const o=D(e,Y);t[0]=o[0],t[1]=o[o.length-1]};1===e&&t.T&&n(o.he,t.q),2===e&&t.N&&n(o.we,t.K)},ee=(e,t,o)=>{const{de:n,le:a,ie:s,_e:c,ue:r,fe:i}=g.ce,l=g.re;if(t){const n={modalName:t};return e===l.fe?C(i)&&i(n):e===l._e?C(c)&&c(n):(n.modal=o,C(r)&&r(n)),O(e,n)}const d={cookie:g.o.p};e===l.ie?C(s)&&s(F(d)):e===l.le?C(a)&&a(F(d)):(d.changedCategories=g.o.L,d.changedServices=g.o.ee,C(n)&&n(F(d))),O(e,F(d))},te=(e,t)=>{try{return e()}catch(e){return!t&&console.warn('CookieConsent:',e),!1}},oe=e=>{const{Y:t,ee:o,O:n,X:a,oe:c,p:r,L:i}=g.o;for(const e of n){const n=o[e]||t[e]||[];for(const o of n){const n=a[e][o];if(!n)continue;const{onAccept:s,onReject:c}=n;!n.Se&&b(t[e],o)?(n.Se=!0,C(s)&&s()):n.Se&&!b(t[e],o)&&(n.Se=!1,C(c)&&c())}}if(!g.t.manageScriptTags)return;const l=c,d=e||r.categories||[],f=(e,n)=>{if(n>=e.length)return;const a=c[n];if(a.xe)return f(e,n+1);const r=a.Me,l=a.De,_=a.Te,u=b(d,l),p=!!_&&b(t[l],_);if(!_&&!a.ke&&u||!_&&a.ke&&!u&&b(i,l)||_&&!a.ke&&p||_&&a.ke&&!p&&b(o[l]||[],_)){a.xe=!0;const t=N(r,'type',!0);A(r,'type',!!t),A(r,s);let o=N(r,'src',!0);o&&A(r,'src',!0);const c=k('script');c.textContent=r.innerHTML;for(const{nodeName:e}of r.attributes)E(c,e,r[e]||N(r,e));t&&(c.type=t),o?c.src=o:o=r.src;const i=!!o&&(!t||['text/javascript','module'].includes(t));if(i&&(c.onload=c.onerror=()=>{f(e,++n)}),r.replaceWith(c),i)return}f(e,++n)};f(l,0)},ne='bottom',ae='left',se='center',ce='right',re='inline',ie='wide',le='pm--',de=['middle','top',ne],fe=[ae,se,ce],_e={box:{Ee:[ie,re],Ae:de,Ne:fe,He:ne,Ve:ce},cloud:{Ee:[re],Ae:de,Ne:fe,He:ne,Ve:se},bar:{Ee:[re],Ae:de.slice(1),Ne:[],He:ne,Ve:''}},ue={box:{Ee:[],Ae:[],Ne:[],He:'',Ve:''},bar:{Ee:[ie],Ae:[],Ne:[ae,ce],He:'',Ve:ae}},pe=e=>{const t=g.o.i.guiOptions,o=t&&t.consentModal,n=t&&t.preferencesModal;0===e&&ge(g.ne.he,_e,o,'cm--','box','cm'),1===e&&ge(g.ne.we,ue,n,le,'box','pm')},ge=(e,t,o,n,a,s)=>{e.className=s;const c=o&&o.layout,r=o&&o.position,i=o&&o.flipButtons,l=!o||!1!==o.equalWeightButtons,d=c&&c.split(' ')||[],f=d[0],_=d[1],u=f in t?f:a,p=t[u],m=b(p.Ee,_)&&_,v=r&&r.split(' ')||[],y=v[0],h=n===le?v[0]:v[1],C=b(p.Ae,y)?y:p.He,w=b(p.Ne,h)?h:p.Ve,S=t=>{t&&V(e,n+t)};S(u),S(m),S(C),S(w),i&&S('flip');const x=s+'__btn--secondary';if('cm'===s){const{Ie:e,Le:t}=g.ne;e&&(l?j(e,x):V(e,x)),t&&(l?j(t,x):V(t,x))}else{const{je:e}=g.ne;e&&(l?j(e,x):V(e,x))}},me=(e,t)=>{const o=g.o,n=g.ne,{hide:a,hidePreferences:s,acceptCategory:_}=e,p=e=>{_(e),s(),a()},m=o.u&&o.u.preferencesModal;if(!m)return;const b=m.title,v=m.closeIconLabel,C=m.acceptAllBtn,S=m.acceptNecessaryBtn,x=m.savePreferencesBtn,M=m.sections||[],D=C||S||x;if(n.Fe)n.Pe=k(c),L(n.Pe,'body');else{n.Fe=k(c),V(n.Fe,'pm-wrapper');const e=k('div');V(e,'pm-overlay'),H(n.Fe,e),R(e,d,s),n.we=k(c),V(n.we,'pm'),E(n.we,'role','dialog'),E(n.we,i,!0),E(n.we,'aria-modal',!0),E(n.we,'aria-labelledby','pm__title'),R(n.ye,'keydown',(e=>{27===e.keyCode&&s()}),!0),n.Oe=k(c),L(n.Oe,'header'),n.Re=k('h2'),L(n.Re,'title'),n.Re.id='pm__title',n.Be=k(r),L(n.Be,'close-btn'),E(n.Be,'aria-label',m.closeIconLabel||''),R(n.Be,d,s),n.$e=k('span'),n.$e.innerHTML=W(),H(n.Be,n.$e),n.Ge=k(c),L(n.Ge,'body'),n.Je=k(c),L(n.Je,'footer');var T=k(c);V(T,'btns');var A=k(c),N=k(c);L(A,l),L(N,l),H(n.Je,A),H(n.Je,N),H(n.Oe,n.Re),H(n.Oe,n.Be),n.ve=k(c),E(n.ve,'tabIndex',-1),H(n.we,n.ve),H(n.we,n.Oe),H(n.we,n.Ge),D&&H(n.we,n.Je),H(n.Fe,n.we)}let I;b&&(n.Re.innerHTML=b,v&&E(n.Be,'aria-label',v)),M.forEach(((e,t)=>{const a=e.title,s=e.description,l=e.linkedCategory,f=l&&o.P[l],_=e.cookieTable,u=_&&_.body,p=_&&_.caption,g=u&&u.length>0,b=!!f,v=b&&o.X[l],C=h(v)&&w(v)||[],S=b&&(!!s||!!g||w(v).length>0);var x=k(c);if(L(x,'section'),S||s){var M=k(c);L(M,'section-desc-wrapper')}let D=C.length;if(S&&D>0){const e=k(c);L(e,'section-services');for(const t of C){const o=v[t],n=o&&o.label||t,a=k(c),s=k(c),r=k(c),i=k(c);L(a,'service'),L(i,'service-title'),L(s,'service-header'),L(r,'service-icon');const d=be(n,t,f,!0,l);i.innerHTML=n,H(s,r),H(s,i),H(a,s),H(a,d),H(e,a)}H(M,e)}if(a){var T=k(c),A=k(b?r:c);if(L(T,'section-title-wrapper'),L(A,'section-title'),A.innerHTML=a,H(T,A),b){const e=k('span');e.innerHTML=W(2,3.5),L(e,'section-arrow'),H(T,e),x.className+='--toggle';const t=be(a,l,f);let o=m.serviceCounterLabel;if(D>0&&y(o)){let e=k('span');L(e,'badge'),L(e,'service-counter'),E(e,i,!0),E(e,'data-servicecounter',D),o&&(o=o.split('|'),o=o.length>1&&D>1?o[1]:o[0],E(e,'data-counterlabel',o)),e.innerHTML=D+(o?' '+o:''),H(A,e)}if(S){L(x,'section--expandable');var N=l+'-desc';E(A,'aria-expanded',!1),E(A,'aria-controls',N)}H(T,t)}else E(A,'role','heading'),E(A,'aria-level','3');H(x,T)}if(s){var F=k('p');L(F,'section-desc'),F.innerHTML=s,H(M,F)}if(S&&(E(M,i,'true'),M.id=N,((e,t,o)=>{R(A,d,(()=>{t.classList.contains('is-expanded')?(j(t,'is-expanded'),E(o,'aria-expanded','false'),E(e,i,'true')):(V(t,'is-expanded'),E(o,'aria-expanded','true'),E(e,i,'false'))}))})(M,x,A),g)){const e=k('table'),o=k('thead'),a=k('tbody');if(p){const t=k('caption');L(t,'table-caption'),t.innerHTML=p,e.appendChild(t)}L(e,'section-table'),L(o,'table-head'),L(a,'table-body');const s=_.headers,r=w(s),i=n.Ue.createDocumentFragment(),l=k('tr');for(const e of r){const o=s[e],n=k('th');n.id='cc__row-'+o+t,E(n,'scope','col'),L(n,'table-th'),n.innerHTML=o,H(i,n)}H(l,i),H(o,l);const d=n.Ue.createDocumentFragment();for(const e of u){const o=k('tr');L(o,'table-tr');for(const n of r){const a=s[n],r=e[n],i=k('td'),l=k(c);L(i,'table-td'),E(i,'data-column',a),E(i,'headers','cc__row-'+a+t),l.insertAdjacentHTML('beforeend',r),H(i,l),H(o,i)}H(d,o)}H(a,d),H(e,o),H(e,a),H(M,e)}(S||s)&&H(x,M);const P=n.Pe||n.Ge;b?(I||(I=k(c),L(I,'section-toggles')),I.appendChild(x)):I=null,H(P,I||x)})),C&&(n.ze||(n.ze=k(r),L(n.ze,'btn'),E(n.ze,f,'all'),H(A,n.ze),R(n.ze,d,(()=>p('all')))),n.ze.innerHTML=C),S&&(n.je||(n.je=k(r),L(n.je,'btn'),E(n.je,f,'necessary'),H(A,n.je),R(n.je,d,(()=>p([])))),n.je.innerHTML=S),x&&(n.qe||(n.qe=k(r),L(n.qe,'btn'),L(n.qe,'btn--secondary'),E(n.qe,f,'save'),H(N,n.qe),R(n.qe,d,(()=>p()))),n.qe.innerHTML=x),n.Pe&&(n.we.replaceChild(n.Pe,n.Ge),n.Ge=n.Pe),pe(1),o.N||(o.N=!0,ee(g.re.ue,u,n.we),t(e),H(n.Ce,n.Fe),X(n.we),setTimeout((()=>V(n.Fe,'cc--anim')),100)),Z(2)};function be(e,t,o,n,a){const c=g.o,r=g.ne,l=k('label'),f=k('input'),_=k('span'),u=k('span'),p=k('span'),m=k('span'),v=k('span');if(m.innerHTML=W(1,3),v.innerHTML=W(0,3),f.type='checkbox',V(l,'section__toggle-wrapper'),V(f,'section__toggle'),V(m,'toggle__icon-on'),V(v,'toggle__icon-off'),V(_,'toggle__icon'),V(u,'toggle__icon-circle'),V(p,'toggle__label'),E(_,i,'true'),n?(V(l,'toggle-service'),E(f,s,a),r.se[a][t]=f):r.ae[t]=f,n?(e=>{R(f,'change',(()=>{const t=r.se[e],o=r.ae[e];c.Z[e]=[];for(let o in t){const n=t[o];n.checked&&c.Z[e].push(n.value)}o.checked=c.Z[e].length>0}))})(a):(e=>{R(f,d,(()=>{const t=r.se[e],o=f.checked;c.Z[e]=[];for(let n in t)t[n].checked=o,o&&c.Z[e].push(n)}))})(t),f.value=t,p.textContent=e.replace(/<.*>.*<\\/.*>/gm,''),H(u,v),H(u,m),H(_,u),c.D)(o.readOnly||o.enabled)&&(f.checked=!0);else if(n){const e=c.Y[a];f.checked=o.readOnly||b(e,t)}else b(c.R,t)&&(f.checked=!0);return o.readOnly&&(f.disabled=!0),H(l,f),H(l,_),H(l,p),l}const ve=()=>{const e=k('span');return g.ne.Ke||(g.ne.Ke=e),e},ye=(e,t)=>{const o=g.o,n=g.ne,{hide:a,showPreferences:s,acceptCategory:u}=e,p=o.u&&o.u.consentModal;if(!p)return;const m=p.acceptAllBtn,b=p.acceptNecessaryBtn,v=p.showPreferencesBtn,y=p.closeIconLabel,h=p.footer,C=p.label,w=p.title,S=e=>{a(),u(e)};if(!n.Qe){n.Qe=k(c),n.he=k(c),n.We=k(c),n.Xe=k(c),n.Ye=k(c),V(n.Qe,'cm-wrapper'),V(n.he,'cm'),I(n.We,'body'),I(n.Xe,'texts'),I(n.Ye,'btns'),E(n.he,'role','dialog'),E(n.he,'aria-modal','true'),E(n.he,i,'false'),E(n.he,'aria-describedby','cm__desc'),C?E(n.he,'aria-label',C):w&&E(n.he,'aria-labelledby','cm__title');const e='box',t=o.i.guiOptions,a=t&&t.consentModal,s=(a&&a.layout||e).split(' ')[0]===e;w&&y&&s&&(n.Le||(n.Le=k(r),n.Le.innerHTML=W(),I(n.Le,'btn'),I(n.Le,'btn--close'),R(n.Le,d,(()=>{S([])})),H(n.We,n.Le)),E(n.Le,'aria-label',y)),H(n.We,n.Xe),(m||b||v)&&H(n.We,n.Ye),n.be=k(c),E(n.be,'tabIndex',-1),H(n.he,n.be),H(n.he,n.We),H(n.Qe,n.he)}w&&(n.Ze||(n.Ze=k('h2'),n.Ze.className=n.Ze.id='cm__title',H(n.Xe,n.Ze)),n.Ze.innerHTML=w);let x=p.description;if(x&&(o.V&&(x=x.replace('{{revisionMessage}}',o.I?'':p.revisionMessage||'')),n.et||(n.et=k('p'),n.et.className=n.et.id='cm__desc',H(n.Xe,n.et)),n.et.innerHTML=x),m&&(n.tt||(n.tt=k(r),H(n.tt,ve()),I(n.tt,'btn'),E(n.tt,f,'all'),R(n.tt,d,(()=>{S('all')}))),n.tt.firstElementChild.innerHTML=m),b&&(n.Ie||(n.Ie=k(r),H(n.Ie,ve()),I(n.Ie,'btn'),E(n.Ie,f,'necessary'),R(n.Ie,d,(()=>{S([])}))),n.Ie.firstElementChild.innerHTML=b),v&&(n.ot||(n.ot=k(r),H(n.ot,ve()),I(n.ot,'btn'),I(n.ot,'btn--secondary'),E(n.ot,f,'show'),R(n.ot,'mouseenter',(()=>{o.N||me(e,t)})),R(n.ot,d,s)),n.ot.firstElementChild.innerHTML=v),n.nt||(n.nt=k(c),I(n.nt,l),m&&H(n.nt,n.tt),b&&H(n.nt,n.Ie),(m||b)&&H(n.We,n.nt),H(n.Ye,n.nt)),n.ot&&!n.st&&(n.st=k(c),n.Ie&&n.tt?(I(n.st,l),H(n.st,n.ot),H(n.Ye,n.st)):(H(n.nt,n.ot),I(n.nt,l+'--uneven'))),h){if(!n.ct){let e=k(c),t=k(c);n.ct=k(c),I(e,'footer'),I(t,'links'),I(n.ct,'link-group'),H(t,n.ct),H(e,t),H(n.he,e)}n.ct.innerHTML=h}pe(0),o.T||(o.T=!0,ee(g.re.ue,_,n.he),t(e),H(n.Ce,n.Qe),X(n.he),setTimeout((()=>V(n.Qe,'cc--anim')),100)),Z(1),J(n.We,e,me,t)},he=e=>{if(!y(e))return null;if(e in g.o._)return e;let t=e.slice(0,2);return t in g.o._?t:null},Ce=()=>g.o.l||g.o.i.language.default,we=e=>{e&&(g.o.l=e)},Se=async e=>{const t=g.o;let o=he(e)?e:Ce(),n=t._[o];if(y(n)?n=await(async e=>{try{const t=await fetch(e);return await t.json()}catch(e){return console.error(e),!1}})(n):C(n)&&(n=await n()),!n)throw`Could not load translation for the '${o}' language`;return t.u=n,we(o),!0},xe=()=>{let e=g.o.i.language.rtl,t=g.ne.Ce;e&&t&&(v(e)||(e=[e]),b(e,g.o.l)?V(t,'cc--rtl'):j(t,'cc--rtl'))},Me=()=>{const e=g.ne;if(e.Ce)return;e.Ce=k(c),e.Ce.id='cc-main',e.Ce.setAttribute('data-nosnippet',''),xe();let t=g.o.i.root;t&&y(t)&&(t=document.querySelector(t)),(t||e.Ue.body).appendChild(e.Ce)},De=e=>te((()=>localStorage.removeItem(e))),Te=(e,t)=>{if(t instanceof RegExp)return e.filter((e=>t.test(e)));{const o=m(e,t);return o>-1?[e[o]]:[]}},ke=e=>{const{hostname:t,protocol:o}=location,{name:n,path:a,domain:s,sameSite:c,useLocalStorage:r,secure:i}=g.t.cookie,l=e?(()=>{const e=g.o.S,t=e?new Date-e:0;return 864e5*B()-t})():864e5*B(),d=new Date;d.setTime(d.getTime()+l),g.o.p.expirationTime=d.getTime();const f=JSON.stringify(g.o.p);let _=n+'='+encodeURIComponent(f)+(0!==l?'; expires='+d.toUTCString():'')+'; Path='+a+'; SameSite='+c;b(t,'.')&&(_+='; Domain='+s),i&&'https:'===o&&(_+='; Secure'),r?((e,t)=>{te((()=>localStorage.setItem(e,t)))})(n,f):document.cookie=_,g.o.p},Ee=(e,t,o)=>{if(0===e.length)return;const n=o||g.t.cookie.domain,a=t||g.t.cookie.path,s='www.'===n.slice(0,4),c=s&&n.substring(4),r=(e,t)=>{t&&'.'!==t.slice(0,1)&&(t='.'+t),document.cookie=e+'=; path='+a+(t?'; domain='+t:'')+'; expires=Thu, 01 Jan 1970 00:00:01 GMT;'};for(const t of e)r(t,o),o||r(t,n),s&&r(t,c)},Ae=e=>{const t=e||g.t.cookie.name,o=g.t.cookie.useLocalStorage;return((e,t)=>{let o;return o=te((()=>JSON.parse(t?e:decodeURIComponent(e))),!0)||{},o})(o?(n=t,te((()=>localStorage.getItem(n)))||''):Ne(t,!0),o);var n},Ne=(e,t)=>{const o=document.cookie.match('(^|;)\\\\s*'+e+'\\\\s*=\\\\s*([^;]+)');return o?t?o.pop():e:''},He=e=>{const t=document.cookie.split(/;\\s*/),o=[];for(const n of t){let t=n.split('=')[0];e?te((()=>{e.test(t)&&o.push(t)})):o.push(t)}return o},Ve=(o,n=[])=>{((e,t)=>{const{O:o,R:n,B:a,N:s,Z:c,$:r,X:i}=g.o;let l=[];if(e){v(e)?l.push(...e):y(e)&&(l='all'===e?o:[e]);for(const e of o)c[e]=b(l,e)?w(i[e]):[]}else l=[...n,...r],s&&(l=(()=>{const e=g.ne.ae;if(!e)return[];let t=[];for(let o in e)e[o].checked&&t.push(o);return t})());l=l.filter((e=>!b(o,e)||!b(t,e))),l.push(...a),G(l)})(o,n),(()=>{const e=g.o,{Z:t,B:o,Y:n,X:a,O:s}=e,c=s;e.te=F(n);for(const s of c){const c=a[s],r=w(c),i=t[s]&&t[s].length>0,l=b(o,s);if(0!==r.length){if(n[s]=[],l)n[s].push(...r);else if(i){const e=t[s];n[s].push(...e)}else n[s]=e.Z[s];n[s]=S(n[s])}}})(),(()=>{const o=g.o;o.L=g.t.mode===t&&o.D?$(o.$,o.R):$(o.R,o.p.categories);let n=o.L.length>0,a=!1;for(const e of o.O)o.ee[e]=$(o.Y[e],o.te[e]),o.ee[e].length>0&&(a=!0);const s=g.ne.ae;for(const e in s)s[e].checked=b(o.R,e);for(const e of o.O){const t=g.ne.se[e],n=o.Y[e];for(const e in t)t[e].checked=b(n,e)}o.C||(o.C=new Date),o.M||(o.M=([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)))),o.p={categories:F(o.R),revision:g.t.revision,data:o.h,consentTimestamp:o.C.toISOString(),consentId:o.M,services:F(o.Y),languageCode:g.o.l},o.S&&(o.p.lastConsentTimestamp=o.S.toISOString());let c=!1;const r=n||a;(o.D||r)&&(o.D&&(o.D=!1,c=!0),o.S=o.S?new Date:o.C,o.p.lastConsentTimestamp=o.S.toISOString(),ke(),g.t.autoClearCookies&&(c||r)&&(e=>{const t=g.o,o=He(),n=(e=>{const t=g.o;return(e?t.O:t.L).filter((e=>{const o=t.P[e];return!!o&&!o.readOnly&&!!o.autoClear}))})(e);for(const e in t.ee)for(const n of t.ee[e]){const a=t.X[e][n].cookies;if(!b(t.Y[e],n)&&a)for(const e of a){const t=Te(o,e.name);Ee(t,e.path,e.domain)}}for(const a of n){const n=t.P[a].autoClear,s=n&&n.cookies||[],c=b(t.L,a),r=!b(t.R,a),i=c&&r;if(e?r:i){n.reloadPage&&i&&(t.j=!0);for(const e of s){const t=Te(o,e.name);Ee(t,e.path,e.domain)}}}})(c),oe()),c&&(ee(g.re.ie),ee(g.re.le),g.t.mode===e)||(r&&ee(g.re.de),o.j&&(o.j=!1,location.reload()))})()},Ie=e=>{const t=g.o.D?[]:g.o.R;return b(t,e)},Le=(e,t)=>{const{O:o,X:n}=g.o;if(!(e&&t&&y(t)&&b(o,t)&&0!==w(n[t]).length))return!1;((e,t)=>{const o=g.o,{X:n,Z:a,N:s}=o,c=g.ne.se[t]||{},r=g.ne.ae[t]||{},i=w(n[t]);if(a[t]=[],y(e)){if('all'===e){if(a[t].push(...i),s)for(let e in c)c[e].checked=!0,T(c[e])}else if(b(i,e)&&a[t].push(e),s)for(let t in c)c[t].checked=e===t,T(c[t])}else if(v(e))for(let o of i){const n=b(e,o);n&&a[t].push(o),s&&(c[o].checked=n,T(c[o]))}const l=0===a[t].length;o.R=l?o.R.filter((e=>e!==t)):S([...o.R,t]),s&&(r.checked=!l,T(r))})(e,t),Ve()},je=(e,t)=>{const o=g.o.D?[]:g.o.Y[t]||[];return b(o,e)},Fe=e=>''!==Ne(e,!0),Pe=(e,t,o)=>{let n=[];const a=e=>{if(y(e)){let t=Ne(e);''!==t&&n.push(t)}else n.push(...He(e))};if(v(e))for(let t of e)a(t);else a(e);Ee(n,t,o)},Oe=e=>{const{ne:t,o:n}=g;if(!n.k){if(!n.T){if(!e)return;ye(Ge,Me)}n.k=!0,n.J=x(),n.v&&K(!0),z(t.he,1),V(t.ye,o),E(t.he,i,'false'),setTimeout((()=>{U(g.ne.be)}),100),ee(g.re.fe,_)}},Re=()=>{const{ne:e,o:t,re:n}=g;t.k&&(t.k=!1,t.v&&K(),U(e.Ke,!0),j(e.ye,o),E(e.he,i,'true'),U(t.J),t.J=null,ee(n._e,_))},Be=()=>{const e=g.o;e.A||(e.N||me(Ge,Me),e.A=!0,e.k?e.U=x():e.J=x(),z(g.ne.we,2),V(g.ne.ye,n),E(g.ne.we,i,'false'),setTimeout((()=>{U(g.ne.ve)}),100),ee(g.re.fe,u))},$e=()=>{const e=g.o;e.A&&(e.A=!1,(()=>{const e=We(),t=g.o.P,o=g.ne.ae,n=g.ne.se,a=e=>b(g.o.$,e);for(const s in o){const c=!!t[s].readOnly;o[s].checked=c||(e?Ie(s):a(s));for(const t in n[s])n[s][t].checked=c||(e?je(t,s):a(s))}})(),U(g.ne.$e,!0),j(g.ne.ye,n),E(g.ne.we,i,'true'),e.k?(U(e.U),e.U=null):(U(e.J),e.J=null),ee(g.re._e,u))};var Ge={show:Oe,hide:Re,showPreferences:Be,hidePreferences:$e,acceptCategory:Ve};const Je=async(e,t)=>{if(!he(e))return!1;const o=g.o;return!(e===Ce()&&!0!==t||!await Se(e)||(we(e),o.T&&ye(Ge,Me),o.N&&me(Ge,Me),xe(),0))},Ue=()=>{const{F:e,Y:t}=g.o,{accepted:o,rejected:n}=(()=>{const{D:e,R:t,O:o}=g.o;return{accepted:t,rejected:e?[]:o.filter((e=>!b(t,e)))}})();return F({acceptType:e,acceptedCategories:o,rejectedCategories:n,acceptedServices:t,rejectedServices:P()})},ze=(e,t)=>{let o=document.querySelector('script[src=\"'+e+'\"]');return new Promise((n=>{if(o)return n(!0);if(o=k('script'),h(t))for(const e in t)E(o,e,t[e]);o.onload=()=>n(!0),o.onerror=()=>{o.remove(),n(!1)},o.src=e,H(document.head,o)}))},qe=e=>{let t,o=e.value,n=e.mode,a=!1;const s=g.o;if('update'===n){s.h=t=Ke('data');const e=typeof t==typeof o;if(e&&'object'==typeof t){!t&&(t={});for(let e in o)t[e]!==o[e]&&(t[e]=o[e],a=!0)}else!e&&t||t===o||(t=o,a=!0)}else t=o,a=!0;return a&&(s.h=t,s.p.data=t,ke(!0)),a},Ke=(e,t)=>{const o=Ae(t);return e?o[e]:o},Qe=e=>{const t=g.t,o=g.o.i;return e?t[e]||o[e]:{...t,...o,cookie:{...t.cookie}}},We=()=>!g.o.D,Xe=async e=>{const{o:o,t:n,re:a}=g,c=window;if(!c._ccRun){if(c._ccRun=!0,(e=>{const{ne:o,t:n,o:a}=g,c=n,r=a,{cookie:i}=c,l=g.ce,d=e.cookie,f=e.categories,_=w(f)||[],u=navigator,p=document;o.Ue=p,o.ye=p.documentElement,i.domain=location.hostname,r.i=e,r.P=f,r.O=_,r._=e.language.translations,r.v=!!e.disablePageInteraction,l.ie=e.onFirstConsent,l.le=e.onConsent,l.de=e.onChange,l._e=e.onModalHide,l.fe=e.onModalShow,l.ue=e.onModalReady;const{mode:m,autoShow:v,lazyHtmlGeneration:y,autoClearCookies:C,revision:S,manageScriptTags:x,hideFromBots:M}=e;m===t&&(c.mode=m),'boolean'==typeof C&&(c.autoClearCookies=C),'boolean'==typeof x&&(c.manageScriptTags=x),'number'==typeof S&&S>=0&&(c.revision=S,r.V=!0),'boolean'==typeof v&&(c.autoShow=v),'boolean'==typeof y&&(c.lazyHtmlGeneration=y),!1===M&&(c.hideFromBots=!1),!0===c.hideFromBots&&u&&(r.G=u.userAgent&&/bot|crawl|spider|slurp|teoma/i.test(u.userAgent)||u.webdriver),h(d)&&(c.cookie={...i,...d}),c.autoClearCookies,r.V,c.manageScriptTags,(e=>{const{P:t,X:o,Y:n,Z:a,B:s}=g.o;for(let c of e){const e=t[c],r=e.services||{},i=h(r)&&w(r)||[];o[c]={},n[c]=[],a[c]=[],e.readOnly&&(s.push(c),n[c]=i),g.ne.se[c]={};for(let e of i){const t=r[e];t.Se=!1,o[c][e]=t}}})(_),(()=>{if(!g.t.manageScriptTags)return;const e=g.o,t=D(document,'script['+s+']');for(const o of t){let t=N(o,s),n=o.dataset.service||'',a=!1;if(t&&'!'===t.charAt(0)&&(t=t.slice(1),a=!0),'!'===n.charAt(0)&&(n=n.slice(1),a=!0),b(e.O,t)&&(e.oe.push({Me:o,xe:!1,ke:a,De:t,Te:n}),n)){const o=e.X[t];o[n]||(o[n]={Se:!1})}}})(),we((()=>{const e=g.o.i.language.autoDetect;if(e){const t={browser:navigator.language,document:document.documentElement.lang},o=he(t[e]);if(o)return o}return Ce()})())})(e),o.G)return;(()=>{const e=g.o,o=g.t,n=Ae(),{categories:a,services:s,consentId:c,consentTimestamp:r,lastConsentTimestamp:i,data:l,revision:d}=n,f=v(a);e.p=n,e.M=c;const _=!!c&&y(c);e.C=r,e.C&&(e.C=new Date(r)),e.S=i,e.S&&(e.S=new Date(i)),e.h=void 0!==l?l:null,e.V&&_&&d!==o.revision&&(e.I=!1),e.D=!(_&&e.I&&e.C&&e.S&&f),o.cookie.useLocalStorage&&!e.D&&(e.D=(new Date).getTime()>(n.expirationTime||0),e.D&&De(o.cookie.name)),e.D,(()=>{const e=g.o;for(const o of e.O){const n=e.P[o];if(n.readOnly||n.enabled){e.$.push(o);const n=e.X[o]||{};for(let a in n)e.Z[o].push(a),e.i.mode===t&&e.Y[o].push(a)}}})(),e.D?o.mode===t&&(e.R=[...e.$]):(e.Y={...e.Y,...s},e.Z={...e.Y},G([...e.B,...a]))})();const i=We();if(!await Se())return!1;if(J(null,r=Ge,me,Me),g.o.D&&ye(r,Me),g.t.lazyHtmlGeneration||me(r,Me),n.autoShow&&!i&&Oe(!0),i)return oe(),ee(a.le);n.mode===t&&oe(o.$)}var r},Ye=e=>{const{Ce:t,ye:s}=g.ne,{name:c,path:r,domain:i,useLocalStorage:l}=g.t.cookie;e&&(l?De(c):Pe(c,r,i));for(const{pe:e,ge:t,me:o}of g.o.m)e.removeEventListener(t,o);t&&t.remove(),s&&s.classList.remove(a,n,o);const d=new p;for(const e in g)g[e]=d[e];window._ccRun=!1};export{Ve as acceptCategory,Le as acceptService,Ie as acceptedCategory,je as acceptedService,Pe as eraseCookies,Qe as getConfig,Ke as getCookie,Ue as getUserPreferences,Re as hide,$e as hidePreferences,ze as loadScript,Ye as reset,Xe as run,qe as setCookieData,Je as setLanguage,Oe as show,Be as showPreferences,We as validConsent,Fe as validCookie};\n"], "mappings": ";AAMA,IAAM,IAAE;AAAR,IAAiB,IAAE;AAAnB,IAA6B,IAAE;AAA/B,IAA+C,IAAE;AAAjD,IAAqE,IAAE;AAAvE,IAA8F,IAAE;AAAhG,IAAgH,IAAE;AAAlH,IAAwH,IAAE;AAA1H,IAAmI,IAAE;AAArI,IAAmJ,IAAE;AAArJ,IAAiK,IAAE;AAAnK,IAA2K,IAAE;AAA7K,IAAyL,IAAE;AAA3L,IAA0M,IAAE;AAAmB,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,IAAE,EAAC,MAAK,GAAE,UAAS,GAAE,UAAS,MAAG,oBAAmB,MAAG,kBAAiB,MAAG,kBAAiB,MAAG,cAAa,MAAG,QAAO,EAAC,MAAK,aAAY,kBAAiB,KAAI,QAAO,IAAG,MAAK,KAAI,QAAO,MAAG,UAAS,MAAK,EAAC,GAAE,KAAK,IAAE,EAAC,GAAE,CAAC,GAAE,GAAE,IAAG,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,OAAG,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,IAAG,GAAE,MAAG,GAAE,OAAG,GAAE,OAAG,GAAE,OAAG,GAAE,OAAG,GAAE,CAAC,GAAE,GAAE,OAAG,GAAE,MAAG,GAAE,CAAC,GAAE,GAAE,OAAG,GAAE,IAAG,GAAE,OAAG,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,OAAG,GAAE,OAAG,GAAE,OAAG,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,EAAC,GAAE,KAAK,KAAG,EAAC,IAAG,CAAC,GAAE,IAAG,CAAC,EAAC,GAAE,KAAK,KAAG,CAAC,GAAE,KAAK,KAAG,EAAC,IAAG,qBAAoB,IAAG,gBAAe,IAAG,eAAc,IAAG,kBAAiB,IAAG,kBAAiB,IAAG,kBAAiB;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,IAAI;AAAZ,IAAc,IAAE,CAACA,IAAEC,OAAID,GAAE,QAAQC,EAAC;AAAlC,IAAoC,IAAE,CAACD,IAAEC,OAAI,OAAK,EAAED,IAAEC,EAAC;AAAvD,IAAyD,IAAE,CAAAD,OAAG,MAAM,QAAQA,EAAC;AAA7E,IAA+E,IAAE,CAAAA,OAAG,YAAU,OAAOA;AAArG,IAAuG,IAAE,CAAAA,OAAG,CAAC,CAACA,MAAG,YAAU,OAAOA,MAAG,CAAC,EAAEA,EAAC;AAAzI,IAA2I,IAAE,CAAAA,OAAG,cAAY,OAAOA;AAAnK,IAAqK,IAAE,CAAAA,OAAG,OAAO,KAAKA,EAAC;AAAvL,IAAyL,IAAE,CAAAA,OAAG,MAAM,KAAK,IAAI,IAAIA,EAAC,CAAC;AAAnN,IAAqN,IAAE,MAAI,SAAS;AAApO,IAAkP,IAAE,CAAAA,OAAGA,GAAE,eAAe;AAAxQ,IAA0Q,IAAE,CAACA,IAAEC,OAAID,GAAE,iBAAiBC,EAAC;AAAvS,IAAyS,IAAE,CAAAD,OAAGA,GAAE,cAAc,IAAI,MAAM,QAAQ,CAAC;AAAjV,IAAmV,IAAE,CAAAA,OAAG;AAAC,QAAMC,KAAE,SAAS,cAAcD,EAAC;AAAE,SAAOA,OAAI,MAAIC,GAAE,OAAKD,KAAGC;AAAC;AAArZ,IAAuZ,IAAE,CAACD,IAAEC,IAAEC,OAAIF,GAAE,aAAaC,IAAEC,EAAC;AAApb,IAAsb,IAAE,CAACF,IAAEC,IAAEC,OAAI;AAAC,EAAAF,GAAE,gBAAgBE,KAAE,UAAQD,KAAEA,EAAC;AAAC;AAAle,IAAoe,IAAE,CAACD,IAAEC,IAAEC,OAAIF,GAAE,aAAaE,KAAE,UAAQD,KAAEA,EAAC;AAA3gB,IAA6gB,IAAE,CAACD,IAAEC,OAAID,GAAE,YAAYC,EAAC;AAAriB,IAAuiB,IAAE,CAACD,IAAEC,OAAID,GAAE,UAAU,IAAIC,EAAC;AAAjkB,IAAmkB,IAAE,CAACD,IAAEC,OAAI,EAAED,IAAE,SAAOC,EAAC;AAAxlB,IAA0lB,IAAE,CAACD,IAAEC,OAAI,EAAED,IAAE,SAAOC,EAAC;AAA/mB,IAAinB,IAAE,CAACD,IAAEC,OAAID,GAAE,UAAU,OAAOC,EAAC;AAA9oB,IAAgpB,IAAE,CAAAD,OAAG;AAAC,MAAG,YAAU,OAAOA;AAAE,WAAOA;AAAE,MAAGA,cAAa;AAAK,WAAO,IAAI,KAAKA,GAAE,QAAQ,CAAC;AAAE,MAAIC,KAAE,MAAM,QAAQD,EAAC,IAAE,CAAC,IAAE,CAAC;AAAE,WAAQE,MAAKF,IAAE;AAAC,QAAIG,KAAEH,GAAEE,EAAC;AAAE,IAAAD,GAAEC,EAAC,IAAE,EAAEC,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAjzB,IAAmzB,IAAE,MAAI;AAAC,QAAMD,KAAE,CAAC,GAAE,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,aAAUC,MAAKH;AAAE,IAAAD,GAAEI,EAAC,IAAE,EAAED,GAAEC,EAAC,GAAE,EAAEF,GAAEE,EAAC,CAAC,CAAC;AAAE,SAAOJ;AAAC;AAAr4B,IAAu4B,IAAE,CAACA,IAAEC,OAAI,cAAc,IAAI,YAAYD,IAAE,EAAC,QAAOC,GAAC,CAAC,CAAC;AAA37B,IAA67B,IAAE,CAACD,IAAEC,IAAEC,IAAEC,OAAI;AAAC,EAAAH,GAAE,iBAAiBC,IAAEC,EAAC,GAAEC,MAAG,EAAE,EAAE,EAAE,KAAK,EAAC,IAAGH,IAAE,IAAGC,IAAE,IAAGC,GAAC,CAAC;AAAC;AAAlgC,IAAogC,IAAE,MAAI;AAAC,QAAMF,KAAE,EAAE,EAAE,OAAO;AAAiB,SAAO,EAAEA,EAAC,IAAEA,GAAE,EAAE,EAAE,CAAC,IAAEA;AAAC;AAArkC,IAAukC,IAAE,CAACA,IAAEC,OAAI;AAAC,QAAMC,KAAEF,MAAG,CAAC,GAAEG,KAAEF,MAAG,CAAC;AAAE,SAAOC,GAAE,OAAQ,CAAAF,OAAG,CAAC,EAAEG,IAAEH,EAAC,CAAE,EAAE,OAAOG,GAAE,OAAQ,CAAAH,OAAG,CAAC,EAAEE,IAAEF,EAAC,CAAE,CAAC;AAAC;AAAnqC,IAAqqC,IAAE,CAAAA,OAAG;AAAC,IAAE,EAAE,IAAE,EAAEA,EAAC,GAAE,EAAE,EAAE,KAAG,MAAI;AAAC,QAAIA,KAAE;AAAS,UAAK,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE,GAAEC,KAAEH,GAAE;AAAO,WAAOG,OAAIF,GAAE,SAAOF,KAAE,QAAMI,OAAID,GAAE,WAASH,KAAE,cAAaA;AAAA,EAAC,GAAG;AAAC;AAAlzC,IAAozC,IAAE,CAACA,IAAEC,IAAEC,IAAEC,OAAI;AAAC,QAAMC,KAAE,WAAU,EAAC,MAAKC,IAAE,iBAAgBC,IAAE,MAAKC,IAAE,iBAAgBC,IAAE,gBAAeC,GAAC,IAAER,IAAES,KAAEV,MAAG,UAASW,KAAE,CAAAX,OAAG,EAAEU,IAAE,aAAaV,EAAC,IAAI,GAAEY,KAAE,CAACZ,IAAEC,OAAI;AAAC,MAAED,EAAC,GAAES,GAAER,EAAC,GAAEO,GAAE,GAAED,GAAE;AAAA,EAAC,GAAEM,KAAEF,GAAE,uBAAuB,GAAEG,KAAEH,GAAE,mBAAmB,GAAEI,KAAEJ,GAAEP,KAAE,KAAK,GAAEY,KAAEL,GAAEP,KAAE,WAAW,GAAEa,KAAEN,GAAEP,KAAE,QAAQ,GAAEc,KAAE,EAAE,EAAE;AAAmB,aAAUlB,MAAKa;AAAE,MAAEb,IAAE,iBAAgB,QAAQ,GAAE,EAAEA,IAAE,GAAG,CAAAA,OAAG;AAAC,QAAEA,EAAC,GAAEM,GAAE;AAAA,IAAC,CAAE,GAAEY,OAAI,EAAElB,IAAE,cAAc,CAAAA,OAAG;AAAC,QAAEA,EAAC,GAAE,EAAE,EAAE,KAAGE,GAAED,IAAEE,EAAC;AAAA,IAAC,GAAG,IAAE,GAAE,EAAEH,IAAE,SAAS,MAAI;AAAC,QAAE,EAAE,KAAGE,GAAED,IAAEE,EAAC;AAAA,IAAC,CAAE;AAAG,WAAQH,MAAKc;AAAE,MAAEd,IAAE,iBAAgB,QAAQ,GAAE,EAAEA,IAAE,GAAG,CAAAA,OAAG;AAAC,QAAEA,EAAC,GAAEK,GAAE,IAAE;AAAA,IAAC,GAAG,IAAE;AAAE,WAAQL,MAAKe;AAAE,MAAEf,IAAE,GAAG,CAAAA,OAAG;AAAC,MAAAY,GAAEZ,IAAE,KAAK;AAAA,IAAC,GAAG,IAAE;AAAE,WAAQA,MAAKiB;AAAE,MAAEjB,IAAE,GAAG,CAAAA,OAAG;AAAC,MAAAY,GAAEZ,EAAC;AAAA,IAAC,GAAG,IAAE;AAAE,WAAQA,MAAKgB;AAAE,MAAEhB,IAAE,GAAG,CAAAA,OAAG;AAAC,MAAAY,GAAEZ,IAAE,CAAC,CAAC;AAAA,IAAC,GAAG,IAAE;AAAC;AAA/7D,IAAi8D,IAAE,CAACA,IAAEC,OAAI;AAAC,EAAAD,OAAIC,OAAID,GAAE,WAAS,KAAIA,GAAE,MAAM,GAAEC,MAAGD,GAAE,gBAAgB,UAAU;AAAE;AAA7gE,IAA+gE,IAAE,CAACA,IAAEC,OAAI;AAAC,QAAMC,KAAE,CAAAC,OAAG;AAAC,IAAAA,GAAE,OAAO,oBAAoB,iBAAgBD,EAAC,GAAE,cAAYC,GAAE,gBAAc,QAAM,iBAAiBH,EAAC,EAAE,WAAS,GAAG,CAAAA,OAAG,MAAIA,KAAE,EAAE,GAAG,KAAG,EAAE,GAAG,IAAIC,EAAC,CAAC;AAAA,EAAC;AAAE,IAAED,IAAE,iBAAgBE,EAAC;AAAC;AAAE,IAAI;AAAE,IAAM,IAAE,CAAAF,OAAG;AAAC,eAAa,CAAC,GAAEA,KAAE,EAAE,EAAE,GAAG,IAAG,CAAC,IAAE,IAAE,WAAY,MAAI;AAAC,MAAE,EAAE,GAAG,IAAG,CAAC;AAAA,EAAC,GAAG,GAAG;AAAC;AAAjF,IAAmF,IAAE,CAAC,iDAAgD,gDAA+C,4CAA4C;AAAjO,IAAmO,IAAE,CAACA,KAAE,GAAEC,KAAE,QAAM,0CAA0CA,EAAC,cAAc,EAAED,EAAC,CAAC;AAA/S,IAA2T,IAAE,CAAAA,OAAG;AAAC,QAAMC,KAAE,EAAE,IAAGC,KAAE,EAAE;AAAE,GAAC,CAAAF,OAAG;AAAC,UAAMG,KAAEH,OAAIC,GAAE,IAAGG,KAAEF,GAAE,EAAE,yBAAuBD,GAAE,KAAGE,KAAEF,GAAE,KAAGA,GAAE;AAAG,MAAEG,IAAE,WAAW,CAAAH,OAAG;AAAC,UAAG,UAAQA,GAAE,OAAK,EAAEE,KAAED,GAAE,KAAG,CAACA,GAAE,IAAEA,GAAE;AAAG;AAAO,YAAME,KAAE,EAAE,GAAEC,KAAEF,KAAED,GAAE,IAAEA,GAAE;AAAE,YAAIG,GAAE,WAASJ,GAAE,WAASG,OAAIC,GAAE,CAAC,KAAGL,GAAE,SAASI,EAAC,MAAI,EAAEH,EAAC,GAAE,EAAEI,GAAE,CAAC,CAAC,KAAGD,OAAIC,GAAE,CAAC,KAAGL,GAAE,SAASI,EAAC,MAAI,EAAEH,EAAC,GAAE,EAAEI,GAAE,CAAC,CAAC;AAAA,IAAG,GAAG,IAAE;AAAA,EAAC,GAAGL,EAAC;AAAC;AAApmB,IAAsmB,IAAE,CAAC,UAAS,GAAE,SAAQ,WAAU,YAAY,EAAE,IAAK,CAAAA,OAAGA,KAAE,uBAAwB,EAAE,KAAK,GAAG;AAAhsB,IAAksB,IAAE,CAAAA,OAAG;AAAC,QAAK,EAAC,GAAEC,IAAE,IAAGC,GAAC,IAAE,GAAEC,KAAE,CAACH,IAAEC,OAAI;AAAC,UAAMC,KAAE,EAAEF,IAAE,CAAC;AAAE,IAAAC,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAED,GAAE,CAAC,IAAEC,GAAEA,GAAE,SAAO,CAAC;AAAA,EAAC;AAAE,QAAIF,MAAGC,GAAE,KAAGE,GAAED,GAAE,IAAGD,GAAE,CAAC,GAAE,MAAID,MAAGC,GAAE,KAAGE,GAAED,GAAE,IAAGD,GAAE,CAAC;AAAC;AAAh0B,IAAk0B,KAAG,CAACD,IAAEC,IAAEC,OAAI;AAAC,QAAK,EAAC,IAAGC,IAAE,IAAGC,IAAE,IAAGC,IAAE,IAAGC,IAAE,IAAGC,IAAE,IAAGC,GAAC,IAAE,EAAE,IAAGC,KAAE,EAAE;AAAG,MAAGR,IAAE;AAAC,UAAME,KAAE,EAAC,WAAUF,GAAC;AAAE,WAAOD,OAAIS,GAAE,KAAG,EAAED,EAAC,KAAGA,GAAEL,EAAC,IAAEH,OAAIS,GAAE,KAAG,EAAEH,EAAC,KAAGA,GAAEH,EAAC,KAAGA,GAAE,QAAMD,IAAE,EAAEK,EAAC,KAAGA,GAAEJ,EAAC,IAAG,EAAEH,IAAEG,EAAC;AAAA,EAAC;AAAC,QAAMgB,KAAE,EAAC,QAAO,EAAE,EAAE,EAAC;AAAE,EAAAnB,OAAIS,GAAE,KAAG,EAAEJ,EAAC,KAAGA,GAAE,EAAEc,EAAC,CAAC,IAAEnB,OAAIS,GAAE,KAAG,EAAEL,EAAC,KAAGA,GAAE,EAAEe,EAAC,CAAC,KAAGA,GAAE,oBAAkB,EAAE,EAAE,GAAEA,GAAE,kBAAgB,EAAE,EAAE,IAAG,EAAEhB,EAAC,KAAGA,GAAE,EAAEgB,EAAC,CAAC,IAAG,EAAEnB,IAAE,EAAEmB,EAAC,CAAC;AAAC;AAA1nC,IAA4nC,KAAG,CAACnB,IAAEC,OAAI;AAAC,MAAG;AAAC,WAAOD,GAAE;AAAA,EAAC,SAAOA,IAAE;AAAC,WAAM,CAACC,MAAG,QAAQ,KAAK,kBAAiBD,EAAC,GAAE;AAAA,EAAE;AAAC;AAA7sC,IAA+sC,KAAG,CAAAA,OAAG;AAAC,QAAK,EAAC,GAAEC,IAAE,IAAGC,IAAE,GAAEC,IAAE,GAAEC,IAAE,IAAGE,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,aAAUR,MAAKG,IAAE;AAAC,UAAMA,KAAED,GAAEF,EAAC,KAAGC,GAAED,EAAC,KAAG,CAAC;AAAE,eAAUE,MAAKC,IAAE;AAAC,YAAMA,KAAEC,GAAEJ,EAAC,EAAEE,EAAC;AAAE,UAAG,CAACC;AAAE;AAAS,YAAK,EAAC,UAASE,IAAE,UAASC,GAAC,IAAEH;AAAE,OAACA,GAAE,MAAI,EAAEF,GAAED,EAAC,GAAEE,EAAC,KAAGC,GAAE,KAAG,MAAG,EAAEE,EAAC,KAAGA,GAAE,KAAGF,GAAE,MAAI,CAAC,EAAEF,GAAED,EAAC,GAAEE,EAAC,MAAIC,GAAE,KAAG,OAAG,EAAEG,EAAC,KAAGA,GAAE;AAAA,IAAE;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,EAAE;AAAiB;AAAO,QAAMG,KAAEH,IAAEa,KAAEnB,MAAGO,GAAE,cAAY,CAAC,GAAEG,KAAE,CAACV,IAAEG,OAAI;AAAC,QAAGA,MAAGH,GAAE;AAAO;AAAO,UAAMI,KAAEE,GAAEH,EAAC;AAAE,QAAGC,GAAE;AAAG,aAAOM,GAAEV,IAAEG,KAAE,CAAC;AAAE,UAAMI,KAAEH,GAAE,IAAGK,KAAEL,GAAE,IAAGO,KAAEP,GAAE,IAAGQ,KAAE,EAAEO,IAAEV,EAAC,GAAEI,KAAE,CAAC,CAACF,MAAG,EAAEV,GAAEQ,EAAC,GAAEE,EAAC;AAAE,QAAG,CAACA,MAAG,CAACP,GAAE,MAAIQ,MAAG,CAACD,MAAGP,GAAE,MAAI,CAACQ,MAAG,EAAEJ,IAAEC,EAAC,KAAGE,MAAG,CAACP,GAAE,MAAIS,MAAGF,MAAGP,GAAE,MAAI,CAACS,MAAG,EAAEX,GAAEO,EAAC,KAAG,CAAC,GAAEE,EAAC,GAAE;AAAC,MAAAP,GAAE,KAAG;AAAG,YAAMH,KAAE,EAAEM,IAAE,QAAO,IAAE;AAAE,QAAEA,IAAE,QAAO,CAAC,CAACN,EAAC,GAAE,EAAEM,IAAE,CAAC;AAAE,UAAIL,KAAE,EAAEK,IAAE,OAAM,IAAE;AAAE,MAAAL,MAAG,EAAEK,IAAE,OAAM,IAAE;AAAE,YAAMD,KAAE,EAAE,QAAQ;AAAE,MAAAA,GAAE,cAAYC,GAAE;AAAU,iBAAS,EAAC,UAASP,GAAC,KAAIO,GAAE;AAAW,UAAED,IAAEN,IAAEO,GAAEP,EAAC,KAAG,EAAEO,IAAEP,EAAC,CAAC;AAAE,MAAAC,OAAIK,GAAE,OAAKL,KAAGC,KAAEI,GAAE,MAAIJ,KAAEA,KAAEK,GAAE;AAAI,YAAMC,KAAE,CAAC,CAACN,OAAI,CAACD,MAAG,CAAC,mBAAkB,QAAQ,EAAE,SAASA,EAAC;AAAG,UAAGO,OAAIF,GAAE,SAAOA,GAAE,UAAQ,MAAI;AAAC,QAAAI,GAAEV,IAAE,EAAEG,EAAC;AAAA,MAAC,IAAGI,GAAE,YAAYD,EAAC,GAAEE;AAAE;AAAA,IAAM;AAAC,IAAAE,GAAEV,IAAE,EAAEG,EAAC;AAAA,EAAC;AAAE,EAAAO,GAAED,IAAE,CAAC;AAAC;AAAhkE,IAAkkE,KAAG;AAArkE,IAA8kE,KAAG;AAAjlE,IAAwlE,KAAG;AAA3lE,IAAomE,KAAG;AAAvmE,IAA+mE,KAAG;AAAlnE,IAA2nE,KAAG;AAA9nE,IAAqoE,KAAG;AAAxoE,IAA+oE,KAAG,CAAC,UAAS,OAAM,EAAE;AAApqE,IAAsqE,KAAG,CAAC,IAAG,IAAG,EAAE;AAAlrE,IAAorE,KAAG,EAAC,KAAI,EAAC,IAAG,CAAC,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,GAAE,OAAM,EAAC,IAAG,CAAC,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,GAAE,KAAI,EAAC,IAAG,CAAC,EAAE,GAAE,IAAG,GAAG,MAAM,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,GAAE,EAAC;AAAvzE,IAAyzE,KAAG,EAAC,KAAI,EAAC,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,GAAE,GAAE,KAAI,EAAC,IAAG,CAAC,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,GAAE,EAAC;AAA34E,IAA64E,KAAG,CAAAT,OAAG;AAAC,QAAMC,KAAE,EAAE,EAAE,EAAE,YAAWC,KAAED,MAAGA,GAAE,cAAaE,KAAEF,MAAGA,GAAE;AAAiB,QAAID,MAAG,GAAG,EAAE,GAAG,IAAG,IAAGE,IAAE,QAAO,OAAM,IAAI,GAAE,MAAIF,MAAG,GAAG,EAAE,GAAG,IAAG,IAAGG,IAAE,IAAG,OAAM,IAAI;AAAC;AAAxiF,IAA0iF,KAAG,CAACH,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,OAAI;AAAC,EAAAL,GAAE,YAAUK;AAAE,QAAMC,KAAEJ,MAAGA,GAAE,QAAOK,KAAEL,MAAGA,GAAE,UAASM,KAAEN,MAAGA,GAAE,aAAYO,KAAE,CAACP,MAAG,UAAKA,GAAE,oBAAmBiB,KAAEb,MAAGA,GAAE,MAAM,GAAG,KAAG,CAAC,GAAEI,KAAES,GAAE,CAAC,GAAER,KAAEQ,GAAE,CAAC,GAAEP,KAAEF,MAAKT,KAAES,KAAEN,IAAES,KAAEZ,GAAEW,EAAC,GAAEE,KAAE,EAAED,GAAE,IAAGF,EAAC,KAAGA,IAAEK,KAAET,MAAGA,GAAE,MAAM,GAAG,KAAG,CAAC,GAAEU,KAAED,GAAE,CAAC,GAAEE,KAAEf,OAAI,KAAGa,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEI,KAAE,EAAEP,GAAE,IAAGI,EAAC,IAAEA,KAAEJ,GAAE,IAAGQ,KAAE,EAAER,GAAE,IAAGK,EAAC,IAAEA,KAAEL,GAAE,IAAGS,KAAE,CAAArB,OAAG;AAAC,IAAAA,MAAG,EAAED,IAAEG,KAAEF,EAAC;AAAA,EAAC;AAAE,EAAAqB,GAAEV,EAAC,GAAEU,GAAER,EAAC,GAAEQ,GAAEF,EAAC,GAAEE,GAAED,EAAC,GAAEb,MAAGc,GAAE,MAAM;AAAE,QAAMC,KAAElB,KAAE;AAAmB,MAAG,SAAOA,IAAE;AAAC,UAAK,EAAC,IAAGL,IAAE,IAAGC,GAAC,IAAE,EAAE;AAAG,IAAAD,OAAIS,KAAE,EAAET,IAAEuB,EAAC,IAAE,EAAEvB,IAAEuB,EAAC,IAAGtB,OAAIQ,KAAE,EAAER,IAAEsB,EAAC,IAAE,EAAEtB,IAAEsB,EAAC;AAAA,EAAE,OAAK;AAAC,UAAK,EAAC,IAAGvB,GAAC,IAAE,EAAE;AAAG,IAAAA,OAAIS,KAAE,EAAET,IAAEuB,EAAC,IAAE,EAAEvB,IAAEuB,EAAC;AAAA,EAAE;AAAC;AAAxgG,IAA0gG,KAAG,CAACvB,IAAEC,OAAI;AAAC,QAAMC,KAAE,EAAE,GAAEC,KAAE,EAAE,IAAG,EAAC,MAAKC,IAAE,iBAAgBC,IAAE,gBAAeM,GAAC,IAAEX,IAAEa,KAAE,CAAAb,OAAG;AAAC,IAAAW,GAAEX,EAAC,GAAEK,GAAE,GAAED,GAAE;AAAA,EAAC,GAAEU,KAAEZ,GAAE,KAAGA,GAAE,EAAE;AAAiB,MAAG,CAACY;AAAE;AAAO,QAAMC,KAAED,GAAE,OAAME,KAAEF,GAAE,gBAAeM,KAAEN,GAAE,cAAaQ,KAAER,GAAE,oBAAmBS,KAAET,GAAE,oBAAmBU,KAAEV,GAAE,YAAU,CAAC,GAAEW,KAAEL,MAAGE,MAAGC;AAAE,MAAGpB,GAAE;AAAG,IAAAA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,MAAM;AAAA,OAAM;AAAC,IAAAA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,YAAY;AAAE,UAAMH,KAAE,EAAE,KAAK;AAAE,MAAEA,IAAE,YAAY,GAAE,EAAEG,GAAE,IAAGH,EAAC,GAAE,EAAEA,IAAE,GAAEK,EAAC,GAAEF,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,IAAI,GAAE,EAAEA,GAAE,IAAG,QAAO,QAAQ,GAAE,EAAEA,GAAE,IAAG,GAAE,IAAE,GAAE,EAAEA,GAAE,IAAG,cAAa,IAAE,GAAE,EAAEA,GAAE,IAAG,mBAAkB,WAAW,GAAE,EAAEA,GAAE,IAAG,WAAW,CAAAH,OAAG;AAAC,aAAKA,GAAE,WAASK,GAAE;AAAA,IAAC,GAAG,IAAE,GAAEF,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,QAAQ,GAAEA,GAAE,KAAG,EAAE,IAAI,GAAE,EAAEA,GAAE,IAAG,OAAO,GAAEA,GAAE,GAAG,KAAG,aAAYA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,WAAW,GAAE,EAAEA,GAAE,IAAG,cAAaW,GAAE,kBAAgB,EAAE,GAAE,EAAEX,GAAE,IAAG,GAAEE,EAAC,GAAEF,GAAE,KAAG,EAAE,MAAM,GAAEA,GAAE,GAAG,YAAU,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,MAAM,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,QAAQ;AAAE,QAAIuB,KAAE,EAAE,CAAC;AAAE,MAAEA,IAAE,MAAM;AAAE,QAAIC,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,CAAC;AAAE,MAAED,IAAE,CAAC,GAAE,EAAEC,IAAE,CAAC,GAAE,EAAEzB,GAAE,IAAGwB,EAAC,GAAE,EAAExB,GAAE,IAAGyB,EAAC,GAAE,EAAEzB,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,YAAW,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAEsB,MAAG,EAAEtB,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE;AAAA,EAAC;AAAC,MAAI0B;AAAE,EAAAd,OAAIZ,GAAE,GAAG,YAAUY,IAAEC,MAAG,EAAEb,GAAE,IAAG,cAAaa,EAAC,IAAGQ,GAAE,QAAS,CAACxB,IAAEC,OAAI;AAAC,UAAMG,KAAEJ,GAAE,OAAMK,KAAEL,GAAE,aAAYS,KAAET,GAAE,gBAAeU,KAAED,MAAGP,GAAE,EAAEO,EAAC,GAAEE,KAAEX,GAAE,aAAYY,KAAED,MAAGA,GAAE,MAAKE,KAAEF,MAAGA,GAAE,SAAQmB,KAAElB,MAAGA,GAAE,SAAO,GAAEG,KAAE,CAAC,CAACL,IAAEM,KAAED,MAAGb,GAAE,EAAEO,EAAC,GAAEW,KAAE,EAAEJ,EAAC,KAAG,EAAEA,EAAC,KAAG,CAAC,GAAEM,KAAEP,OAAI,CAAC,CAACV,MAAG,CAAC,CAACyB,MAAG,EAAEd,EAAC,EAAE,SAAO;AAAG,QAAIO,KAAE,EAAE,CAAC;AAAE,QAAG,EAAEA,IAAE,SAAS,GAAED,MAAGjB,IAAE;AAAC,UAAImB,KAAE,EAAE,CAAC;AAAE,QAAEA,IAAE,sBAAsB;AAAA,IAAC;AAAC,QAAIC,KAAEL,GAAE;AAAO,QAAGE,MAAGG,KAAE,GAAE;AAAC,YAAMzB,KAAE,EAAE,CAAC;AAAE,QAAEA,IAAE,kBAAkB;AAAE,iBAAUC,MAAKmB,IAAE;AAAC,cAAMlB,KAAEc,GAAEf,EAAC,GAAEE,KAAED,MAAGA,GAAE,SAAOD,IAAEG,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,CAAC,GAAEE,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,CAAC;AAAE,UAAEJ,IAAE,SAAS,GAAE,EAAEI,IAAE,eAAe,GAAE,EAAEH,IAAE,gBAAgB,GAAE,EAAEE,IAAE,cAAc;AAAE,cAAMY,KAAE,GAAGhB,IAAEF,IAAES,IAAE,MAAGD,EAAC;AAAE,QAAAD,GAAE,YAAUL,IAAE,EAAEE,IAAEE,EAAC,GAAE,EAAEF,IAAEG,EAAC,GAAE,EAAEJ,IAAEC,EAAC,GAAE,EAAED,IAAEe,EAAC,GAAE,EAAEnB,IAAEI,EAAC;AAAA,MAAC;AAAC,QAAEoB,IAAExB,EAAC;AAAA,IAAC;AAAC,QAAGI,IAAE;AAAC,UAAIsB,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAEZ,KAAE,IAAE,CAAC;AAAE,UAAG,EAAEW,IAAE,uBAAuB,GAAE,EAAEC,IAAE,eAAe,GAAEA,GAAE,YAAUvB,IAAE,EAAEsB,IAAEC,EAAC,GAAEZ,IAAE;AAAC,cAAMf,KAAE,EAAE,MAAM;AAAE,QAAAA,GAAE,YAAU,EAAE,GAAE,GAAG,GAAE,EAAEA,IAAE,eAAe,GAAE,EAAE0B,IAAE1B,EAAC,GAAEuB,GAAE,aAAW;AAAW,cAAMtB,KAAE,GAAGG,IAAEK,IAAEC,EAAC;AAAE,YAAIR,KAAEY,GAAE;AAAoB,YAAGW,KAAE,KAAG,EAAEvB,EAAC,GAAE;AAAC,cAAIF,KAAE,EAAE,MAAM;AAAE,YAAEA,IAAE,OAAO,GAAE,EAAEA,IAAE,iBAAiB,GAAE,EAAEA,IAAE,GAAE,IAAE,GAAE,EAAEA,IAAE,uBAAsByB,EAAC,GAAEvB,OAAIA,KAAEA,GAAE,MAAM,GAAG,GAAEA,KAAEA,GAAE,SAAO,KAAGuB,KAAE,IAAEvB,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAEF,IAAE,qBAAoBE,EAAC,IAAGF,GAAE,YAAUyB,MAAGvB,KAAE,MAAIA,KAAE,KAAI,EAAEyB,IAAE3B,EAAC;AAAA,QAAC;AAAC,YAAGsB,IAAE;AAAC,YAAEC,IAAE,qBAAqB;AAAE,cAAIK,KAAEnB,KAAE;AAAQ,YAAEkB,IAAE,iBAAgB,KAAE,GAAE,EAAEA,IAAE,iBAAgBC,EAAC;AAAA,QAAC;AAAC,UAAEF,IAAEzB,EAAC;AAAA,MAAC;AAAM,UAAE0B,IAAE,QAAO,SAAS,GAAE,EAAEA,IAAE,cAAa,GAAG;AAAE,QAAEJ,IAAEG,EAAC;AAAA,IAAC;AAAC,QAAGrB,IAAE;AAAC,UAAI0B,KAAE,EAAE,GAAG;AAAE,QAAEA,IAAE,cAAc,GAAEA,GAAE,YAAU1B,IAAE,EAAEmB,IAAEO,EAAC;AAAA,IAAC;AAAC,QAAGT,OAAI,EAAEE,IAAE,GAAE,MAAM,GAAEA,GAAE,KAAGI,KAAG,CAAC5B,IAAEC,IAAEC,OAAI;AAAC,QAAEyB,IAAE,GAAG,MAAI;AAAC,QAAA1B,GAAE,UAAU,SAAS,aAAa,KAAG,EAAEA,IAAE,aAAa,GAAE,EAAEC,IAAE,iBAAgB,OAAO,GAAE,EAAEF,IAAE,GAAE,MAAM,MAAI,EAAEC,IAAE,aAAa,GAAE,EAAEC,IAAE,iBAAgB,MAAM,GAAE,EAAEF,IAAE,GAAE,OAAO;AAAA,MAAE,CAAE;AAAA,IAAC,GAAGwB,IAAED,IAAEI,EAAC,GAAEG,KAAG;AAAC,YAAM9B,KAAE,EAAE,OAAO,GAAEE,KAAE,EAAE,OAAO,GAAEE,KAAE,EAAE,OAAO;AAAE,UAAGS,IAAE;AAAC,cAAMZ,KAAE,EAAE,SAAS;AAAE,UAAEA,IAAE,eAAe,GAAEA,GAAE,YAAUY,IAAEb,GAAE,YAAYC,EAAC;AAAA,MAAC;AAAC,QAAED,IAAE,eAAe,GAAE,EAAEE,IAAE,YAAY,GAAE,EAAEE,IAAE,YAAY;AAAE,YAAMC,KAAEM,GAAE,SAAQJ,KAAE,EAAEF,EAAC,GAAEG,KAAEL,GAAE,GAAG,uBAAuB,GAAEM,KAAE,EAAE,IAAI;AAAE,iBAAUT,MAAKO,IAAE;AAAC,cAAML,KAAEG,GAAEL,EAAC,GAAEG,KAAE,EAAE,IAAI;AAAE,QAAAA,GAAE,KAAG,aAAWD,KAAED,IAAE,EAAEE,IAAE,SAAQ,KAAK,GAAE,EAAEA,IAAE,UAAU,GAAEA,GAAE,YAAUD,IAAE,EAAEM,IAAEL,EAAC;AAAA,MAAC;AAAC,QAAEM,IAAED,EAAC,GAAE,EAAEN,IAAEO,EAAC;AAAE,YAAMU,KAAEhB,GAAE,GAAG,uBAAuB;AAAE,iBAAUH,MAAKY,IAAE;AAAC,cAAMV,KAAE,EAAE,IAAI;AAAE,UAAEA,IAAE,UAAU;AAAE,mBAAUC,MAAKI,IAAE;AAAC,gBAAMH,KAAEC,GAAEF,EAAC,GAAEI,KAAEP,GAAEG,EAAC,GAAEK,KAAE,EAAE,IAAI,GAAEC,KAAE,EAAE,CAAC;AAAE,YAAED,IAAE,UAAU,GAAE,EAAEA,IAAE,eAAcJ,EAAC,GAAE,EAAEI,IAAE,WAAU,aAAWJ,KAAEH,EAAC,GAAEQ,GAAE,mBAAmB,aAAYF,EAAC,GAAE,EAAEC,IAAEC,EAAC,GAAE,EAAEP,IAAEM,EAAC;AAAA,QAAC;AAAC,UAAEW,IAAEjB,EAAC;AAAA,MAAC;AAAC,QAAEE,IAAEe,EAAC,GAAE,EAAEnB,IAAEE,EAAC,GAAE,EAAEF,IAAEI,EAAC,GAAE,EAAEoB,IAAExB,EAAC;AAAA,IAAC;AAAC,KAACsB,MAAGjB,OAAI,EAAEkB,IAAEC,EAAC;AAAE,UAAMQ,KAAE7B,GAAE,MAAIA,GAAE;AAAG,IAAAY,MAAGc,OAAIA,KAAE,EAAE,CAAC,GAAE,EAAEA,IAAE,iBAAiB,IAAGA,GAAE,YAAYN,EAAC,KAAGM,KAAE,MAAK,EAAEG,IAAEH,MAAGN,EAAC;AAAA,EAAC,CAAE,GAAEH,OAAIjB,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,GAAE,KAAK,GAAE,EAAEwB,IAAExB,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAIU,GAAE,KAAK,CAAE,IAAGV,GAAE,GAAG,YAAUiB,KAAGE,OAAInB,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,GAAE,WAAW,GAAE,EAAEwB,IAAExB,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAIU,GAAE,CAAC,CAAC,CAAE,IAAGV,GAAE,GAAG,YAAUmB,KAAGC,OAAIpB,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,gBAAgB,GAAE,EAAEA,GAAE,IAAG,GAAE,MAAM,GAAE,EAAEyB,IAAEzB,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAIU,GAAE,CAAE,IAAGV,GAAE,GAAG,YAAUoB,KAAGpB,GAAE,OAAKA,GAAE,GAAG,aAAaA,GAAE,IAAGA,GAAE,EAAE,GAAEA,GAAE,KAAGA,GAAE,KAAI,GAAG,CAAC,GAAED,GAAE,MAAIA,GAAE,IAAE,MAAG,GAAG,EAAE,GAAG,IAAG,GAAEC,GAAE,EAAE,GAAEF,GAAED,EAAC,GAAE,EAAEG,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,EAAE,GAAE,WAAY,MAAI,EAAEA,GAAE,IAAG,UAAU,GAAG,GAAG,IAAG,EAAE,CAAC;AAAC;AAAE,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAME,KAAE,EAAE,GAAEC,KAAE,EAAE,IAAGE,KAAE,EAAE,OAAO,GAAEC,KAAE,EAAE,OAAO,GAAEC,KAAE,EAAE,MAAM,GAAEC,KAAE,EAAE,MAAM,GAAEC,KAAE,EAAE,MAAM,GAAEC,KAAE,EAAE,MAAM,GAAEE,KAAE,EAAE,MAAM;AAAE,MAAGF,GAAE,YAAU,EAAE,GAAE,CAAC,GAAEE,GAAE,YAAU,EAAE,GAAE,CAAC,GAAEN,GAAE,OAAK,YAAW,EAAED,IAAE,yBAAyB,GAAE,EAAEC,IAAE,iBAAiB,GAAE,EAAEI,IAAE,iBAAiB,GAAE,EAAEE,IAAE,kBAAkB,GAAE,EAAEL,IAAE,cAAc,GAAE,EAAEC,IAAE,qBAAqB,GAAE,EAAEC,IAAE,eAAe,GAAE,EAAEF,IAAE,GAAE,MAAM,GAAER,MAAG,EAAEM,IAAE,gBAAgB,GAAE,EAAEC,IAAE,GAAEN,EAAC,GAAEG,GAAE,GAAGH,EAAC,EAAEH,EAAC,IAAES,MAAGH,GAAE,GAAGN,EAAC,IAAES,IAAEP,MAAG,CAAAH,OAAG;AAAC,MAAEU,IAAE,UAAU,MAAI;AAAC,YAAMT,KAAEM,GAAE,GAAGP,EAAC,GAAEE,KAAEK,GAAE,GAAGP,EAAC;AAAE,MAAAM,GAAE,EAAEN,EAAC,IAAE,CAAC;AAAE,eAAQE,MAAKD,IAAE;AAAC,cAAME,KAAEF,GAAEC,EAAC;AAAE,QAAAC,GAAE,WAASG,GAAE,EAAEN,EAAC,EAAE,KAAKG,GAAE,KAAK;AAAA,MAAC;AAAC,MAAAD,GAAE,UAAQI,GAAE,EAAEN,EAAC,EAAE,SAAO;AAAA,IAAC,CAAE;AAAA,EAAC,GAAGI,EAAC,KAAG,CAAAJ,OAAG;AAAC,MAAEU,IAAE,GAAG,MAAI;AAAC,YAAMT,KAAEM,GAAE,GAAGP,EAAC,GAAEE,KAAEQ,GAAE;AAAQ,MAAAJ,GAAE,EAAEN,EAAC,IAAE,CAAC;AAAE,eAAQG,MAAKF;AAAE,QAAAA,GAAEE,EAAC,EAAE,UAAQD,IAAEA,MAAGI,GAAE,EAAEN,EAAC,EAAE,KAAKG,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAGF,EAAC,GAAES,GAAE,QAAMT,IAAEY,GAAE,cAAYb,GAAE,QAAQ,kBAAiB,EAAE,GAAE,EAAEY,IAAEI,EAAC,GAAE,EAAEJ,IAAEE,EAAC,GAAE,EAAEH,IAAEC,EAAC,GAAEN,GAAE;AAAE,KAACJ,GAAE,YAAUA,GAAE,aAAWQ,GAAE,UAAQ;AAAA,WAAYP,IAAE;AAAC,UAAMH,KAAEM,GAAE,EAAEF,EAAC;AAAE,IAAAM,GAAE,UAAQR,GAAE,YAAU,EAAEF,IAAEC,EAAC;AAAA,EAAC;AAAM,MAAEK,GAAE,GAAEL,EAAC,MAAIS,GAAE,UAAQ;AAAI,SAAOR,GAAE,aAAWQ,GAAE,WAAS,OAAI,EAAED,IAAEC,EAAC,GAAE,EAAED,IAAEE,EAAC,GAAE,EAAEF,IAAEI,EAAC,GAAEJ;AAAC;AAAC,IAAM,KAAG,MAAI;AAAC,QAAMT,KAAE,EAAE,MAAM;AAAE,SAAO,EAAE,GAAG,OAAK,EAAE,GAAG,KAAGA,KAAGA;AAAC;AAA7D,IAA+D,KAAG,CAACA,IAAEC,OAAI;AAAC,QAAMC,KAAE,EAAE,GAAEC,KAAE,EAAE,IAAG,EAAC,MAAKC,IAAE,iBAAgBC,IAAE,gBAAeO,GAAC,IAAEZ,IAAEa,KAAEX,GAAE,KAAGA,GAAE,EAAE;AAAa,MAAG,CAACW;AAAE;AAAO,QAAMC,KAAED,GAAE,cAAaE,KAAEF,GAAE,oBAAmBG,KAAEH,GAAE,oBAAmBI,KAAEJ,GAAE,gBAAeK,KAAEL,GAAE,QAAOO,KAAEP,GAAE,OAAMQ,KAAER,GAAE,OAAMS,KAAE,CAAAtB,OAAG;AAAC,IAAAI,GAAE,GAAEQ,GAAEZ,EAAC;AAAA,EAAC;AAAE,MAAG,CAACG,GAAE,IAAG;AAAC,IAAAA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,YAAY,GAAE,EAAEA,GAAE,IAAG,IAAI,GAAE,EAAEA,GAAE,IAAG,MAAM,GAAE,EAAEA,GAAE,IAAG,OAAO,GAAE,EAAEA,GAAE,IAAG,MAAM,GAAE,EAAEA,GAAE,IAAG,QAAO,QAAQ,GAAE,EAAEA,GAAE,IAAG,cAAa,MAAM,GAAE,EAAEA,GAAE,IAAG,GAAE,OAAO,GAAE,EAAEA,GAAE,IAAG,oBAAmB,UAAU,GAAEiB,KAAE,EAAEjB,GAAE,IAAG,cAAaiB,EAAC,IAAEC,MAAG,EAAElB,GAAE,IAAG,mBAAkB,WAAW;AAAE,UAAMH,KAAE,OAAMC,KAAEC,GAAE,EAAE,YAAWE,KAAEH,MAAGA,GAAE,cAAaI,MAAGD,MAAGA,GAAE,UAAQJ,IAAG,MAAM,GAAG,EAAE,CAAC,MAAIA;AAAE,IAAAqB,MAAGJ,MAAGZ,OAAIF,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,GAAG,YAAU,EAAE,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,YAAY,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAI;AAAC,MAAAmB,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,EAAEnB,GAAE,IAAGA,GAAE,EAAE,IAAG,EAAEA,GAAE,IAAG,cAAac,EAAC,IAAG,EAAEd,GAAE,IAAGA,GAAE,EAAE,IAAGW,MAAGC,MAAGC,OAAI,EAAEb,GAAE,IAAGA,GAAE,EAAE,GAAEA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,YAAW,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE;AAAA,EAAC;AAAC,EAAAkB,OAAIlB,GAAE,OAAKA,GAAE,KAAG,EAAE,IAAI,GAAEA,GAAE,GAAG,YAAUA,GAAE,GAAG,KAAG,aAAY,EAAEA,GAAE,IAAGA,GAAE,EAAE,IAAGA,GAAE,GAAG,YAAUkB;AAAG,MAAIE,KAAEV,GAAE;AAAY,MAAGU,OAAIrB,GAAE,MAAIqB,KAAEA,GAAE,QAAQ,uBAAsBrB,GAAE,IAAE,KAAGW,GAAE,mBAAiB,EAAE,IAAGV,GAAE,OAAKA,GAAE,KAAG,EAAE,GAAG,GAAEA,GAAE,GAAG,YAAUA,GAAE,GAAG,KAAG,YAAW,EAAEA,GAAE,IAAGA,GAAE,EAAE,IAAGA,GAAE,GAAG,YAAUoB,KAAGT,OAAIX,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,GAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,GAAE,KAAK,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAI;AAAC,IAAAmB,GAAE,KAAK;AAAA,EAAC,CAAE,IAAGnB,GAAE,GAAG,kBAAkB,YAAUW,KAAGC,OAAIZ,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,GAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,GAAE,WAAW,GAAE,EAAEA,GAAE,IAAG,GAAG,MAAI;AAAC,IAAAmB,GAAE,CAAC,CAAC;AAAA,EAAC,CAAE,IAAGnB,GAAE,GAAG,kBAAkB,YAAUY,KAAGC,OAAIb,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,GAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,KAAK,GAAE,EAAEA,GAAE,IAAG,gBAAgB,GAAE,EAAEA,GAAE,IAAG,GAAE,MAAM,GAAE,EAAEA,GAAE,IAAG,cAAc,MAAI;AAAC,IAAAD,GAAE,KAAG,GAAGF,IAAEC,EAAC;AAAA,EAAC,CAAE,GAAE,EAAEE,GAAE,IAAG,GAAEE,EAAC,IAAGF,GAAE,GAAG,kBAAkB,YAAUa,KAAGb,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAG,CAAC,GAAEW,MAAG,EAAEX,GAAE,IAAGA,GAAE,EAAE,GAAEY,MAAG,EAAEZ,GAAE,IAAGA,GAAE,EAAE,IAAGW,MAAGC,OAAI,EAAEZ,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,IAAGA,GAAE,MAAI,CAACA,GAAE,OAAKA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,MAAIA,GAAE,MAAI,EAAEA,GAAE,IAAG,CAAC,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAGA,GAAE,EAAE,MAAI,EAAEA,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,IAAG,IAAE,UAAU,KAAIe,IAAE;AAAC,QAAG,CAACf,GAAE,IAAG;AAAC,UAAIH,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,CAAC;AAAE,MAAAE,GAAE,KAAG,EAAE,CAAC,GAAE,EAAEH,IAAE,QAAQ,GAAE,EAAEC,IAAE,OAAO,GAAE,EAAEE,GAAE,IAAG,YAAY,GAAE,EAAEF,IAAEE,GAAE,EAAE,GAAE,EAAEH,IAAEC,EAAC,GAAE,EAAEE,GAAE,IAAGH,EAAC;AAAA,IAAC;AAAC,IAAAG,GAAE,GAAG,YAAUe;AAAA,EAAC;AAAC,KAAG,CAAC,GAAEhB,GAAE,MAAIA,GAAE,IAAE,MAAG,GAAG,EAAE,GAAG,IAAG,GAAEC,GAAE,EAAE,GAAEF,GAAED,EAAC,GAAE,EAAEG,GAAE,IAAGA,GAAE,EAAE,GAAE,EAAEA,GAAE,EAAE,GAAE,WAAY,MAAI,EAAEA,GAAE,IAAG,UAAU,GAAG,GAAG,IAAG,EAAE,CAAC,GAAE,EAAEA,GAAE,IAAGH,IAAE,IAAGC,EAAC;AAAC;AAA5lE,IAA8lE,KAAG,CAAAD,OAAG;AAAC,MAAG,CAAC,EAAEA,EAAC;AAAE,WAAO;AAAK,MAAGA,MAAK,EAAE,EAAE;AAAE,WAAOA;AAAE,MAAIC,KAAED,GAAE,MAAM,GAAE,CAAC;AAAE,SAAOC,MAAK,EAAE,EAAE,IAAEA,KAAE;AAAI;AAA5rE,IAA8rE,KAAG,MAAI,EAAE,EAAE,KAAG,EAAE,EAAE,EAAE,SAAS;AAA3tE,IAAmuE,KAAG,CAAAD,OAAG;AAAC,EAAAA,OAAI,EAAE,EAAE,IAAEA;AAAE;AAAtvE,IAAwvE,KAAG,OAAMA,OAAG;AAAC,QAAMC,KAAE,EAAE;AAAE,MAAIC,KAAE,GAAGF,EAAC,IAAEA,KAAE,GAAG,GAAEG,KAAEF,GAAE,EAAEC,EAAC;AAAE,MAAG,EAAEC,EAAC,IAAEA,KAAE,OAAM,OAAMH,OAAG;AAAC,QAAG;AAAC,YAAMC,KAAE,MAAM,MAAMD,EAAC;AAAE,aAAO,MAAMC,GAAE,KAAK;AAAA,IAAC,SAAOD,IAAE;AAAC,aAAO,QAAQ,MAAMA,EAAC,GAAE;AAAA,IAAE;AAAA,EAAC,GAAGG,EAAC,IAAE,EAAEA,EAAC,MAAIA,KAAE,MAAMA,GAAE,IAAG,CAACA;AAAE,UAAK,uCAAuCD,EAAC;AAAa,SAAOD,GAAE,IAAEE,IAAE,GAAGD,EAAC,GAAE;AAAE;AAAxgF,IAA0gF,KAAG,MAAI;AAAC,MAAIF,KAAE,EAAE,EAAE,EAAE,SAAS,KAAIC,KAAE,EAAE,GAAG;AAAG,EAAAD,MAAGC,OAAI,EAAED,EAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,EAAEA,IAAE,EAAE,EAAE,CAAC,IAAE,EAAEC,IAAE,SAAS,IAAE,EAAEA,IAAE,SAAS;AAAE;AAAnnF,IAAqnF,KAAG,MAAI;AAAC,QAAMD,KAAE,EAAE;AAAG,MAAGA,GAAE;AAAG;AAAO,EAAAA,GAAE,KAAG,EAAE,CAAC,GAAEA,GAAE,GAAG,KAAG,WAAUA,GAAE,GAAG,aAAa,kBAAiB,EAAE,GAAE,GAAG;AAAE,MAAIC,KAAE,EAAE,EAAE,EAAE;AAAK,EAAAA,MAAG,EAAEA,EAAC,MAAIA,KAAE,SAAS,cAAcA,EAAC,KAAIA,MAAGD,GAAE,GAAG,MAAM,YAAYA,GAAE,EAAE;AAAC;AAAzzF,IAA2zF,KAAG,CAAAA,OAAG,GAAI,MAAI,aAAa,WAAWA,EAAC,CAAE;AAAp2F,IAAs2F,KAAG,CAACA,IAAEC,OAAI;AAAC,MAAGA,cAAa;AAAO,WAAOD,GAAE,OAAQ,CAAAA,OAAGC,GAAE,KAAKD,EAAC,CAAE;AAAE;AAAC,UAAME,KAAE,EAAEF,IAAEC,EAAC;AAAE,WAAOC,KAAE,KAAG,CAACF,GAAEE,EAAC,CAAC,IAAE,CAAC;AAAA,EAAC;AAAC;AAA98F,IAAg9F,KAAG,CAAAF,OAAG;AAAC,QAAK,EAAC,UAASC,IAAE,UAASC,GAAC,IAAE,UAAS,EAAC,MAAKC,IAAE,MAAKC,IAAE,QAAOC,IAAE,UAASC,IAAE,iBAAgBC,IAAE,QAAOC,GAAC,IAAE,EAAE,EAAE,QAAOC,KAAET,MAAG,MAAI;AAAC,UAAMA,KAAE,EAAE,EAAE,GAAEC,KAAED,KAAE,oBAAI,SAAKA,KAAE;AAAE,WAAO,QAAM,EAAE,IAAEC;AAAA,EAAC,GAAG,IAAE,QAAM,EAAE,GAAEkB,KAAE,oBAAI;AAAK,EAAAA,GAAE,QAAQA,GAAE,QAAQ,IAAEV,EAAC,GAAE,EAAE,EAAE,EAAE,iBAAeU,GAAE,QAAQ;AAAE,QAAMT,KAAE,KAAK,UAAU,EAAE,EAAE,CAAC;AAAE,MAAIC,KAAER,KAAE,MAAI,mBAAmBO,EAAC,KAAG,MAAID,KAAE,eAAaU,GAAE,YAAY,IAAE,MAAI,YAAUf,KAAE,gBAAcE;AAAE,IAAEL,IAAE,GAAG,MAAIU,MAAG,cAAYN,KAAGG,MAAG,aAAWN,OAAIS,MAAG,aAAYJ,MAAG,CAACP,IAAEC,OAAI;AAAC,OAAI,MAAI,aAAa,QAAQD,IAAEC,EAAC,CAAE;AAAA,EAAC,GAAGE,IAAEO,EAAC,IAAE,SAAS,SAAOC,IAAE,EAAE,EAAE;AAAC;AAAr+G,IAAu+G,KAAG,CAACX,IAAEC,IAAEC,OAAI;AAAC,MAAG,MAAIF,GAAE;AAAO;AAAO,QAAMG,KAAED,MAAG,EAAE,EAAE,OAAO,QAAOE,KAAEH,MAAG,EAAE,EAAE,OAAO,MAAKI,KAAE,WAASF,GAAE,MAAM,GAAE,CAAC,GAAEG,KAAED,MAAGF,GAAE,UAAU,CAAC,GAAEI,KAAE,CAACP,IAAEC,OAAI;AAAC,IAAAA,MAAG,QAAMA,GAAE,MAAM,GAAE,CAAC,MAAIA,KAAE,MAAIA,KAAG,SAAS,SAAOD,KAAE,aAAWI,MAAGH,KAAE,cAAYA,KAAE,MAAI;AAAA,EAA0C;AAAE,aAAUA,MAAKD;AAAE,IAAAO,GAAEN,IAAEC,EAAC,GAAEA,MAAGK,GAAEN,IAAEE,EAAC,GAAEE,MAAGE,GAAEN,IAAEK,EAAC;AAAC;AAA/xH,IAAiyH,KAAG,CAAAN,OAAG;AAAC,QAAMC,KAAED,MAAG,EAAE,EAAE,OAAO,MAAKE,KAAE,EAAE,EAAE,OAAO;AAAgB,UAAO,CAACF,IAAEC,OAAI;AAAC,QAAIC;AAAE,WAAOA,KAAE,GAAI,MAAI,KAAK,MAAMD,KAAED,KAAE,mBAAmBA,EAAC,CAAC,GAAG,IAAE,KAAG,CAAC,GAAEE;AAAA,EAAC,GAAGA,MAAGC,KAAEF,IAAE,GAAI,MAAI,aAAa,QAAQE,EAAC,CAAE,KAAG,MAAI,GAAGF,IAAE,IAAE,GAAEC,EAAC;AAAE,MAAIC;AAAC;AAAx/H,IAA0/H,KAAG,CAACH,IAAEC,OAAI;AAAC,QAAMC,KAAE,SAAS,OAAO,MAAM,cAAYF,KAAE,kBAAkB;AAAE,SAAOE,KAAED,KAAEC,GAAE,IAAI,IAAEF,KAAE;AAAE;AAA5lI,IAA8lI,KAAG,CAAAA,OAAG;AAAC,QAAMC,KAAE,SAAS,OAAO,MAAM,MAAM,GAAEC,KAAE,CAAC;AAAE,aAAUC,MAAKF,IAAE;AAAC,QAAIA,KAAEE,GAAE,MAAM,GAAG,EAAE,CAAC;AAAE,IAAAH,KAAE,GAAI,MAAI;AAAC,MAAAA,GAAE,KAAKC,EAAC,KAAGC,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE,IAAEC,GAAE,KAAKD,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAA7uI,IAA+uI,KAAG,CAACA,IAAEC,KAAE,CAAC,MAAI;AAAC,GAAC,CAACH,IAAEC,OAAI;AAAC,UAAK,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,QAAIC,KAAE,CAAC;AAAE,QAAGT,IAAE;AAAC,QAAEA,EAAC,IAAES,GAAE,KAAK,GAAGT,EAAC,IAAE,EAAEA,EAAC,MAAIS,KAAE,UAAQT,KAAEE,KAAE,CAACF,EAAC;AAAG,iBAAUA,MAAKE;AAAE,QAAAI,GAAEN,EAAC,IAAE,EAAES,IAAET,EAAC,IAAE,EAAEQ,GAAER,EAAC,CAAC,IAAE,CAAC;AAAA,IAAC;AAAM,MAAAS,KAAE,CAAC,GAAGN,IAAE,GAAGI,EAAC,GAAEF,OAAII,MAAG,MAAI;AAAC,cAAMT,KAAE,EAAE,GAAG;AAAG,YAAG,CAACA;AAAE,iBAAM,CAAC;AAAE,YAAIC,KAAE,CAAC;AAAE,iBAAQC,MAAKF;AAAE,UAAAA,GAAEE,EAAC,EAAE,WAASD,GAAE,KAAKC,EAAC;AAAE,eAAOD;AAAA,MAAC,GAAG;AAAG,IAAAQ,KAAEA,GAAE,OAAQ,CAAAT,OAAG,CAAC,EAAEE,IAAEF,EAAC,KAAG,CAAC,EAAEC,IAAED,EAAC,CAAE,GAAES,GAAE,KAAK,GAAGL,EAAC,GAAE,EAAEK,EAAC;AAAA,EAAC,GAAGP,IAAEC,EAAC,IAAG,MAAI;AAAC,UAAMH,KAAE,EAAE,GAAE,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEL,IAAEM,KAAED;AAAE,IAAAL,GAAE,KAAG,EAAEG,EAAC;AAAE,eAAUE,MAAKC,IAAE;AAAC,YAAMA,KAAEF,GAAEC,EAAC,GAAEE,KAAE,EAAED,EAAC,GAAEE,KAAEP,GAAEI,EAAC,KAAGJ,GAAEI,EAAC,EAAE,SAAO,GAAEI,KAAE,EAAEP,IAAEG,EAAC;AAAE,UAAG,MAAIE,GAAE,QAAO;AAAC,YAAGJ,GAAEE,EAAC,IAAE,CAAC,GAAEI;AAAE,UAAAN,GAAEE,EAAC,EAAE,KAAK,GAAGE,EAAC;AAAA,iBAAUC,IAAE;AAAC,gBAAMR,KAAEC,GAAEI,EAAC;AAAE,UAAAF,GAAEE,EAAC,EAAE,KAAK,GAAGL,EAAC;AAAA,QAAC;AAAM,UAAAG,GAAEE,EAAC,IAAEL,GAAE,EAAEK,EAAC;AAAE,QAAAF,GAAEE,EAAC,IAAE,EAAEF,GAAEE,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC,GAAG,IAAG,MAAI;AAAC,UAAMH,KAAE,EAAE;AAAE,IAAAA,GAAE,IAAE,EAAE,EAAE,SAAO,KAAGA,GAAE,IAAE,EAAEA,GAAE,GAAEA,GAAE,CAAC,IAAE,EAAEA,GAAE,GAAEA,GAAE,EAAE,UAAU;AAAE,QAAIC,KAAED,GAAE,EAAE,SAAO,GAAEE,KAAE;AAAG,eAAUJ,MAAKE,GAAE;AAAE,MAAAA,GAAE,GAAGF,EAAC,IAAE,EAAEE,GAAE,EAAEF,EAAC,GAAEE,GAAE,GAAGF,EAAC,CAAC,GAAEE,GAAE,GAAGF,EAAC,EAAE,SAAO,MAAII,KAAE;AAAI,UAAMC,KAAE,EAAE,GAAG;AAAG,eAAUL,MAAKK;AAAE,MAAAA,GAAEL,EAAC,EAAE,UAAQ,EAAEE,GAAE,GAAEF,EAAC;AAAE,eAAUA,MAAKE,GAAE,GAAE;AAAC,YAAMD,KAAE,EAAE,GAAG,GAAGD,EAAC,GAAEG,KAAED,GAAE,EAAEF,EAAC;AAAE,iBAAUA,MAAKC;AAAE,QAAAA,GAAED,EAAC,EAAE,UAAQ,EAAEG,IAAEH,EAAC;AAAA,IAAC;AAAC,IAAAE,GAAE,MAAIA,GAAE,IAAE,oBAAI,SAAMA,GAAE,MAAIA,GAAE,KAAG,CAAC,GAAG,IAAE,OAAK,OAAK,OAAK,OAAO,QAAQ,UAAU,CAAAF,QAAIA,KAAE,OAAO,gBAAgB,IAAI,WAAW,CAAC,CAAC,EAAE,CAAC,IAAE,MAAIA,KAAE,GAAG,SAAS,EAAE,CAAE,IAAGE,GAAE,IAAE,EAAC,YAAW,EAAEA,GAAE,CAAC,GAAE,UAAS,EAAE,EAAE,UAAS,MAAKA,GAAE,GAAE,kBAAiBA,GAAE,EAAE,YAAY,GAAE,WAAUA,GAAE,GAAE,UAAS,EAAEA,GAAE,CAAC,GAAE,cAAa,EAAE,EAAE,EAAC,GAAEA,GAAE,MAAIA,GAAE,EAAE,uBAAqBA,GAAE,EAAE,YAAY;AAAG,QAAII,KAAE;AAAG,UAAMC,KAAEJ,MAAGC;AAAE,KAACF,GAAE,KAAGK,QAAKL,GAAE,MAAIA,GAAE,IAAE,OAAGI,KAAE,OAAIJ,GAAE,IAAEA,GAAE,IAAE,oBAAI,SAAKA,GAAE,GAAEA,GAAE,EAAE,uBAAqBA,GAAE,EAAE,YAAY,GAAE,GAAG,GAAE,EAAE,EAAE,qBAAmBI,MAAGC,QAAK,CAAAP,OAAG;AAAC,YAAMC,KAAE,EAAE,GAAEC,KAAE,GAAG,GAAEC,MAAG,CAAAH,OAAG;AAAC,cAAMC,KAAE,EAAE;AAAE,gBAAOD,KAAEC,GAAE,IAAEA,GAAE,GAAG,OAAQ,CAAAD,OAAG;AAAC,gBAAME,KAAED,GAAE,EAAED,EAAC;AAAE,iBAAM,CAAC,CAACE,MAAG,CAACA,GAAE,YAAU,CAAC,CAACA,GAAE;AAAA,QAAS,CAAE;AAAA,MAAC,GAAGF,EAAC;AAAE,iBAAUA,MAAKC,GAAE;AAAG,mBAAUE,MAAKF,GAAE,GAAGD,EAAC,GAAE;AAAC,gBAAMI,KAAEH,GAAE,EAAED,EAAC,EAAEG,EAAC,EAAE;AAAQ,cAAG,CAAC,EAAEF,GAAE,EAAED,EAAC,GAAEG,EAAC,KAAGC;AAAE,uBAAUJ,MAAKI,IAAE;AAAC,oBAAMH,KAAE,GAAGC,IAAEF,GAAE,IAAI;AAAE,iBAAGC,IAAED,GAAE,MAAKA,GAAE,MAAM;AAAA,YAAC;AAAA,QAAC;AAAC,iBAAUI,MAAKD,IAAE;AAAC,cAAMA,KAAEF,GAAE,EAAEG,EAAC,EAAE,WAAUC,KAAEF,MAAGA,GAAE,WAAS,CAAC,GAAEG,KAAE,EAAEL,GAAE,GAAEG,EAAC,GAAEG,KAAE,CAAC,EAAEN,GAAE,GAAEG,EAAC,GAAEI,KAAEF,MAAGC;AAAE,YAAGP,KAAEO,KAAEC,IAAE;AAAC,UAAAL,GAAE,cAAYK,OAAIP,GAAE,IAAE;AAAI,qBAAUD,MAAKK,IAAE;AAAC,kBAAMJ,KAAE,GAAGC,IAAEF,GAAE,IAAI;AAAE,eAAGC,IAAED,GAAE,MAAKA,GAAE,MAAM;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,GAAGM,EAAC,GAAE,GAAG,IAAGA,OAAI,GAAG,EAAE,GAAG,EAAE,GAAE,GAAG,EAAE,GAAG,EAAE,GAAE,EAAE,EAAE,SAAO,OAAKC,MAAG,GAAG,EAAE,GAAG,EAAE,GAAEL,GAAE,MAAIA,GAAE,IAAE,OAAG,SAAS,OAAO;AAAA,EAAG,GAAG;AAAC;AAArqM,IAAuqM,KAAG,CAAAF,OAAG;AAAC,QAAMC,KAAE,EAAE,EAAE,IAAE,CAAC,IAAE,EAAE,EAAE;AAAE,SAAO,EAAEA,IAAED,EAAC;AAAC;AAAltM,IAAotM,KAAG,CAACA,IAAEC,OAAI;AAAC,QAAK,EAAC,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,MAAG,EAAEH,MAAGC,MAAG,EAAEA,EAAC,KAAG,EAAEC,IAAED,EAAC,KAAG,MAAI,EAAEE,GAAEF,EAAC,CAAC,EAAE;AAAQ,WAAM;AAAG,GAAC,CAACD,IAAEC,OAAI;AAAC,UAAMC,KAAE,EAAE,GAAE,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH,IAAEI,KAAE,EAAE,GAAG,GAAGL,EAAC,KAAG,CAAC,GAAEM,KAAE,EAAE,GAAG,GAAGN,EAAC,KAAG,CAAC,GAAEO,KAAE,EAAEL,GAAEF,EAAC,CAAC;AAAE,QAAGG,GAAEH,EAAC,IAAE,CAAC,GAAE,EAAED,EAAC,GAAE;AAAC,UAAG,UAAQA,IAAE;AAAC,YAAGI,GAAEH,EAAC,EAAE,KAAK,GAAGO,EAAC,GAAEH;AAAE,mBAAQL,MAAKM;AAAE,YAAAA,GAAEN,EAAC,EAAE,UAAQ,MAAG,EAAEM,GAAEN,EAAC,CAAC;AAAA,MAAC,WAAS,EAAEQ,IAAER,EAAC,KAAGI,GAAEH,EAAC,EAAE,KAAKD,EAAC,GAAEK;AAAE,iBAAQJ,MAAKK;AAAE,UAAAA,GAAEL,EAAC,EAAE,UAAQD,OAAIC,IAAE,EAAEK,GAAEL,EAAC,CAAC;AAAA,IAAC,WAAS,EAAED,EAAC;AAAE,eAAQE,MAAKM,IAAE;AAAC,cAAML,KAAE,EAAEH,IAAEE,EAAC;AAAE,QAAAC,MAAGC,GAAEH,EAAC,EAAE,KAAKC,EAAC,GAAEG,OAAIC,GAAEJ,EAAC,EAAE,UAAQC,IAAE,EAAEG,GAAEJ,EAAC,CAAC;AAAA,MAAE;AAAC,UAAMO,KAAE,MAAIL,GAAEH,EAAC,EAAE;AAAO,IAAAC,GAAE,IAAEO,KAAEP,GAAE,EAAE,OAAQ,CAAAF,OAAGA,OAAIC,EAAE,IAAE,EAAE,CAAC,GAAGC,GAAE,GAAED,EAAC,CAAC,GAAEI,OAAIE,GAAE,UAAQ,CAACE,IAAE,EAAEF,EAAC;AAAA,EAAE,GAAGP,IAAEC,EAAC,GAAE,GAAG;AAAC;AAA1tN,IAA4tN,KAAG,CAACD,IAAEC,OAAI;AAAC,QAAMC,KAAE,EAAE,EAAE,IAAE,CAAC,IAAE,EAAE,EAAE,EAAED,EAAC,KAAG,CAAC;AAAE,SAAO,EAAEC,IAAEF,EAAC;AAAC;AAAlxN,IAAoxN,KAAG,CAAAA,OAAG,OAAK,GAAGA,IAAE,IAAE;AAAtyN,IAAwyN,KAAG,CAACA,IAAEC,IAAEC,OAAI;AAAC,MAAIC,KAAE,CAAC;AAAE,QAAMC,KAAE,CAAAJ,OAAG;AAAC,QAAG,EAAEA,EAAC,GAAE;AAAC,UAAIC,KAAE,GAAGD,EAAC;AAAE,aAAKC,MAAGE,GAAE,KAAKF,EAAC;AAAA,IAAC;AAAM,MAAAE,GAAE,KAAK,GAAG,GAAGH,EAAC,CAAC;AAAA,EAAC;AAAE,MAAG,EAAEA,EAAC;AAAE,aAAQC,MAAKD;AAAE,MAAAI,GAAEH,EAAC;AAAA;AAAO,IAAAG,GAAEJ,EAAC;AAAE,KAAGG,IAAEF,IAAEC,EAAC;AAAC;AAAv7N,IAAy7N,KAAG,CAAAF,OAAG;AAAC,QAAK,EAAC,IAAGC,IAAE,GAAEE,GAAC,IAAE;AAAE,MAAG,CAACA,GAAE,GAAE;AAAC,QAAG,CAACA,GAAE,GAAE;AAAC,UAAG,CAACH;AAAE;AAAO,SAAG,IAAG,EAAE;AAAA,IAAC;AAAC,IAAAG,GAAE,IAAE,MAAGA,GAAE,IAAE,EAAE,GAAEA,GAAE,KAAG,EAAE,IAAE,GAAE,EAAEF,GAAE,IAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,GAAE,OAAO,GAAE,WAAY,MAAI;AAAC,QAAE,EAAE,GAAG,EAAE;AAAA,IAAC,GAAG,GAAG,GAAE,GAAG,EAAE,GAAG,IAAG,CAAC;AAAA,EAAC;AAAC;AAA5mO,IAA8mO,KAAG,MAAI;AAAC,QAAK,EAAC,IAAGD,IAAE,GAAEC,IAAE,IAAGE,GAAC,IAAE;AAAE,EAAAF,GAAE,MAAIA,GAAE,IAAE,OAAGA,GAAE,KAAG,EAAE,GAAE,EAAED,GAAE,IAAG,IAAE,GAAE,EAAEA,GAAE,IAAG,CAAC,GAAE,EAAEA,GAAE,IAAG,GAAE,MAAM,GAAE,EAAEC,GAAE,CAAC,GAAEA,GAAE,IAAE,MAAK,GAAGE,GAAE,IAAG,CAAC;AAAE;AAApuO,IAAsuO,KAAG,MAAI;AAAC,QAAMH,KAAE,EAAE;AAAE,EAAAA,GAAE,MAAIA,GAAE,KAAG,GAAG,IAAG,EAAE,GAAEA,GAAE,IAAE,MAAGA,GAAE,IAAEA,GAAE,IAAE,EAAE,IAAEA,GAAE,IAAE,EAAE,GAAE,EAAE,EAAE,GAAG,IAAG,CAAC,GAAE,EAAE,EAAE,GAAG,IAAG,CAAC,GAAE,EAAE,EAAE,GAAG,IAAG,GAAE,OAAO,GAAE,WAAY,MAAI;AAAC,MAAE,EAAE,GAAG,EAAE;AAAA,EAAC,GAAG,GAAG,GAAE,GAAG,EAAE,GAAG,IAAG,CAAC;AAAE;AAA14O,IAA44O,KAAG,MAAI;AAAC,QAAMA,KAAE,EAAE;AAAE,EAAAA,GAAE,MAAIA,GAAE,IAAE,QAAI,MAAI;AAAC,UAAMA,KAAE,GAAG,GAAEC,KAAE,EAAE,EAAE,GAAEC,KAAE,EAAE,GAAG,IAAGC,KAAE,EAAE,GAAG,IAAGC,KAAE,CAAAJ,OAAG,EAAE,EAAE,EAAE,GAAEA,EAAC;AAAE,eAAUK,MAAKH,IAAE;AAAC,YAAMI,KAAE,CAAC,CAACL,GAAEI,EAAC,EAAE;AAAS,MAAAH,GAAEG,EAAC,EAAE,UAAQC,OAAIN,KAAE,GAAGK,EAAC,IAAED,GAAEC,EAAC;AAAG,iBAAUJ,MAAKE,GAAEE,EAAC;AAAE,QAAAF,GAAEE,EAAC,EAAEJ,EAAC,EAAE,UAAQK,OAAIN,KAAE,GAAGC,IAAEI,EAAC,IAAED,GAAEC,EAAC;AAAA,IAAE;AAAA,EAAC,GAAG,GAAE,EAAE,EAAE,GAAG,IAAG,IAAE,GAAE,EAAE,EAAE,GAAG,IAAG,CAAC,GAAE,EAAE,EAAE,GAAG,IAAG,GAAE,MAAM,GAAEL,GAAE,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,IAAE,SAAO,EAAEA,GAAE,CAAC,GAAEA,GAAE,IAAE,OAAM,GAAG,EAAE,GAAG,IAAG,CAAC;AAAE;AAAE,IAAI,KAAG,EAAC,MAAK,IAAG,MAAK,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,gBAAe,GAAE;AAAE,IAAM,KAAG,OAAMA,IAAEC,OAAI;AAAC,MAAG,CAAC,GAAGD,EAAC;AAAE,WAAM;AAAG,QAAME,KAAE,EAAE;AAAE,SAAM,EAAEF,OAAI,GAAG,KAAG,SAAKC,MAAG,CAAC,MAAM,GAAGD,EAAC,MAAI,GAAGA,EAAC,GAAEE,GAAE,KAAG,GAAG,IAAG,EAAE,GAAEA,GAAE,KAAG,GAAG,IAAG,EAAE,GAAE,GAAG,GAAE;AAAG;AAA1I,IAA4I,KAAG,MAAI;AAAC,QAAK,EAAC,GAAEF,IAAE,GAAEC,GAAC,IAAE,EAAE,GAAE,EAAC,UAASC,IAAE,UAASC,GAAC,KAAG,MAAI;AAAC,UAAK,EAAC,GAAEH,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,WAAM,EAAC,UAASD,IAAE,UAASD,KAAE,CAAC,IAAEE,GAAE,OAAQ,CAAAF,OAAG,CAAC,EAAEC,IAAED,EAAC,CAAE,EAAC;AAAA,EAAC,GAAG;AAAE,SAAO,EAAE,EAAC,YAAWA,IAAE,oBAAmBE,IAAE,oBAAmBC,IAAE,kBAAiBF,IAAE,kBAAiB,EAAE,EAAC,CAAC;AAAC;AAAlY,IAAoY,KAAG,CAACD,IAAEC,OAAI;AAAC,MAAIC,KAAE,SAAS,cAAc,iBAAeF,KAAE,IAAI;AAAE,SAAO,IAAI,QAAS,CAAAG,OAAG;AAAC,QAAGD;AAAE,aAAOC,GAAE,IAAE;AAAE,QAAGD,KAAE,EAAE,QAAQ,GAAE,EAAED,EAAC;AAAE,iBAAUD,MAAKC;AAAE,UAAEC,IAAEF,IAAEC,GAAED,EAAC,CAAC;AAAE,IAAAE,GAAE,SAAO,MAAIC,GAAE,IAAE,GAAED,GAAE,UAAQ,MAAI;AAAC,MAAAA,GAAE,OAAO,GAAEC,GAAE,KAAE;AAAA,IAAC,GAAED,GAAE,MAAIF,IAAE,EAAE,SAAS,MAAKE,EAAC;AAAA,EAAC,CAAE;AAAC;AAAjnB,IAAmnB,KAAG,CAAAF,OAAG;AAAC,MAAIC,IAAEC,KAAEF,GAAE,OAAMG,KAAEH,GAAE,MAAKI,KAAE;AAAG,QAAMC,KAAE,EAAE;AAAE,MAAG,aAAWF,IAAE;AAAC,IAAAE,GAAE,IAAEJ,KAAE,GAAG,MAAM;AAAE,UAAMD,KAAE,OAAOC,MAAG,OAAOC;AAAE,QAAGF,MAAG,YAAU,OAAOC,IAAE;AAAC,OAACA,OAAIA,KAAE,CAAC;AAAG,eAAQD,MAAKE;AAAE,QAAAD,GAAED,EAAC,MAAIE,GAAEF,EAAC,MAAIC,GAAED,EAAC,IAAEE,GAAEF,EAAC,GAAEI,KAAE;AAAA,IAAG;AAAK,OAACJ,MAAGC,MAAGA,OAAIC,OAAID,KAAEC,IAAEE,KAAE;AAAA,EAAG;AAAM,IAAAH,KAAEC,IAAEE,KAAE;AAAG,SAAOA,OAAIC,GAAE,IAAEJ,IAAEI,GAAE,EAAE,OAAKJ,IAAE,GAAG,IAAE,IAAGG;AAAC;AAAn4B,IAAq4B,KAAG,CAACJ,IAAEC,OAAI;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,SAAOD,KAAEE,GAAEF,EAAC,IAAEE;AAAC;AAA76B,IAA+6B,KAAG,CAAAF,OAAG;AAAC,QAAMC,KAAE,EAAE,GAAEC,KAAE,EAAE,EAAE;AAAE,SAAOF,KAAEC,GAAED,EAAC,KAAGE,GAAEF,EAAC,IAAE,EAAC,GAAGC,IAAE,GAAGC,IAAE,QAAO,EAAC,GAAGD,GAAE,OAAM,EAAC;AAAC;AAA9/B,IAAggC,KAAG,MAAI,CAAC,EAAE,EAAE;AAA5gC,IAA8gC,KAAG,OAAMD,OAAG;AAAC,QAAK,EAAC,GAAEE,IAAE,GAAEC,IAAE,IAAGC,GAAC,IAAE,GAAEE,KAAE;AAAO,MAAG,CAACA,GAAE,QAAO;AAAC,QAAGA,GAAE,SAAO,OAAI,CAAAN,OAAG;AAAC,YAAK,EAAC,IAAGE,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,GAAEE,KAAEH,IAAEI,KAAEH,IAAE,EAAC,QAAOI,GAAC,IAAEF,IAAEG,KAAE,EAAE,IAAGU,KAAEnB,GAAE,QAAOU,KAAEV,GAAE,YAAWW,KAAE,EAAED,EAAC,KAAG,CAAC,GAAEE,KAAE,WAAUC,KAAE;AAAS,MAAAX,GAAE,KAAGW,IAAEX,GAAE,KAAGW,GAAE,iBAAgBL,GAAE,SAAO,SAAS,UAASD,GAAE,IAAEP,IAAEO,GAAE,IAAEG,IAAEH,GAAE,IAAEI,IAAEJ,GAAE,IAAEP,GAAE,SAAS,cAAaO,GAAE,IAAE,CAAC,CAACP,GAAE,wBAAuBS,GAAE,KAAGT,GAAE,gBAAeS,GAAE,KAAGT,GAAE,WAAUS,GAAE,KAAGT,GAAE,UAASS,GAAE,KAAGT,GAAE,aAAYS,GAAE,KAAGT,GAAE,aAAYS,GAAE,KAAGT,GAAE;AAAa,YAAK,EAAC,MAAKc,IAAE,UAASE,IAAE,oBAAmBC,IAAE,kBAAiBG,IAAE,UAASE,IAAE,kBAAiBC,IAAE,cAAaC,GAAC,IAAExB;AAAE,MAAAc,OAAI,MAAIR,GAAE,OAAKQ,KAAG,aAAW,OAAOM,OAAId,GAAE,mBAAiBc,KAAG,aAAW,OAAOG,OAAIjB,GAAE,mBAAiBiB,KAAG,YAAU,OAAOD,MAAGA,MAAG,MAAIhB,GAAE,WAASgB,IAAEf,GAAE,IAAE,OAAI,aAAW,OAAOS,OAAIV,GAAE,WAASU,KAAG,aAAW,OAAOC,OAAIX,GAAE,qBAAmBW,KAAG,UAAKO,OAAIlB,GAAE,eAAa,QAAI,SAAKA,GAAE,gBAAcM,OAAIL,GAAE,IAAEK,GAAE,aAAW,gCAAgC,KAAKA,GAAE,SAAS,KAAGA,GAAE,YAAW,EAAEO,EAAC,MAAIb,GAAE,SAAO,EAAC,GAAGE,IAAE,GAAGW,GAAC,IAAGb,GAAE,kBAAiBC,GAAE,GAAED,GAAE,mBAAkB,CAAAN,OAAG;AAAC,cAAK,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE;AAAE,iBAAQC,MAAKN,IAAE;AAAC,gBAAMA,KAAEC,GAAEK,EAAC,GAAEC,KAAEP,GAAE,YAAU,CAAC,GAAEQ,KAAE,EAAED,EAAC,KAAG,EAAEA,EAAC,KAAG,CAAC;AAAE,UAAAL,GAAEI,EAAC,IAAE,CAAC,GAAEH,GAAEG,EAAC,IAAE,CAAC,GAAEF,GAAEE,EAAC,IAAE,CAAC,GAAEN,GAAE,aAAWK,GAAE,KAAKC,EAAC,GAAEH,GAAEG,EAAC,IAAEE,KAAG,EAAE,GAAG,GAAGF,EAAC,IAAE,CAAC;AAAE,mBAAQN,MAAKQ,IAAE;AAAC,kBAAMP,KAAEM,GAAEP,EAAC;AAAE,YAAAC,GAAE,KAAG,OAAGC,GAAEI,EAAC,EAAEN,EAAC,IAAEC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAGU,EAAC,IAAG,MAAI;AAAC,YAAG,CAAC,EAAE,EAAE;AAAiB;AAAO,cAAMX,KAAE,EAAE,GAAEC,KAAE,EAAE,UAAS,YAAU,IAAE,GAAG;AAAE,mBAAUC,MAAKD,IAAE;AAAC,cAAIA,KAAE,EAAEC,IAAE,CAAC,GAAEC,KAAED,GAAE,QAAQ,WAAS,IAAGE,KAAE;AAAG,cAAGH,MAAG,QAAMA,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,MAAM,CAAC,GAAEG,KAAE,OAAI,QAAMD,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,MAAM,CAAC,GAAEC,KAAE,OAAI,EAAEJ,GAAE,GAAEC,EAAC,MAAID,GAAE,GAAG,KAAK,EAAC,IAAGE,IAAE,IAAG,OAAG,IAAGE,IAAE,IAAGH,IAAE,IAAGE,GAAC,CAAC,GAAEA,KAAG;AAAC,kBAAMD,KAAEF,GAAE,EAAEC,EAAC;AAAE,YAAAC,GAAEC,EAAC,MAAID,GAAEC,EAAC,IAAE,EAAC,IAAG,MAAE;AAAA,UAAE;AAAA,QAAC;AAAA,MAAC,GAAG,GAAE,IAAI,MAAI;AAAC,cAAMH,KAAE,EAAE,EAAE,EAAE,SAAS;AAAW,YAAGA,IAAE;AAAC,gBAAMC,KAAE,EAAC,SAAQ,UAAU,UAAS,UAAS,SAAS,gBAAgB,KAAI,GAAEC,KAAE,GAAGD,GAAED,EAAC,CAAC;AAAE,cAAGE;AAAE,mBAAOA;AAAA,QAAC;AAAC,eAAO,GAAG;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAGF,EAAC,GAAEE,GAAE;AAAE;AAAO,KAAC,MAAI;AAAC,YAAMF,KAAE,EAAE,GAAEE,KAAE,EAAE,GAAEC,KAAE,GAAG,GAAE,EAAC,YAAWC,IAAE,UAASC,IAAE,WAAUC,IAAE,kBAAiBC,IAAE,sBAAqBC,IAAE,MAAKC,IAAE,UAASU,GAAC,IAAEhB,IAAEO,KAAE,EAAEN,EAAC;AAAE,MAAAJ,GAAE,IAAEG,IAAEH,GAAE,IAAEM;AAAE,YAAMK,KAAE,CAAC,CAACL,MAAG,EAAEA,EAAC;AAAE,MAAAN,GAAE,IAAEO,IAAEP,GAAE,MAAIA,GAAE,IAAE,IAAI,KAAKO,EAAC,IAAGP,GAAE,IAAEQ,IAAER,GAAE,MAAIA,GAAE,IAAE,IAAI,KAAKQ,EAAC,IAAGR,GAAE,IAAE,WAASS,KAAEA,KAAE,MAAKT,GAAE,KAAGW,MAAGQ,OAAIjB,GAAE,aAAWF,GAAE,IAAE,QAAIA,GAAE,IAAE,EAAEW,MAAGX,GAAE,KAAGA,GAAE,KAAGA,GAAE,KAAGU,KAAGR,GAAE,OAAO,mBAAiB,CAACF,GAAE,MAAIA,GAAE,KAAG,oBAAI,QAAM,QAAQ,KAAGG,GAAE,kBAAgB,IAAGH,GAAE,KAAG,GAAGE,GAAE,OAAO,IAAI,IAAGF,GAAE,IAAG,MAAI;AAAC,cAAMA,KAAE,EAAE;AAAE,mBAAUE,MAAKF,GAAE,GAAE;AAAC,gBAAMG,KAAEH,GAAE,EAAEE,EAAC;AAAE,cAAGC,GAAE,YAAUA,GAAE,SAAQ;AAAC,YAAAH,GAAE,EAAE,KAAKE,EAAC;AAAE,kBAAMC,KAAEH,GAAE,EAAEE,EAAC,KAAG,CAAC;AAAE,qBAAQE,MAAKD;AAAE,cAAAH,GAAE,EAAEE,EAAC,EAAE,KAAKE,EAAC,GAAEJ,GAAE,EAAE,SAAO,KAAGA,GAAE,EAAEE,EAAC,EAAE,KAAKE,EAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAG,GAAEJ,GAAE,IAAEE,GAAE,SAAO,MAAIF,GAAE,IAAE,CAAC,GAAGA,GAAE,CAAC,MAAIA,GAAE,IAAE,EAAC,GAAGA,GAAE,GAAE,GAAGK,GAAC,GAAEL,GAAE,IAAE,EAAC,GAAGA,GAAE,EAAC,GAAE,EAAE,CAAC,GAAGA,GAAE,GAAE,GAAGI,EAAC,CAAC;AAAA,IAAE,GAAG;AAAE,UAAMI,KAAE,GAAG;AAAE,QAAG,CAAC,MAAM,GAAG;AAAE,aAAM;AAAG,QAAG,EAAE,MAAKD,KAAE,IAAG,IAAG,EAAE,GAAE,EAAE,EAAE,KAAG,GAAGA,IAAE,EAAE,GAAE,EAAE,EAAE,sBAAoB,GAAGA,IAAE,EAAE,GAAEJ,GAAE,YAAU,CAACK,MAAG,GAAG,IAAE,GAAEA;AAAE,aAAO,GAAG,GAAE,GAAGJ,GAAE,EAAE;AAAE,IAAAD,GAAE,SAAO,KAAG,GAAGD,GAAE,CAAC;AAAA,EAAC;AAAC,MAAIK;AAAC;AAAthH,IAAwhH,KAAG,CAAAP,OAAG;AAAC,QAAK,EAAC,IAAGC,IAAE,IAAGI,GAAC,IAAE,EAAE,IAAG,EAAC,MAAKC,IAAE,MAAKC,IAAE,QAAOC,IAAE,iBAAgBC,GAAC,IAAE,EAAE,EAAE;AAAO,EAAAT,OAAIS,KAAE,GAAGH,EAAC,IAAE,GAAGA,IAAEC,IAAEC,EAAC;AAAG,aAAS,EAAC,IAAGR,IAAE,IAAGC,IAAE,IAAGC,GAAC,KAAI,EAAE,EAAE;AAAE,IAAAF,GAAE,oBAAoBC,IAAEC,EAAC;AAAE,EAAAD,MAAGA,GAAE,OAAO,GAAEI,MAAGA,GAAE,UAAU,OAAO,GAAE,GAAE,CAAC;AAAE,QAAMc,KAAE,IAAI;AAAE,aAAUnB,MAAK;AAAE,MAAEA,EAAC,IAAEmB,GAAEnB,EAAC;AAAE,SAAO,SAAO;AAAE;", "names": ["e", "t", "o", "n", "a", "s", "c", "r", "i", "l", "f", "_", "u", "p", "m", "b", "v", "y", "h", "d", "C", "w", "S", "x", "M", "D", "T", "A", "N", "I", "g", "F", "P"]}