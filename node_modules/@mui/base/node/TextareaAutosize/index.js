"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {};
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _TextareaAutosize.default;
  }
});
var _TextareaAutosize = _interopRequireDefault(require("./TextareaAutosize"));
var _TextareaAutosize2 = require("./TextareaAutosize.types");
Object.keys(_TextareaAutosize2).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _TextareaAutosize2[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _TextareaAutosize2[key];
    }
  });
});